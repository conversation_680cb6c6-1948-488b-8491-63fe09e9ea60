/**
 * PM2 生态系统配置文件
 * 
 * 管理关系分析系统的所有服务进程：
 * - MCP服务器（原子工具管理）
 * - 情绪分析WebSocket服务器
 * - 智能分析WebSocket服务器
 * 
 * 日志管理配置：
 * - 日志按天分割
 * - 保留最近3天的日志
 * - 支持压缩和自动清理
 * 
 * 使用方法：
 * pm2 install pm2-logrotate         # 安装日志轮转模块
 * pm2 set pm2-logrotate:max_size 50M  # 设置最大文件大小
 * pm2 set pm2-logrotate:retain 3    # 保留3天日志
 * pm2 set pm2-logrotate:compress true  # 启用压缩
 * pm2 set pm2-logrotate:dateFormat YYYY-MM-DD_HH-mm-ss  # 设置日期格式
 * pm2 set pm2-logrotate:rotateModule true  # 轮转模块自身日志
 * pm2 set pm2-logrotate:workerInterval 30  # 30秒检查一次
 * pm2 set pm2-logrotate:rotateInterval 0 0 * * *  # 每天午夜轮转
 * 
 * pm2 start ecosystem.config.js     # 启动所有服务
 * pm2 stop all                      # 停止所有服务
 * pm2 restart all                   # 重启所有服务
 * pm2 status                        # 查看服务状态
 * pm2 logs                          # 查看所有日志
 * pm2 logs mcp-server               # 查看特定服务日志
 */

module.exports = {
  apps: [
    {
      name: 'mcp-server',
      script: 'main.py',
      interpreter: 'python',
      cwd: './',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      env: {
        NODE_ENV: 'production',
        PORT: 8080
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 8080
      },
      log_file: './logs/mcp-server.log',
      error_file: './logs/mcp-server-error.log',
      out_file: './logs/mcp-server-out.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    },
    // {
    //   name: 'emotion-analysis-server',
    //   script: 'controllers/love_analysis/emotion/server_start.py',
    //   interpreter: 'python',
    //   cwd: './',
    //   instances: 1,
    //   autorestart: true,
    //   watch: false,
    //   max_memory_restart: '300M',
    //   env: {
    //     NODE_ENV: 'production',
    //     PORT: 8765
    //   },
    //   env_development: {
    //     NODE_ENV: 'development',
    //     PORT: 8765
    //   },
    //   log_file: './logs/emotion-server.log',
    //   error_file: './logs/emotion-server-error.log',
    //   out_file: './logs/emotion-server-out.log',
    //   log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    //   merge_logs: true
    // },
    // {
    //   name: 'intelligent-analysis-server',
    //   script: 'controllers/love_analysis/intelligent/server_start.py',
    //   interpreter: 'python',
    //   cwd: './',
    //   instances: 1,
    //   autorestart: true,
    //   watch: false,
    //   max_memory_restart: '400M',
    //   env: {
    //     NODE_ENV: 'production',
    //     PORT: 8766,
    //     DB_HOST: 'localhost',
    //     DB_PORT: 5432,
    //     DB_NAME: 'relationship_analysis',
    //     DB_USER: 'postgres',
    //     QDRANT_URL: 'http://localhost:6333',
    //     EMBEDDING_MODEL: 'BAAI/bge-large-zh-v1.5'
    //   },
    //   env_development: {
    //     NODE_ENV: 'development',
    //     PORT: 8766,
    //     DB_HOST: 'localhost',
    //     DB_PORT: 5432,
    //     DB_NAME: 'relationship_analysis',
    //     DB_USER: 'postgres',
    //     QDRANT_URL: 'http://localhost:6333',
    //     EMBEDDING_MODEL: 'BAAI/bge-large-zh-v1.5'
    //   },
    //   log_file: './logs/intelligent-server.log',
    //   error_file: './logs/intelligent-server-error.log',
    //   out_file: './logs/intelligent-server-out.log',
    //   log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    //   merge_logs: true
    // },
    

    // 新增common服务
    {
      name: 'common-service',
      script: 'controllers/common/server.py',
      interpreter: 'python',
      cwd: './',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      env: {
        NODE_ENV: 'production',
        PORT: 8010
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 8010
      },
      log_file: './logs/common-server.log',
      error_file: './logs/common-server-error.log',
      out_file: './logs/common-server-out.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    },
    // 导入亲子关系相关服务
    {
      name: 'parent-child-relationship-server',
      script: 'controllers/parent_child_relationship/server.py',
      interpreter: 'python',
      cwd: './',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      env: {
        NODE_ENV: 'production',
        PORT: 8011
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 8011
      },
      log_file: './logs/parent-child-relationship-server.log',
      error_file: './logs/parent-child-relationship-server-error.log',
      out_file: './logs/parent-child-relationship-server-out.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    },
  ],




  // 部署配置（可选）
  deploy: {
    production: {
      user: 'ubuntu',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/relationship_analysis_system.git',
      path: '/var/www/relationship_analysis_system',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  },

  /**
   * PM2 日志轮转配置
   * 
   * 安装和配置命令:
   * pm2 install pm2-logrotate
   * pm2 set pm2-logrotate:max_size 50M
   * pm2 set pm2-logrotate:retain 3
   * pm2 set pm2-logrotate:compress true
   * pm2 set pm2-logrotate:dateFormat YYYY-MM-DD
   * pm2 set pm2-logrotate:rotateModule true
   * pm2 set pm2-logrotate:workerInterval 30
   * pm2 set pm2-logrotate:rotateInterval '0 0 * * *'
   */
  logrotate: {
    max_size: '50M',      // 单个日志文件最大大小
    retain: 3,            // 保留最近3天的日志
    compress: true,       // 启用日志压缩
    dateFormat: 'YYYY-MM-DD', // 日期格式
    rotateModule: true,   // 轮转模块自身日志
    workerInterval: 30,   // 30秒检查一次
    rotateInterval: '0 0 * * *'  // 每天午夜轮转 (cron格式)
  }
}; 
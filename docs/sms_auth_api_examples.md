# 短信验证码登录API使用指南

## 概述

工禧云Common公共服务提供了基于创蓝云智API的短信验证码登录功能，支持手机号注册、登录和JWT令牌管理。

## 服务地址

- **服务地址**: http://localhost:8001
- **API文档**: http://localhost:8001/docs
- **健康检查**: http://localhost:8001/health

## API接口

### 1. 发送验证码

**接口**: `POST /api/auth/send-code`

**请求参数**:
```json
{
    "phone_number": "13800138000",
    "purpose": "login"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "验证码发送成功",
    "data": {
        "phone_number": "13800138000",
        "expires_in": 300,
        "verification_id": "uuid-string"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**cURL示例**:
```bash
curl -X POST http://localhost:8001/api/auth/send-code \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "13800138000",
    "purpose": "login"
  }'
```

### 2. 验证码登录

**接口**: `POST /api/auth/verify-login`

**请求参数**:
```json
{
    "phone_number": "13800138000",
    "verification_code": "123456",
    "nickname": "我的昵称"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user": {
            "id": "phone_13800138000_1704067200",
            "phone_number": "13800138000",
            "nickname": "我的昵称",
            "user_type": "phone",
            "phone_verified": true
        },
        "tokens": {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "token_type": "Bearer",
            "expires_in": 604800
        }
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**cURL示例**:
```bash
curl -X POST http://localhost:8001/api/auth/verify-login \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "13800138000",
    "verification_code": "123456",
    "nickname": "我的昵称"
  }'
```

### 3. 获取用户信息

**接口**: `GET /api/auth/user-info`

**请求头**:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**响应示例**:
```json
{
    "success": true,
    "message": "获取用户信息成功",
    "data": {
        "user": {
            "id": "phone_13800138000_1704067200",
            "phone_number": "13800138000",
            "nickname": "我的昵称",
            "user_type": "phone",
            "status": "active",
            "phone_verified": true,
            "last_login_at": "2024-01-01T12:00:00.000Z",
            "created_at": "2024-01-01T11:00:00.000Z"
        }
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**cURL示例**:
```bash
curl -X GET http://localhost:8001/api/auth/user-info \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. 刷新令牌

**接口**: `POST /api/auth/refresh-token`

**请求参数**:
```json
{
    "refresh_token": "YOUR_REFRESH_TOKEN"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "令牌刷新成功",
    "data": {
        "access_token": "NEW_ACCESS_TOKEN",
        "token_type": "Bearer",
        "expires_in": 604800
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 前端集成示例

### JavaScript/TypeScript

```javascript
class SmsAuthService {
    constructor(baseUrl = 'http://localhost:8001') {
        this.baseUrl = baseUrl;
    }

    // 发送验证码
    async sendCode(phoneNumber) {
        const response = await fetch(`${this.baseUrl}/api/auth/send-code`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phoneNumber,
                purpose: 'login'
            })
        });
        
        return await response.json();
    }

    // 验证码登录
    async verifyLogin(phoneNumber, verificationCode, nickname) {
        const response = await fetch(`${this.baseUrl}/api/auth/verify-login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phoneNumber,
                verification_code: verificationCode,
                nickname: nickname
            })
        });
        
        return await response.json();
    }

    // 获取用户信息
    async getUserInfo(accessToken) {
        const response = await fetch(`${this.baseUrl}/api/auth/user-info`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        return await response.json();
    }

    // 刷新令牌
    async refreshToken(refreshToken) {
        const response = await fetch(`${this.baseUrl}/api/auth/refresh-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                refresh_token: refreshToken
            })
        });
        
        return await response.json();
    }
}

// 使用示例
const authService = new SmsAuthService();

async function loginWithSms() {
    try {
        // 1. 发送验证码
        const sendResult = await authService.sendCode('13800138000');
        if (!sendResult.success) {
            throw new Error(sendResult.message);
        }
        
        console.log('验证码发送成功');
        
        // 2. 用户输入验证码后进行登录
        const verificationCode = '123456'; // 用户输入的验证码
        const loginResult = await authService.verifyLogin(
            '13800138000', 
            verificationCode, 
            '我的昵称'
        );
        
        if (!loginResult.success) {
            throw new Error(loginResult.message);
        }
        
        // 3. 保存token
        localStorage.setItem('access_token', loginResult.data.tokens.access_token);
        localStorage.setItem('refresh_token', loginResult.data.tokens.refresh_token);
        
        console.log('登录成功', loginResult.data.user);
        
        // 4. 获取用户信息
        const userInfo = await authService.getUserInfo(loginResult.data.tokens.access_token);
        console.log('用户信息', userInfo.data.user);
        
    } catch (error) {
        console.error('登录失败:', error.message);
    }
}
```

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `401` - 认证失败
- `429` - 请求过于频繁
- `500` - 服务器内部错误

### 错误响应格式

```json
{
    "success": false,
    "message": "错误描述",
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 环境配置

### 1. 创蓝云智配置

需要在环境变量中设置：
```bash
export SMS_ACCOUNT="your_sms_account"
export SMS_PASSWORD="your_sms_password"
```

### 2. JWT密钥配置

```bash
export JWT_SECRET_KEY="your-super-secret-key"
```

### 3. 数据库迁移

执行数据库迁移添加短信验证码支持：
```bash
python migrate.py
```

## 部署说明

### 1. 启动服务

```bash
# 开发模式
python start_common_service.py

# 生产模式
export COMMON_RELOAD=false
python start_common_service.py
```

### 2. Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8001
CMD ["python", "start_common_service.py"]
```

## 安全注意事项

1. **生产环境必须设置自定义JWT密钥**
2. **短信发送有频率限制（60秒间隔）**
3. **验证码5分钟过期，最多尝试3次**
4. **建议使用HTTPS传输**
5. **设置正确的CORS域名限制** 
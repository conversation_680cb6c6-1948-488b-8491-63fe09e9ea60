# 情绪分析服务前端调用指南

## 📋 概述

情绪分析服务提供两种类型的接口：
- **HTTP API** (端口 8000): 用户管理、服务状态查询
- **WebSocket** (端口 8765): 实时情绪分析和情绪支持

## 🚀 快速开始

### 1. 启动服务器
```bash
python controllers/love_analysis/emotion/server_start.py
```

### 2. 服务地址
- HTTP API: `http://localhost:8000`
- WebSocket: `ws://localhost:8765`
- API文档: `http://localhost:8000/docs`

## 📊 HTTP API 接口

### 基础路径
- 根路径: `/`
- 认证API: `/emotion/auth/`
- 健康检查: `/health`

### 1. 获取服务状态

**GET** `/`

响应示例：
```json
{
  "service": "emotion_analysis",
  "message": "情绪分析服务运行中",
  "services": {
    "http_api": "http://localhost:8000",
    "websocket": "ws://localhost:8765"
  },
  "docs": "/docs"
}
```

### 2. 健康检查

**GET** `/health`

响应示例：
```json
{
  "status": "healthy",
  "services": {
    "http_api": "running",
    "websocket": "running",
    "database": "connected"
  }
}
```

### 3. 注册访客用户

**POST** `/emotion/auth/register/guest`

请求体：
```json
{
  "user_id": "user_12345",
  "nickname": "访客用户"
}
```

响应示例：
```json
{
  "user_id": "user_12345",
  "nickname": "访客用户",
  "user_type": "guest",
  "status": "active",
  "created_at": "2024-01-01T12:00:00",
  "last_login_at": "2024-01-01T12:00:00"
}
```

### 4. 验证用户

**POST** `/emotion/auth/validate`

请求体：
```json
{
  "user_id": "user_12345"
}
```

响应示例：
```json
{
  "user_id": "user_12345",
  "nickname": "访客用户",
  "user_type": "guest",
  "status": "active",
  "created_at": "2024-01-01T12:00:00",
  "last_login_at": "2024-01-01T12:00:00"
}
```

### 5. 获取用户信息

**GET** `/emotion/auth/user/{user_id}`

响应示例：
```json
{
  "user_id": "user_12345",
  "nickname": "访客用户",
  "user_type": "guest",
  "status": "active",
  "created_at": "2024-01-01T12:00:00",
  "last_login_at": "2024-01-01T12:00:00"
}
```

### 6. 获取WebSocket连接信息

**GET** `/emotion/ws-info`

响应示例：
```json
{
  "websocket_url": "ws://localhost:8765",
  "protocol": "json",
  "auth_required": true,
  "auth_message_format": {
    "type": "auth",
    "user_id": "your_user_id_here"
  },
  "supported_message_types": [
    "analyze_text",
    "generate_support",
    "ping"
  ]
}
```

### 7. 存储用户输入并创建关联

**POST** `/emotion/auth/store-input`

用于存储用户的文本输入，并与多个MCP工具调用和情绪分析结果建立关联。

请求体：
```json
{
  "user_id": "user_12345",
  "user_text": "今天心情很糟糕，他又让我失望了...",
  "mcp_call_ids": [
    "ac3281d5-e5c4-475e-bd0a-c57f6c3f8dff",
    "bc4392e6-f6d5-486f-ce1b-d68f7c4e9f00"
  ],
  "emotion_analysis_ids": [
    "12345678-1234-1234-1234-123456789012",
    "23456789-2345-2345-2345-234567890123"
  ],
  "tags": ["relationship", "disappointment"]
}
```

参数说明：
- `user_id`: 用户ID（必填）
- `user_text`: 用户输入的文本内容（必填）
- `mcp_call_ids`: MCP工具调用ID列表（可选）
- `emotion_analysis_ids`: 情绪分析ID列表（可选）
- `tags`: 标签列表（可选）

**注意：** `mcp_call_ids` 和 `emotion_analysis_ids` 都是可选的，可以只存储纯文本内容而不关联任何分析结果。

响应示例：
```json
{
  "user_record_id": "98765432-9876-9876-9876-987654321098",
  "user_id": "user_12345",
  "content": "今天心情很糟糕，他又让我失望了...",
  "content_type": "text",
  "tags": ["relationship", "disappointment"],
  "associations": [
    {
      "id": "assoc-1",
      "type": "mcp_call",
      "mcp_tool_call_id": "ac3281d5-e5c4-475e-bd0a-c57f6c3f8dff",
      "emotion_analysis_id": null,
      "created_at": "2025-06-06T14:30:29.000Z"
    },
    {
      "id": "assoc-2",
      "type": "mcp_call",
      "mcp_tool_call_id": "bc4392e6-f6d5-486f-ce1b-d68f7c4e9f00",
      "emotion_analysis_id": null,
      "created_at": "2025-06-06T14:30:29.000Z"
    },
    {
      "id": "assoc-3",
      "type": "emotion_analysis",
      "mcp_tool_call_id": null,
      "emotion_analysis_id": "12345678-1234-1234-1234-123456789012",
      "created_at": "2025-06-06T14:30:30.000Z"
    },
    {
      "id": "assoc-4",
      "type": "emotion_analysis",
      "mcp_tool_call_id": null,
      "emotion_analysis_id": "23456789-2345-2345-2345-234567890123",
      "created_at": "2025-06-06T14:30:30.000Z"
    }
  ],
  "created_at": "2025-06-06T14:30:29.000Z"
}
```

### 8. 分页查询用户记录

**GET** `/emotion/auth/user-records`

按时间线排序查询用户记录，支持分页和多种过滤条件。

查询参数：
- `user_id`: 用户ID（可选，不传则查询所有用户记录）
- `page`: 页码，从1开始（默认：1）
- `page_size`: 每页大小，1-100（默认：10）
- `include_associations`: 是否包含关联信息（默认：true）
- `content_type`: 内容类型过滤（可选）

请求示例：
```bash
# 查询指定用户的记录
GET /emotion/auth/user-records?user_id=user_12345&page=1&page_size=10&include_associations=true

# 查询所有用户记录（不包含关联信息）
GET /emotion/auth/user-records?page=1&page_size=20&include_associations=false

# 按内容类型过滤
GET /emotion/auth/user-records?content_type=text&page=2&page_size=5
```

响应示例：
```json
{
  "records": [
    {
      "user_record_id": "98765432-9876-9876-9876-987654321098",
      "user_id": "user_12345",
      "content": "今天心情很糟糕，他又让我失望了...",
      "content_type": "text",
      "tags": ["relationship", "disappointment"],
      "associations": [
        {
          "id": "assoc-1",
          "type": "mcp_call",
          "mcp_tool_call_id": "ac3281d5-e5c4-475e-bd0a-c57f6c3f8dff",
          "emotion_analysis_id": null,
          "created_at": "2025-06-06T14:30:29.000Z"
        },
        {
          "id": "assoc-2",
          "type": "emotion_analysis",
          "mcp_tool_call_id": null,
          "emotion_analysis_id": "12345678-1234-1234-1234-123456789012",
          "created_at": "2025-06-06T14:30:30.000Z"
        }
      ],
      "created_at": "2025-06-06T14:30:29.000Z",
      "updated_at": "2025-06-06T14:30:29.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_pages": 3,
    "has_next": true,
    "has_prev": false,
    "next_page": 2,
    "prev_page": null
  },
  "total_count": 25
}
```

## 🌐 WebSocket 接口

### 连接流程

1. **建立连接**: 连接到 `ws://localhost:8765`
2. **身份认证**: 发送认证消息
3. **开始通信**: 发送分析或支持请求

### 消息格式

所有消息都是JSON格式，包含 `type` 字段标识消息类型。

### 1. 身份认证 (必须首先发送)

发送消息：
```json
{
  "type": "auth",
  "user_id": "user_12345"
}
```

成功响应：
```json
{
  "type": "connection_established",
  "session_id": "conn_1736144234_1",
  "user_id": "user_12345",
  "user_info": {
    "user_id": "user_12345",
    "nickname": "访客用户",
    "user_type": "guest",
    "status": "active"
  },
  "message": "情绪分析与支持服务已就绪",
  "services": ["emotion_analysis", "emotional_support"],
  "timestamp": 1736144234.567
}
```

失败响应：
```json
{
  "type": "auth_error",
  "error": "用户ID验证失败: user_12345",
  "timestamp": 1736144234.567
}
```

### 2. 情绪分析

发送消息：
```json
{
  "type": "analyze_text",
  "text": "我今天心情很好，感觉特别开心！"
}
```

处理开始响应：
```json
{
  "type": "analysis_started",
  "message": "开始处理情绪分析...",
  "timestamp": 1736144234.567
}
```

分析结果响应：
```json
{
  "type": "analysis_result",
  "data": {
    "analysis": {
      "segments": [
        {
          "text": "我今天心情很好，感觉特别开心！",
          "emotions": {
            "joy": {
              "score": 0.85,
              "confidence_interval": [0.75, 0.95],
              "text_evidence": ["心情很好", "特别开心"]
            },
            "sadness": {
              "score": 0.1,
              "confidence_interval": [0.05, 0.15],
              "text_evidence": []
            }
          }
        }
      ]
    },
    "intensity": {
      "overall_intensity": "high",
      "emotion_analysis": {
        "joy": [
          {
            "segment_index": 0,
            "score": 0.85,
            "intensity_level": "high",
            "confidence_level": "high",
            "confidence_width": 0.2,
            "is_outlier": false
          }
        ]
      },
      "outlier_emotions": [],
      "risk_level": "low",
      "statistics": {
        "total_emotions_analyzed": 2,
        "high_intensity_count": 1,
        "extreme_intensity_count": 0,
        "outlier_count": 0
      },
      "summary": "检测到 1 个高强度情绪，整体风险等级为低"
    }
  },
  "session_info": {
    "session_id": "user_12345",
    "analysis_record_id": 123456,
    "processing_time_ms": 1500
  },
  "original_text": "我今天心情很好，感觉特别开心！",
  "timestamp": 1736144236.123,
  "status": "success"
}
```

### 3. 情绪支持

发送消息：
```json
{
  "type": "generate_support",
  "context": "我今天工作压力很大，感觉很焦虑",
  "data": [
    {
      "text": "我今天工作压力很大，感觉很焦虑",
      "emotions": {
        "anxiety": {
          "score": 0.8,
          "confidence_interval": [0.7, 0.9]
        }
      }
    }
  ],
  "message": "需要一些安慰和建议"
}
```

处理开始响应：
```json
{
  "type": "support_started",
  "message": "开始生成情绪支持回复...",
  "timestamp": 1736144234.567
}
```

支持结果响应：
```json
{
  "type": "support_result",
  "data": {
    "comfort_response": "我理解你现在的感受，工作压力确实会让人感到焦虑。首先，请记住这种感觉是正常的，许多人都会经历类似的情况。建议你可以尝试深呼吸放松，或者短暂离开工作环境休息一下。如果可能的话，可以将大任务分解成小步骤，一步一步来完成。记住，你有能力处理这些挑战，给自己一些耐心和理解。",
    "detected_emotion": "anxiety",
    "emotion_data_count": 1
  },
  "session_info": {
    "session_id": "user_12345",
    "response_record_id": 789012,
    "analysis_record_id": 123456,
    "processing_time_ms": 2000
  },
  "original_context": "我今天工作压力很大，感觉很焦虑",
  "timestamp": 1736144238.567,
  "status": "success"
}
```

### 4. 心跳检测

发送消息：
```json
{
  "type": "ping"
}
```

响应：
```json
{
  "type": "pong",
  "timestamp": 1736144234.567
}
```

## 🔧 前端集成示例

### JavaScript/TypeScript 示例

```javascript
class EmotionAnalysisClient {
  constructor(httpBaseUrl = 'http://localhost:8000', wsUrl = 'ws://localhost:8765') {
    this.httpBaseUrl = httpBaseUrl;
    this.wsUrl = wsUrl;
    this.ws = null;
    this.userId = null;
    this.sessionId = null;
  }

  // 1. 注册访客用户
  async registerGuestUser(nickname = '访客用户') {
    const userId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const response = await fetch(`${this.httpBaseUrl}/emotion/auth/register/guest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        nickname: nickname
      })
    });

    if (!response.ok) {
      throw new Error(`注册失败: ${response.status}`);
    }

    const userData = await response.json();
    this.userId = userData.user_id;
    return userData;
  }

  // 2. 验证用户
  async validateUser(userId) {
    const response = await fetch(`${this.httpBaseUrl}/emotion/auth/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId
      })
    });

    if (!response.ok) {
      throw new Error(`验证失败: ${response.status}`);
    }

    return await response.json();
  }

  // 3. 连接WebSocket
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket连接已建立');
        
        // 发送认证消息
        this.ws.send(JSON.stringify({
          type: 'auth',
          user_id: this.userId
        }));
      };

      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        
        if (message.type === 'connection_established') {
          this.sessionId = message.session_id;
          console.log('认证成功:', message);
          resolve(message);
        } else if (message.type === 'auth_error') {
          console.error('认证失败:', message);
          reject(new Error(message.error));
        } else {
          this.handleMessage(message);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
        reject(error);
      };

      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭');
      };
    });
  }

  // 4. 发送情绪分析请求
  analyzeText(text) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket未连接');
    }

    this.ws.send(JSON.stringify({
      type: 'analyze_text',
      text: text
    }));
  }

  // 5. 发送情绪支持请求
  generateSupport(context, emotionData, message = '') {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket未连接');
    }

    this.ws.send(JSON.stringify({
      type: 'generate_support',
      context: context,
      data: emotionData,
      message: message
    }));
  }

  // 6. 处理收到的消息
  handleMessage(message) {
    switch (message.type) {
      case 'analysis_started':
        console.log('分析开始:', message.message);
        this.onAnalysisStarted?.(message);
        break;

      case 'analysis_result':
        console.log('分析结果:', message.data);
        this.onAnalysisResult?.(message);
        break;

      case 'support_started':
        console.log('支持生成开始:', message.message);
        this.onSupportStarted?.(message);
        break;

      case 'support_result':
        console.log('支持结果:', message.data);
        this.onSupportResult?.(message);
        break;

      case 'pong':
        console.log('心跳响应');
        break;

      case 'validation_error':
      case 'analysis_error':
      case 'support_error':
        console.error('错误:', message.error);
        this.onError?.(message);
        break;

      default:
        console.log('未知消息类型:', message);
    }
  }

  // 7. 心跳检测
  ping() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type: 'ping' }));
    }
  }

  // 8. 关闭连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  // 9. 存储用户输入并创建关联
  async storeUserInput(userText, mcpCallIds = [], emotionAnalysisIds = [], tags = []) {
    const response = await fetch(`${this.httpBaseUrl}/emotion/auth/store-input`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: this.userId,
        user_text: userText,
        mcp_call_ids: mcpCallIds,
        emotion_analysis_ids: emotionAnalysisIds,
        tags: tags
      })
    });

    if (!response.ok) {
      throw new Error(`存储失败: ${response.status}`);
    }

    return await response.json();
  }

  // 10. 分页查询用户记录
  async queryUserRecords(options = {}) {
    const {
      userId = null,
      page = 1,
      pageSize = 10,
      includeAssociations = true,
      contentType = null
    } = options;

    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
      include_associations: includeAssociations.toString()
    });

    if (userId) params.append('user_id', userId);
    if (contentType) params.append('content_type', contentType);

    const response = await fetch(`${this.httpBaseUrl}/emotion/auth/user-records?${params}`);

    if (!response.ok) {
      throw new Error(`查询失败: ${response.status}`);
    }

    return await response.json();
  }

  // 事件回调（可选）
  onAnalysisStarted = null;
  onAnalysisResult = null;
  onSupportStarted = null;
  onSupportResult = null;
  onError = null;
}
```

### 使用示例

```javascript
// 1. 初始化客户端
const client = new EmotionAnalysisClient();

// 2. 注册用户并连接
async function initializeClient() {
  try {
    // 注册访客用户
    const userData = await client.registerGuestUser('我的昵称');
    console.log('用户注册成功:', userData);

    // 连接WebSocket
    await client.connectWebSocket();
    console.log('WebSocket连接成功');

    // 设置事件回调
    client.onAnalysisResult = (message) => {
      console.log('收到分析结果:', message.data);
      // 更新UI显示分析结果
      updateAnalysisUI(message.data);
    };

    client.onSupportResult = (message) => {
      console.log('收到支持回复:', message.data.comfort_response);
      // 更新UI显示支持回复
      updateSupportUI(message.data);
    };

    client.onError = (message) => {
      console.error('发生错误:', message.error);
      // 显示错误信息
      showError(message.error);
    };

  } catch (error) {
    console.error('初始化失败:', error);
  }
}

// 3. 分析文本
function analyzeUserText() {
  const text = document.getElementById('textInput').value;
  if (text.trim()) {
    client.analyzeText(text);
  }
}

// 4. 生成情绪支持
function generateEmotionalSupport(analysisResult) {
  const context = analysisResult.original_text;
  const emotionData = analysisResult.data.analysis.segments;
  
  client.generateSupport(context, emotionData, '需要情绪支持');
}

// 5. 启动定期心跳
setInterval(() => {
  client.ping();
}, 30000); // 每30秒发送一次心跳

// 6. 页面关闭时断开连接
window.addEventListener('beforeunload', () => {
  client.disconnect();
});

// 7. 存储用户输入示例
async function storeUserInputExample() {
  try {
    const userText = "今天心情很糟糕，感觉被忽视了";
    const mcpCallIds = ["ac3281d5-e5c4-475e-bd0a-c57f6c3f8dff"];
    const emotionAnalysisIds = ["12345678-1234-1234-1234-123456789012"];
    const tags = ["relationship", "sadness"];

    const result = await client.storeUserInput(userText, mcpCallIds, emotionAnalysisIds, tags);
    console.log('用户输入存储成功:', result);
    
    // 显示存储结果
    updateStorageResultUI(result);
  } catch (error) {
    console.error('存储失败:', error);
  }
}

// 8. 查询用户记录示例
async function queryUserRecordsExample() {
  try {
    // 查询当前用户的记录（第1页，每页5条，包含关联信息）
    const currentUserRecords = await client.queryUserRecords({
      userId: client.userId,
      page: 1,
      pageSize: 5,
      includeAssociations: true
    });
    console.log('当前用户记录:', currentUserRecords);

    // 查询所有用户记录（不包含关联信息，用于管理员视图）
    const allRecords = await client.queryUserRecords({
      page: 1,
      pageSize: 20,
      includeAssociations: false
    });
    console.log('所有用户记录:', allRecords);

    // 显示查询结果
    updateRecordsListUI(currentUserRecords);
  } catch (error) {
    console.error('查询失败:', error);
  }
}

// 9. 完整的工作流示例
async function completeWorkflowExample() {
  try {
    const userText = "我和他的关系让我很困扰";
    
    // 1. 先进行情绪分析
    client.analyzeText(userText);
    
    // 2. 等待分析结果并存储
    client.onAnalysisResult = async (analysisMessage) => {
      try {
        const analysisId = analysisMessage.session_info.emotion_analysis_id;
        const mcpCallId = analysisMessage.session_info.mcp_call_id;
        
        // 存储用户输入和分析结果
        if (analysisId && mcpCallId) {
          const storeResult = await client.storeUserInput(
            userText,
            [mcpCallId],
            [analysisId],
            ["relationship", "analysis"]
          );
          console.log('存储完成:', storeResult);
          
          // 3. 生成情绪支持
          const emotionData = analysisMessage.data.analysis.segments;
          client.generateSupport(userText, emotionData, "需要一些安慰");
        }
      } catch (error) {
        console.error('存储过程出错:', error);
      }
    };
    
    // 4. 处理支持结果
    client.onSupportResult = async (supportMessage) => {
      try {
        const supportId = supportMessage.session_info.emotion_support_id;
        const mcpCallId = supportMessage.session_info.mcp_call_id;
        
        // 可以选择也存储支持生成的记录
        console.log('收到支持回复:', supportMessage.data.comfort_response);
      } catch (error) {
        console.error('支持处理出错:', error);
      }
    };
    
  } catch (error) {
    console.error('完整工作流执行失败:', error);
  }
}

// 10. UI更新辅助函数
function updateStorageResultUI(result) {
  // 更新界面显示存储结果
  const resultDiv = document.getElementById('storageResult');
  if (resultDiv) {
    resultDiv.innerHTML = `
      <h3>存储结果</h3>
      <p><strong>记录ID:</strong> ${result.user_record_id}</p>
      <p><strong>内容:</strong> ${result.content}</p>
      <p><strong>关联数量:</strong> ${result.associations.length}</p>
      <p><strong>创建时间:</strong> ${result.created_at}</p>
    `;
  }
}

function updateRecordsListUI(queryResult) {
  // 更新界面显示查询结果
  const listDiv = document.getElementById('recordsList');
  if (listDiv) {
    const recordsHtml = queryResult.records.map(record => `
      <div class="record-item">
        <p><strong>内容:</strong> ${record.content}</p>
        <p><strong>时间:</strong> ${record.created_at}</p>
        <p><strong>关联:</strong> ${record.associations ? record.associations.length : 0} 个</p>
      </div>
    `).join('');
    
    listDiv.innerHTML = `
      <h3>用户记录 (第${queryResult.pagination.page}页，共${queryResult.pagination.total_pages}页)</h3>
      <div class="records-container">${recordsHtml}</div>
      <div class="pagination">
        <p>总计: ${queryResult.total_count} 条记录</p>
        ${queryResult.pagination.has_prev ? '<button onclick="loadPrevPage()">上一页</button>' : ''}
        ${queryResult.pagination.has_next ? '<button onclick="loadNextPage()">下一页</button>' : ''}
      </div>
    `;
  }
}
```

## ⚠️ 错误处理

### HTTP API 错误

- **400**: 请求参数错误
- **404**: 用户不存在
- **403**: 用户状态异常
- **500**: 服务器内部错误

### WebSocket 错误

常见错误类型：
- `auth_error`: 认证失败
- `validation_error`: 参数验证失败
- `analysis_error`: 分析过程错误
- `support_error`: 支持生成错误
- `mcp_server_error`: MCP服务器错误
- `connection_error`: 连接错误

## 🎯 最佳实践

1. **用户管理**:
   - 前端生成唯一的用户ID
   - 先注册用户，再建立WebSocket连接
   - 保存用户ID用于后续连接

2. **连接管理**:
   - 实现自动重连机制
   - 定期发送心跳检测
   - 监听连接状态变化

3. **错误处理**:
   - 为每种错误类型提供适当的用户反馈
   - 实现重试机制
   - 记录错误日志

4. **性能优化**:
   - 避免频繁发送分析请求
   - 合理设置心跳间隔
   - 及时关闭不需要的连接

5. **用户体验**:
   - 显示处理状态（开始/进行中/完成）
   - 提供加载指示器
   - 实现渐进式结果显示

## 📞 技术支持

如有问题，请检查：
1. 服务器是否正常启动
2. 网络连接是否正常
3. 用户ID是否有效
4. 请求参数是否正确

更多详细信息请查看API文档：`http://localhost:8000/docs` 
# 关系分析系统API快速参考

## 🚀 服务地址

| 服务 | 地址 | 端口 |
|------|------|------|
| 情绪分析 | `ws://localhost:8765` | 8765 |
| 智能分析 | `ws://localhost:8766` | 8766 |
| MCP服务器 | `ws://localhost:8080` | 8080 |

## 💭 情绪分析服务

### 文本分析
```javascript
// 请求
ws.send(JSON.stringify({
    "type": "analyze_text",
    "text": "今天心情不好"
}));

// 响应
{
    "type": "analysis_result",
    "data": {
        "overall_intensity": "high",
        "emotion_analysis": { /* 详细情绪数据 */ },
        "risk_level": "medium",
        "summary": "分析摘要"
    }
}
```

### 情绪支持
```javascript
// 请求
ws.send(JSON.stringify({
    "type": "generate_support",
    "context": "今天心情不好",
    "data": [{"emotion": "sadness", "score": 0.8}],
    "message": "历史背景"
}));

// 响应
{
    "type": "support_result",
    "data": {
        "comfort_response": "安慰回复文本",
        "detected_emotion": "sadness"
    }
}
```

### 心跳检测
```javascript
// 请求
ws.send(JSON.stringify({"type": "ping"}));

// 响应
{"type": "pong", "timestamp": 1701234567.123}
```

## 🧠 智能分析服务

### 存储文本
```javascript
ws.send(JSON.stringify({
    "type": "store_relationship_text",
    "text": "今天一起看电影了",
    "user_id": "user_123",
    "metadata": {"date": "2024-01-15"}
}));
```

### 综合分析
```javascript
ws.send(JSON.stringify({
    "type": "request_comprehensive_analysis",
    "user_id": "user_123",
    "analysis_type": "relationship_status",
    "time_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-15"
    }
}));
```

### 获取历史
```javascript
ws.send(JSON.stringify({
    "type": "get_relationship_history",
    "user_id": "user_123",
    "limit": 20,
    "offset": 0
}));
```

### 搜索记录
```javascript
ws.send(JSON.stringify({
    "type": "search_relationship_records",
    "user_id": "user_123",
    "query": "电影 约会",
    "search_type": "semantic",
    "limit": 10
}));
```

## ⚡ 快速开始

```javascript
// 1. 建立连接
const emotionWs = new WebSocket('ws://localhost:8765');
const intelligentWs = new WebSocket('ws://localhost:8766');

// 2. 监听消息
emotionWs.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('情绪分析响应:', data);
};

intelligentWs.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('智能分析响应:', data);
};

// 3. 发送分析请求
emotionWs.onopen = () => {
    emotionWs.send(JSON.stringify({
        type: 'analyze_text',
        text: '今天我很开心'
    }));
};
```

## 📊 强度等级

| 等级 | 范围 | 描述 |
|------|------|------|
| `minimal` | 0.0-0.3 | 极低强度 |
| `low` | 0.3-0.5 | 低强度 |
| `moderate` | 0.5-0.7 | 中等强度 |
| `high` | 0.7-0.85 | 高强度 |
| `extreme` | 0.85-1.0 | 极高强度 |

## 🚨 风险等级

- `low` - 低风险：正常情绪状态
- `medium` - 中风险：需要关注
- `high` - 高风险：需要及时干预

## ❌ 常见错误

| 错误类型 | 描述 | 解决方案 |
|----------|------|----------|
| `validation_error` | 参数验证失败 | 检查必填参数 |
| `analysis_error` | 分析过程错误 | 重试或联系支持 |
| `json_parse_error` | JSON格式错误 | 检查消息格式 |
| `unknown_message_type` | 未知消息类型 | 使用正确的type值 |

## 🔗 示例HTML

```html
<script>
const ws = new WebSocket('ws://localhost:8765');
ws.onopen = () => console.log('连接成功');
ws.onmessage = (e) => console.log(JSON.parse(e.data));
ws.send(JSON.stringify({type: 'analyze_text', text: '测试文本'}));
</script>
```

---
*更多详细信息请参考 [完整API文档](FRONTEND_API_DOCUMENTATION.md)* 
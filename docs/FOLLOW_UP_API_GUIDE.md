# 追问分析 API 使用指南

## 概述

追问分析功能基于 S-A-R-F 深度洞察模型，智能判断用户提供的育儿内容是否需要进一步追问，帮助收集完整的育儿故事信息。

## S-A-R-F 深度洞察模型

追问分析基于四个核心要素：

- **情境 (Scene)**: 事件发生的时间、地点、背景
- **行为 (Action)**: 孩子的具体行为和家长的应对行为
- **结果 (Result)**: 家长应对后，孩子的直接反应和事件的最终走向
- **感受 (Feeling)**: 家长或孩子在过程中的关键情绪

## API 接口

### 追问分析接口

**接口地址**: `POST /analysis/follow-up`

**请求参数**:

```json
{
  "content": "用户输入的育儿相关内容"
}
```

**响应格式**:

```json
{
  "success": true,
  "message": "追问分析完成",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "tool_call_id": "uuid",
    "tool_name": "analyze_follow_up",
    "user_id": "user_id",
    "input_content": "原始输入内容",
    "content_length": 50,
    "follow_up_result": {
      "type": "question",
      "content": "听起来真头疼。当他'非要抢秋千'的时候，您当时是怎么做的呢？",
      "reasoning": "用户描述了情境和孩子的行为，但缺少家长的应对(Action)和事件结果(Result)。提取关键词'抢秋千'进行针对性追问。",
      "need_follow_up": true,
      "analysis_timestamp": "2024-01-15T10:30:00Z",
      "tool_version": "1.0.0"
    },
    "status": "SUCCESS",
    "created_at": "2024-01-15T10:30:00Z",
    "metadata": {
      "s_a_r_f_completeness": {
        "scene": true,
        "action": true,
        "result": false,
        "feeling": false,
        "overall_score": 50
      },
      "key_phrases": ["孩子", "不听话"],
      "content_category": "情绪行为"
    }
  }
}
```

## 使用示例

### 1. 信息不完整的情况（需要追问）

**请求**:

```bash
curl -X POST "http://localhost:8000/analysis/follow-up" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "我家孩子今天在公园不听话，怎么说都不听。"
  }'
```

**响应**:

```json
{
  "success": true,
  "message": "追问分析完成",
  "data": {
    "follow_up_result": {
      "type": "question",
      "content": "听起来真头疼。当他'不听话'的时候，您当时是怎么做的呢？",
      "need_follow_up": true
    }
  }
}
```

### 2. 信息完整的情况（不需要追问）

**请求**:

```bash
curl -X POST "http://localhost:8000/analysis/follow-up" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "晚饭后我们一起画画，他非要把太阳画成绿色的。我告诉他太阳是黄色的，但他坚持说他心中的太阳就是绿色的。我最后没再纠正他，结果他开心地画了一整张绿色的太阳，还说这是最有创意的画。"
  }'
```

**响应**:

```json
{
  "success": true,
  "message": "追问分析完成",
  "data": {
    "follow_up_result": {
      "type": "acknowledgement",
      "content": "谢谢您的分享，这个充满想象力的重要时刻我记下来了。",
      "need_follow_up": false
    }
  }
}
```

## 响应字段说明

### follow_up_result 字段详解

| 字段                 | 类型    | 说明                                                  |
| -------------------- | ------- | ----------------------------------------------------- |
| `type`               | string  | `"question"` 或 `"acknowledgement"`，表示是否需要追问 |
| `content`            | string  | 给用户的回复内容（追问或确认）                        |
| `reasoning`          | string  | AI 的分析推理过程                                     |
| `need_follow_up`     | boolean | 是否需要追问                                          |
| `analysis_timestamp` | string  | 分析时间戳                                            |
| `tool_version`       | string  | 工具版本号                                            |

### metadata 字段详解

| 字段                   | 类型   | 说明                                                           |
| ---------------------- | ------ | -------------------------------------------------------------- |
| `s_a_r_f_completeness` | object | S-A-R-F 四要素完整性评估                                       |
| `key_phrases`          | array  | 从内容中提取的关键短语                                         |
| `content_category`     | string | 内容分类（学习教育、生活习惯、游戏娱乐、情绪行为、亲子互动等） |

### S-A-R-F 完整性评估

```json
{
  "scene": true, // 是否包含情境信息
  "action": false, // 是否包含行为信息
  "result": false, // 是否包含结果信息
  "feeling": true, // 是否包含感受信息
  "overall_score": 50 // 总体完整性评分（0-100）
}
```

## 集成建议

### 前端集成

```javascript
// 追问分析函数
async function analyzeForFollowUp(content) {
  try {
    const response = await fetch("/analysis/follow-up", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content }),
    });

    const result = await response.json();

    if (result.success) {
      const followUp = result.data.follow_up_result;

      if (followUp.need_follow_up) {
        // 显示追问内容
        showFollowUpQuestion(followUp.content);
      } else {
        // 显示确认信息，可以继续分析
        showAcknowledgement(followUp.content);
        proceedToAnalysis(content);
      }
    }
  } catch (error) {
    console.error("追问分析失败:", error);
  }
}

// 显示追问问题
function showFollowUpQuestion(question) {
  const questionDiv = document.createElement("div");
  questionDiv.className = "follow-up-question";
  questionDiv.innerHTML = `
    <div class="ai-avatar">启育</div>
    <div class="question-content">${question}</div>
  `;
  document.getElementById("chat-container").appendChild(questionDiv);
}

// 显示确认信息
function showAcknowledgement(message) {
  console.log("信息完整，可以进行分析:", message);
}
```

### 工作流程建议

1. **用户输入内容** → 调用追问分析 API
2. **如果需要追问** → 显示追问问题，等待用户补充
3. **如果信息充足** → 直接进行详细分析
4. **用户补充信息** → 可选择性地再次进行追问分析或直接分析

## 错误处理

### 常见错误情况

1. **输入内容过短**

```json
{
  "success": false,
  "message": "请提供有效的内容进行分析",
  "data": null
}
```

2. **分析失败**

```json
{
  "success": false,
  "message": "追问分析失败: 具体错误信息",
  "data": null
}
```

### 降级处理

当追问分析出现技术问题时，系统会返回备用的追问内容：

```json
{
  "follow_up_result": {
    "type": "question",
    "content": "能跟我详细分享一下您遇到的具体情况吗？",
    "reasoning": "分析过程中遇到技术问题，使用备用开放式问题。",
    "error": "分析过程中遇到技术问题"
  }
}
```

## 最佳实践

### 1. 内容质量建议

为了获得最佳的追问分析效果，建议用户提供的内容包含：

- **具体的时间和场景**："今天下午在客厅"
- **明确的行为描述**："孩子把玩具扔在地上"
- **情绪和感受**："我很生气"、"孩子很委屈"

### 2. 追问策略

- **渐进式追问**：一次只追问一个核心要素
- **个性化回复**：基于用户的具体内容进行追问
- **温暖语气**：保持支持和理解的态度

### 3. 数据存储

- 所有追问分析调用都会存储在 `mcp_tool_calls` 表中
- 可以通过 `tool_call_id` 追踪具体的分析记录
- 便于后续的数据分析和模型优化

## 技术实现

### 核心组件

1. **追问工具** (`tools/parenting/follow_up.py`)

   - 实现 S-A-R-F 分析逻辑
   - 调用 LLM 进行智能判断
   - 返回结构化的 JSON 结果

2. **分析服务** (`controllers/parent_child_relationship/api/analysis.py`)

   - 封装追问分析业务逻辑
   - 处理数据库存储
   - 提供 S-A-R-F 完整性评估

3. **API 路由** (`controllers/parent_child_relationship/routes/analysis.py`)
   - 提供 RESTful 接口
   - 统一的错误处理
   - 标准的 API 响应格式

### 扩展性

追问功能采用模块化设计，便于后续扩展：

- **多语言支持**：可以轻松添加其他语言的追问逻辑
- **自定义模型**：可以接入不同的 LLM 模型
- **规则引擎**：可以添加更复杂的追问规则

---

_更多技术细节请参考项目的其他文档文件。_

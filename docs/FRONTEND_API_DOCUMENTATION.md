# 关系分析系统前端 API 调用文档

## 📋 目录

- [系统概览](#系统概览)
- [服务连接](#服务连接)
- [情绪分析服务](#情绪分析服务)
- [智能分析服务](#智能分析服务)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)
- [示例代码](#示例代码)

## 🏗️ 系统概览

关系分析系统采用微服务架构，提供三个独立的 WebSocket 服务：

| 服务名称     | 端口 | 功能描述            | WebSocket 地址      |
| ------------ | ---- | ------------------- | ------------------- |
| MCP 服务器   | 8080 | 原子工具管理        | ws://localhost:8080 |
| 情绪分析服务 | 8765 | 情绪检测与支持      | ws://localhost:8765 |
| 智能分析服务 | 8766 | 人设分析与 RAG 检索 | ws://localhost:8766 |

## 🔌 服务连接

### 连接建立

```javascript
// 情绪分析服务连接
const emotionWs = new WebSocket("ws://localhost:8765");

// 智能分析服务连接
const intelligentWs = new WebSocket("ws://localhost:8766");

// 连接事件处理
emotionWs.onopen = function (event) {
  console.log("情绪分析服务连接成功");
};

emotionWs.onmessage = function (event) {
  const data = JSON.parse(event.data);
  handleEmotionResponse(data);
};

emotionWs.onerror = function (error) {
  console.error("连接错误:", error);
};

emotionWs.onclose = function (event) {
  console.log("连接已关闭");
};
```

### 连接确认消息

连接成功后，服务器会发送欢迎消息：

```json
{
  "type": "connection_established",
  "connection_id": "conn_1701234567_1",
  "message": "情绪分析与支持服务已就绪",
  "services": ["emotion_analysis", "emotional_support"],
  "timestamp": 1701234567.123
}
```

## 💭 情绪分析服务

### 支持的消息类型

- `analyze_text` - 文本情绪分析
- `generate_support` - 生成情绪支持回复
- `ping` - 心跳检测

### 1. 文本情绪分析

#### 请求格式

```json
{
  "type": "analyze_text",
  "text": "今天我和她吵架了，我觉得很难过"
}
```

#### 响应流程

1. **开始分析确认**

```json
{
  "type": "analysis_started",
  "message": "开始分析文本情绪...",
  "timestamp": 1701234567.123
}
```

2. **分析结果**

```json
{
  "type": "analysis_result",
  "data": {
    "overall_intensity": "high",
    "emotion_analysis": {
      "sadness": [
        {
          "segment_index": 0,
          "score": 0.85,
          "intensity_level": "high",
          "confidence_level": "high",
          "confidence_width": 0.15,
          "is_outlier": false
        }
      ],
      "anger": [
        {
          "segment_index": 0,
          "score": 0.72,
          "intensity_level": "moderate",
          "confidence_level": "high",
          "confidence_width": 0.12,
          "is_outlier": false
        }
      ]
    },
    "outlier_emotions": [],
    "risk_level": "medium",
    "statistics": {
      "total_emotions_analyzed": 2,
      "high_intensity_count": 1,
      "extreme_intensity_count": 0,
      "outlier_count": 0
    },
    "summary": "检测到1个异常情绪，整体强度为high，风险等级为medium"
  },
  "original_text": "今天我和她吵架了，我觉得很难过",
  "timestamp": 1701234567.456,
  "status": "success"
}
```

#### 强度等级说明

- `minimal` - 极低强度 (0.0-0.3)
- `low` - 低强度 (0.3-0.5)
- `moderate` - 中等强度 (0.5-0.7)
- `high` - 高强度 (0.7-0.85)
- `extreme` - 极高强度 (0.85-1.0)

#### 风险等级说明

- `low` - 低风险：少量中低强度情绪
- `medium` - 中等风险：存在高强度情绪
- `high` - 高风险：存在极高强度情绪或多个高强度情绪

### 2. 生成情绪支持回复

#### 请求格式

```json
{
  "type": "generate_support",
  "context": "今天我和她吵架了，我觉得很难过",
  "data": [
    {
      "emotion": "sadness",
      "score": 0.85,
      "intensity_level": "high"
    }
  ],
  "message": "我们之前的关系一直很好"
}
```

#### 响应流程

1. **开始生成确认**

```json
{
  "type": "support_started",
  "message": "开始生成情绪支持回复...",
  "timestamp": 1701234567.123
}
```

2. **支持回复结果**

```json
{
  "type": "support_result",
  "data": {
    "comfort_response": "我理解你现在的难过情绪。吵架确实让人感到痛苦，但这也是关系中正常的一部分。重要的是你们能够开诚布公地交流，找到解决问题的方法。给彼此一些时间冷静下来，然后尝试理解对方的观点。",
    "detected_emotion": "sadness",
    "emotion_data_count": 1
  },
  "original_context": "今天我和她吵架了，我觉得很难过",
  "timestamp": 1701234567.789,
  "status": "success"
}
```

### 3. 心跳检测

#### 请求格式

```json
{
  "type": "ping"
}
```

#### 响应格式

```json
{
  "type": "pong",
  "timestamp": 1701234567.123
}
```

## 🧠 智能分析服务

### 支持的消息类型

- `ping` - 心跳检测
- `store_relationship_text` - 存储关系文本
- `confirm_persona_insight` - 确认人设洞察
- `request_comprehensive_analysis` - 请求综合分析
- `get_relationship_history` - 获取关系历史
- `get_emotion_diary` - 获取情绪日记
- `search_relationship_records` - 搜索关系记录
- `analyze_emotion_trends` - 分析情绪趋势
- `get_persona_evolution` - 获取人设演化
- `update_text_content` - 更新文本内容

### 1. 存储关系文本

#### 请求格式

```json
{
  "type": "store_relationship_text",
  "text": "今天我们一起去看电影了，她选了一部浪漫喜剧",
  "user_id": "user_123",
  "metadata": {
    "date": "2024-01-15",
    "activity": "movie_date"
  }
}
```

#### 响应格式

```json
{
  "type": "storage_result",
  "success": true,
  "text_id": "text_456",
  "message": "文本存储成功",
  "persona_insights": [
    {
      "insight": "用户喜欢浪漫喜剧类型的电影",
      "confidence": 0.8,
      "category": "entertainment_preference"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. 请求综合分析

#### 请求格式

```json
{
  "type": "request_comprehensive_analysis",
  "user_id": "user_123",
  "analysis_type": "relationship_status",
  "time_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-15"
  }
}
```

#### 响应格式

```json
{
  "type": "comprehensive_analysis_result",
  "analysis": {
    "relationship_status": "stable_positive",
    "emotional_trends": {
      "happiness": 0.75,
      "sadness": 0.15,
      "anger": 0.1
    },
    "key_insights": [
      "关系整体呈现积极稳定的状态",
      "共同活动频率较高，增进了感情"
    ],
    "recommendations": [
      "继续保持良好的沟通习惯",
      "可以尝试更多不同类型的约会活动"
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 3. 获取关系历史

#### 请求格式

```json
{
  "type": "get_relationship_history",
  "user_id": "user_123",
  "limit": 20,
  "offset": 0,
  "filters": {
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-01-15"
    },
    "emotion_filter": ["happiness", "sadness"]
  }
}
```

#### 响应格式

```json
{
  "type": "relationship_history_result",
  "data": {
    "records": [
      {
        "id": "record_789",
        "text": "今天我们一起去看电影了",
        "date": "2024-01-15",
        "emotions": {
          "happiness": 0.8,
          "excitement": 0.6
        },
        "metadata": {
          "activity": "movie_date"
        }
      }
    ],
    "total_count": 15,
    "has_more": false
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 4. 搜索关系记录

#### 请求格式

```json
{
  "type": "search_relationship_records",
  "user_id": "user_123",
  "query": "电影 约会",
  "search_type": "semantic",
  "limit": 10
}
```

#### 响应格式

```json
{
  "type": "search_result",
  "results": [
    {
      "record_id": "record_789",
      "text": "今天我们一起去看电影了",
      "relevance_score": 0.95,
      "date": "2024-01-15",
      "summary": "与电影约会相关的记录"
    }
  ],
  "total_results": 3,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## ❌ 错误处理

### 通用错误格式

```json
{
  "type": "error",
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": 1701234567.123,
  "details": {
    "additional_info": "额外的错误信息"
  }
}
```

### 常见错误类型

#### 1. 验证错误

```json
{
  "type": "validation_error",
  "error": "文本内容不能为空",
  "timestamp": 1701234567.123
}
```

#### 2. 分析错误

```json
{
  "type": "analysis_error",
  "error": "模型分析失败",
  "message": "分析过程中出现错误",
  "timestamp": 1701234567.123,
  "status": "error"
}
```

#### 3. 未知消息类型

```json
{
  "type": "unknown_message_type",
  "error": "未知的消息类型: invalid_type",
  "supported_types": ["analyze_text", "generate_support", "ping"],
  "timestamp": 1701234567.123
}
```

#### 4. JSON 解析错误

```json
{
  "type": "json_parse_error",
  "error": "无法解析JSON消息",
  "timestamp": 1701234567.123
}
```

## ✨ 最佳实践

### 1. 连接管理

```javascript
class EmotionAnalysisClient {
  constructor(url) {
    this.url = url;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    this.ws = new WebSocket(this.url);

    this.ws.onopen = () => {
      console.log("连接成功");
      this.reconnectAttempts = 0;
    };

    this.ws.onclose = () => {
      this.handleReconnect();
    };

    this.ws.onerror = (error) => {
      console.error("连接错误:", error);
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(JSON.parse(event.data));
    };
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        console.log(`重连尝试 ${this.reconnectAttempts}`);
        this.connect();
      }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
    }
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.error("连接未就绪");
    }
  }
}
```

### 2. 心跳检测

```javascript
class HeartbeatManager {
  constructor(ws) {
    this.ws = ws;
    this.heartbeatInterval = 30000; // 30秒
    this.timeoutDuration = 5000; // 5秒超时
    this.heartbeatTimer = null;
    this.timeoutTimer = null;
  }

  start() {
    this.heartbeatTimer = setInterval(() => {
      this.sendPing();
    }, this.heartbeatInterval);
  }

  stop() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
  }

  sendPing() {
    this.ws.send(JSON.stringify({ type: "ping" }));

    this.timeoutTimer = setTimeout(() => {
      console.error("心跳超时，连接可能已断开");
      // 触发重连逻辑
    }, this.timeoutDuration);
  }

  handlePong() {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
  }
}
```

### 3. 消息队列处理

```javascript
class MessageQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
  }

  async addMessage(message) {
    this.queue.push(message);
    if (!this.processing) {
      await this.processQueue();
    }
  }

  async processQueue() {
    this.processing = true;

    while (this.queue.length > 0) {
      const message = this.queue.shift();
      try {
        await this.handleMessage(message);
      } catch (error) {
        console.error("处理消息失败:", error);
      }
    }

    this.processing = false;
  }

  async handleMessage(message) {
    // 根据消息类型进行处理
    switch (message.type) {
      case "analysis_result":
        this.handleAnalysisResult(message);
        break;
      case "support_result":
        this.handleSupportResult(message);
        break;
      // ... 其他消息类型
    }
  }
}
```

## 🔧 示例代码

### 完整的前端集成示例

```html
<!DOCTYPE html>
<html>
  <head>
    <title>关系分析系统前端示例</title>
    <style>
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .input-group {
        margin: 10px 0;
      }
      .result {
        background: #f5f5f5;
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
      }
      .error {
        background: #ffe6e6;
        color: #d00;
      }
      .success {
        background: #e6ffe6;
        color: #070;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>关系分析系统</h1>

      <div class="input-group">
        <label>输入文本:</label><br />
        <textarea
          id="textInput"
          rows="4"
          cols="50"
          placeholder="请输入要分析的文本..."
        ></textarea
        ><br />
        <button onclick="analyzeText()">分析情绪</button>
        <button onclick="generateSupport()">生成支持</button>
      </div>

      <div id="results"></div>
    </div>

    <script>
      // 全局变量
      let emotionWs = null;
      let intelligentWs = null;
      let currentAnalysisData = null;

      // 页面加载时连接服务
      window.onload = function () {
        connectToServices();
      };

      function connectToServices() {
        // 连接情绪分析服务
        emotionWs = new WebSocket("ws://localhost:8765");

        emotionWs.onopen = function () {
          addResult("✅ 情绪分析服务连接成功", "success");
        };

        emotionWs.onmessage = function (event) {
          const data = JSON.parse(event.data);
          handleEmotionMessage(data);
        };

        emotionWs.onerror = function (error) {
          addResult("❌ 情绪分析服务连接错误: " + error, "error");
        };

        // 连接智能分析服务
        intelligentWs = new WebSocket("ws://localhost:8766");

        intelligentWs.onopen = function () {
          addResult("✅ 智能分析服务连接成功", "success");
        };

        intelligentWs.onmessage = function (event) {
          const data = JSON.parse(event.data);
          handleIntelligentMessage(data);
        };

        intelligentWs.onerror = function (error) {
          addResult("❌ 智能分析服务连接错误: " + error, "error");
        };
      }

      function analyzeText() {
        const text = document.getElementById("textInput").value.trim();
        if (!text) {
          alert("请输入要分析的文本");
          return;
        }

        const message = {
          type: "analyze_text",
          text: text,
        };

        emotionWs.send(JSON.stringify(message));
      }

      function generateSupport() {
        const text = document.getElementById("textInput").value.trim();
        if (!text) {
          alert("请输入文本");
          return;
        }

        if (!currentAnalysisData) {
          alert("请先进行情绪分析");
          return;
        }

        const message = {
          type: "generate_support",
          context: text,
          data: currentAnalysisData,
          message: "",
        };

        emotionWs.send(JSON.stringify(message));
      }

      function handleEmotionMessage(data) {
        switch (data.type) {
          case "connection_established":
            addResult("🔗 " + data.message, "success");
            break;

          case "analysis_started":
            addResult("🔄 " + data.message);
            break;

          case "analysis_result":
            handleAnalysisResult(data);
            break;

          case "support_started":
            addResult("🔄 " + data.message);
            break;

          case "support_result":
            handleSupportResult(data);
            break;

          case "analysis_error":
          case "support_error":
            addResult("❌ " + data.message + ": " + data.error, "error");
            break;

          case "validation_error":
            addResult("⚠️ 验证错误: " + data.error, "error");
            break;

          default:
            console.log("未处理的消息类型:", data);
        }
      }

      function handleIntelligentMessage(data) {
        switch (data.type) {
          case "connection_established":
            addResult("🔗 智能分析: " + data.message, "success");
            break;

          default:
            console.log("智能分析消息:", data);
        }
      }

      function handleAnalysisResult(data) {
        const result = data.data;

        // 准备支持生成的数据
        currentAnalysisData = [];
        for (const emotion in result.emotion_analysis) {
          const emotionData = result.emotion_analysis[emotion];
          if (emotionData.length > 0) {
            currentAnalysisData.push({
              emotion: emotion,
              score: emotionData[0].score,
              intensity_level: emotionData[0].intensity_level,
            });
          }
        }

        let html = "<h3>📊 分析结果</h3>";
        html += `<p><strong>整体强度:</strong> ${result.overall_intensity}</p>`;
        html += `<p><strong>风险等级:</strong> ${result.risk_level}</p>`;
        html += `<p><strong>摘要:</strong> ${result.summary}</p>`;

        html += "<h4>情绪详情:</h4>";
        for (const emotion in result.emotion_analysis) {
          const emotionData = result.emotion_analysis[emotion][0];
          html += `<p>• <strong>${emotion}:</strong> 分数=${emotionData.score.toFixed(
            2
          )}, 强度=${emotionData.intensity_level}</p>`;
        }

        if (result.outlier_emotions.length > 0) {
          html += "<h4>异常情绪:</h4>";
          result.outlier_emotions.forEach((outlier) => {
            html += `<p>• ${outlier.emotion}: ${outlier.outlier_reason}</p>`;
          });
        }

        addResult(html);
      }

      function handleSupportResult(data) {
        const result = data.data;

        let html = "<h3>💝 情绪支持</h3>";
        html += `<p><strong>检测到的主要情绪:</strong> ${
          result.detected_emotion || "未知"
        }</p>`;
        html += `<div style="background: #f0f8ff; padding: 15px; border-radius: 5px; border-left: 4px solid #4a90e2;">`;
        html += `<strong>支持回复:</strong><br>${result.comfort_response}`;
        html += `</div>`;

        addResult(html, "success");
      }

      function addResult(content, type = "") {
        const resultsDiv = document.getElementById("results");
        const resultDiv = document.createElement("div");
        resultDiv.className = `result ${type}`;
        resultDiv.innerHTML = content;
        resultsDiv.appendChild(resultDiv);
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
      }
    </script>
  </body>
</html>
```

## 更新用户资料接口

### PUT /api/auth/profile

更新当前用户的基本资料信息。

**请求头：**

```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**请求体：**

```json
{
  "nickname": "新昵称",
  "avatar_url": "https://example.com/avatar.jpg",
  "username": "新用户名"
}
```

**字段说明：**

- `nickname` (可选): 用户昵称，1-100 个字符
- `avatar_url` (可选): 头像链接，必须是有效的 HTTP/HTTPS URL
- `username` (可选): 用户名，1-50 个字符，只能包含字母、数字、下划线和中文字符

**响应示例：**

成功响应 (200):

```json
{
  "success": true,
  "message": "用户资料更新成功",
  "data": {
    "user": {
      "id": "user_12345",
      "phone_number": "138****5678",
      "nickname": "新昵称",
      "avatar_url": "https://example.com/avatar.jpg",
      "username": "新用户名",
      "user_type": "phone",
      "status": "active",
      "phone_verified": true,
      "last_login_at": "2024-01-01T12:00:00",
      "updated_at": "2024-01-01T12:30:00",
      "created_at": "2024-01-01T10:00:00"
    },
    "updated_fields": ["nickname", "avatar_url", "username"],
    "update_summary": "成功更新了 3 个字段"
  },
  "timestamp": "2024-01-01T12:30:00Z"
}
```

错误响应：

400 - 请求参数错误:

```json
{
  "success": false,
  "message": "请提供至少一个需要更新的字段",
  "data": {},
  "timestamp": "2024-01-01T12:30:00Z"
}
```

401 - 未认证:

```json
{
  "success": false,
  "message": "未提供认证令牌",
  "data": {},
  "timestamp": "2024-01-01T12:30:00Z"
}
```

403 - 用户状态不活跃:

```json
{
  "success": false,
  "message": "用户状态不活跃，无法更新资料",
  "data": {},
  "timestamp": "2024-01-01T12:30:00Z"
}
```

404 - 用户不存在:

```json
{
  "success": false,
  "message": "用户不存在或已被删除",
  "data": {},
  "timestamp": "2024-01-01T12:30:00Z"
}
```

**使用示例：**

```javascript
// 只更新昵称
const response = await fetch("/api/auth/profile", {
  method: "PUT",
  headers: {
    Authorization: "Bearer " + token,
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    nickname: "我的新昵称",
  }),
});

// 更新多个字段
const response = await fetch("/api/auth/profile", {
  method: "PUT",
  headers: {
    Authorization: "Bearer " + token,
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    nickname: "张三",
    username: "zhangsan2024",
    avatar_url: "https://example.com/avatar.jpg",
  }),
});
```

**注意事项：**

1. 至少需要提供一个字段进行更新
2. 所有字段都是可选的，可以只更新部分字段
3. 不能通过此接口修改手机号、用户类型等敏感信息
4. 头像 URL 必须是有效的 HTTP/HTTPS 链接
5. 用户名只能包含字母、数字、下划线和中文字符
6. 需要有效的 JWT token 进行身份验证

## 📝 注意事项

1. **连接稳定性**: 建议实现自动重连机制，处理网络中断情况
2. **消息顺序**: WebSocket 消息是异步的，注意处理消息顺序问题
3. **错误处理**: 始终包含完善的错误处理逻辑
4. **性能优化**: 对于大量数据，考虑分页和虚拟滚动
5. **安全考虑**: 在生产环境中使用 wss://协议和适当的身份验证
6. **资源清理**: 页面卸载时正确关闭 WebSocket 连接

## 🔄 版本更新

- **v2.0.0** (2024-01-15): 重构为模块化架构，支持懒加载
- **v1.0.0** (2024-01-01): 初始版本发布

---

_最后更新: 2024 年 1 月 15 日_

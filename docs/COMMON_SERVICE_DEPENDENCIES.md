# Common 服务依赖管理和配置指南

## 概述

本文档说明了 common 服务中用到的所有依赖包，以及 PM2 日志管理配置。

## 新增的依赖包

### 1. 加密服务依赖

**包名**: `pycryptodome==3.21.0`

**用途**: 
- 闪验服务（flash_service.py）中的 AES 和 RSA 加密算法
- 用于解密闪验 SDK 返回的加密手机号

**主要模块**:
```python
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
```

### 2. 腾讯云 STS 服务依赖

**包名**: `qcloud-python-sts==3.1.6`

**用途**: 
- COS 临时密钥服务（cos_service.py）
- 生成腾讯云 COS 临时访问密钥用于前端直接上传

**主要模块**:
```python
from sts.sts import Sts
```

### 3. JWT 认证依赖

**包名**: `PyJWT==2.9.0`

**用途**: 
- JWT 认证服务（jwt_service.py）
- 生成和验证用户登录 token

**主要模块**:
```python
import jwt
```

## 完整依赖列表

以下是 common 服务中使用的所有关键依赖包：

| 包名 | 版本 | 用途 | 状态 |
|------|------|------|------|
| fastapi | 0.115.6 | Web 框架 | ✅ |
| uvicorn | 0.34.2 | ASGI 服务器 | ✅ |
| pydantic | 2.11.5 | 数据验证 | ✅ |
| aiohttp | 3.12.2 | 异步 HTTP 客户端 | ✅ |
| requests | 2.32.3 | HTTP 客户端 | ✅ |
| PyJWT | 2.9.0 | JWT 认证 | ✅ 新增 |
| pycryptodome | 3.21.0 | 加密算法 | ✅ 新增 |
| qcloud-python-sts | 3.1.6 | 腾讯云 STS | ✅ 新增 |
| asyncpg | 0.30.0 | PostgreSQL 驱动 | ✅ |
| python-multipart | 0.0.20 | 表单解析 | ✅ |

## PM2 日志管理配置

### 配置特性

- **日志按天分割**: 每天午夜自动轮转日志文件
- **保留天数**: 只保留最近 3 天的日志文件
- **自动压缩**: 旧日志文件自动 gzip 压缩节省空间
- **大小限制**: 单个日志文件超过 50MB 时自动轮转

### 配置方法

1. **安装日志轮转模块**:
```bash
pm2 install pm2-logrotate
```

2. **自动配置**（推荐）:
```bash
./scripts/setup_pm2_logrotate.sh
```

3. **手动配置**:
```bash
# 设置最大文件大小
pm2 set pm2-logrotate:max_size 50M

# 保留3天日志
pm2 set pm2-logrotate:retain 3

# 启用压缩
pm2 set pm2-logrotate:compress true

# 设置日期格式
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD

# 每天午夜轮转
pm2 set pm2-logrotate:rotateInterval '0 0 * * *'
```

### 日志文件结构

```
logs/
├── mcp-server.log              # 当前日志
├── mcp-server-2024-01-15.log   # 昨天日志
├── mcp-server-2024-01-14.log.gz # 前天日志（压缩）
├── common-server.log           # 当前日志
├── common-server-2024-01-15.log
└── parent-child-relationship-server.log
```

## 使用脚本

### 1. 依赖检查脚本

**路径**: `scripts/check_dependencies.py`

**功能**: 检查所有关键依赖包是否已正确安装

**使用方法**:
```bash
python scripts/check_dependencies.py
```

**输出示例**:
```
🔍 检查 common 服务依赖包...
============================================================
包名                   状态         版本/错误信息
------------------------------------------------------------
fastapi              ✅ 已安装      0.115.12
uvicorn              ✅ 已安装      0.34.2
PyJWT                ✅ 已安装      2.9.0
...
============================================================
📊 检查结果: 10/10 个包已正确安装
```

### 2. PM2 日志轮转配置脚本

**路径**: `scripts/setup_pm2_logrotate.sh`

**功能**: 自动安装和配置 PM2 日志轮转

**使用方法**:
```bash
./scripts/setup_pm2_logrotate.sh
```

## 安装步骤

### 完整安装流程

1. **安装 Python 依赖**:
```bash
pip install -r requirements.txt
```

2. **检查依赖**:
```bash
python scripts/check_dependencies.py
```

3. **配置 PM2 日志轮转**:
```bash
./scripts/setup_pm2_logrotate.sh
```

4. **启动服务**:
```bash
pm2 start ecosystem.config.js
```

### 故障排除

#### 依赖安装失败

如果部分包安装失败，可以手动安装：

```bash
# 手动安装缺失的包
pip install PyJWT==2.9.0
pip install pycryptodome==3.21.0
pip install qcloud-python-sts==3.1.6
```

#### PM2 日志轮转问题

1. **检查模块状态**:
```bash
pm2 ls
```

2. **查看配置**:
```bash
pm2 conf pm2-logrotate
```

3. **重新配置**:
```bash
pm2 uninstall pm2-logrotate
./scripts/setup_pm2_logrotate.sh
```

## 更新历史

### 2024-01-16
- ✅ 添加 `PyJWT==2.9.0` 用于 JWT 认证
- ✅ 添加 `pycryptodome==3.21.0` 用于闪验加密
- ✅ 添加 `qcloud-python-sts==3.1.6` 用于腾讯云 STS
- ✅ 配置 PM2 日志按天分割，保留 3 天
- ✅ 创建自动化配置脚本
- ✅ 创建依赖检查脚本

---

## 相关文档

- [Common 服务 API 文档](./FRONTEND_API_DOCUMENTATION.md)
- [项目快速参考](./QUICK_REFERENCE.md)
- [短信认证 API 示例](./sms_auth_api_examples.md) 
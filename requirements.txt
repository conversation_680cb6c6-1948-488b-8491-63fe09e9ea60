annotated-types==0.7.0
anyio==4.9.0
asyncpg==0.30.0
certifi==2025.4.26
click==8.2.1
distro==1.9.0
exceptiongroup==1.3.0
fastmcp==2.5.2
grpcio==1.71.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
hyperframe==6.1.0
idna==3.10
iniconfig==2.1.0
jiter==0.10.0
markdown-it-py==3.0.0
mcp==1.9.2
mdurl==0.1.2
numpy==2.2.6
openai==1.82.1
openapi-pydantic==0.5.1
packaging>=23.2,<25
pluggy==1.6.0
portalocker==2.10.1
protobuf==6.31.1
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
pytest==8.3.5
pytest-asyncio==1.0.0
python-dotenv==1.1.0
python-multipart==0.0.20
qdrant-client==1.14.2
rich==14.0.0
setuptools==78.1.1
shellingham==1.5.4
sniffio==1.3.1
sse-starlette==2.3.5
starlette>=0.27
tqdm==4.67.1
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.13.2  
urllib3==2.4.0
uvicorn==0.34.2
websockets==15.0.1
wheel==0.45.1

# 短信验证码登录系统依赖
fastapi==0.115.9
aiohttp==3.12.2
PyJWT==2.9.0

# 阿里云百炼大模型依赖
dashscope==1.20.12

# 腾讯云COS STS服务依赖
requests==2.32.3
qcloud-python-sts==3.1.6

# 加密服务依赖（闪验服务的AES/RSA加密）
pycryptodome==3.21.0

# langchain相关依赖（记忆模块向量数据库）
langchain-openai==0.2.11
langchain-community==0.3.15
# langchain-core==>=0.,<25
chromadb==1.0.15
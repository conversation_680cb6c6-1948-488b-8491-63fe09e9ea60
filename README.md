
## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 配置环境变量（复制.env.example到.env并修改）
cp .env.example .env
```

### 2. 数据库初始化

```bash
# 运行数据库迁移
python migrate.py
```

### 3. 启动服务

#### 方式一：开发模式（单独启动）
```bash
# 启动MCP服务器（原子工具管理）
python main.py

# 启动通用服务（认证、文件上传等）
python controllers/common/server.py

# 启动亲子关系分析服务
python controllers/parent_child_relationship/server.py
```

#### 方式二：生产模式（PM2管理）
```bash
# 安装PM2（如果未安装）
npm install -g pm2

# 启动所有服务
pm2 start ecosystem.config.js

# 查看服务状态
pm2 status

# 查看日志
pm2 logs
```

## 📦 核心功能模块

### 🎯 MCP服务器 (main.py)
- **端口**: 8080
- **功能**: 原子工具管理，MCP 2.x规范兼容
- **传输**: streamable-http
- **工具注册**: 自动注册所有tools和workflows

### 🔐 通用服务 (controllers/common/)
- **端口**: 8010
- **功能**: 
  - JWT认证和授权
  - 短信验证码登录
  - 闪验一键登录
  - 腾讯云COS文件上传
  - 火山引擎语音识别

### 👨‍👩‍👧‍👦 亲子关系分析 (controllers/parent_child_relationship/)
- **端口**: 8011
- **功能**:
  - 育儿日记分析
  - 亲子关系洞察
  - 成长记录管理
  - 个性化建议生成

### 💕 情感分析模块 (controllers/love_analysis/)
- **情绪分析**: WebSocket实时情绪识别
- **智能分析**: RAG知识检索、人设洞察
- **向量数据库**: Qdrant集成，语义搜索

### 🛠️ 基础工具 (tools/)
- **情感支持**: 智能安慰回应生成
- **文本分析**: 关系文本标注和情绪识别  
- **人格分析**: 基于心理学的人格特质分析
- **育儿工具**: 育儿日记分析、洞察生成

## 🗄️ 数据库设计

### PostgreSQL 主数据库
- **用户管理**: 支持访客、微信、手机号三种登录方式
- **记录存储**: 用户记录、分析结果、洞察数据
- **文件管理**: COS文件关联和元数据
- **成员管理**: 家庭成员信息

### 向量数据库
- **ChromaDB**: 本地记忆存储，支持语义搜索
- **Qdrant**: 分布式向量数据库，人设洞察存储

## 🔧 配置管理

### 环境变量配置
```bash
# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5433
DATABASE_NAME=mcp_db_local
DATABASE_USER=mcp_db_local
DATABASE_PASSWORD=mcp_db_local

# 短信服务配置
SMS_ACCOUNT=your_sms_account
SMS_PASSWORD=your_sms_password
SMS_DEV_MODE=true

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080

# 腾讯云COS配置
COS_SECRET_ID=your_secret_id
COS_SECRET_KEY=your_secret_key

# 向量数据库配置
QDRANT_URL=http://localhost:6333
OPENAI_API_KEY=your_openai_key
```

## 🚀 部署指南

### PM2进程管理
```bash
# 启动所有服务
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save

# 日志管理
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 50M
pm2 set pm2-logrotate:retain 3
```

### Nginx反向代理
```nginx
# 使用项目根目录的nginx.conf配置文件
# 支持多服务反向代理和负载均衡
```

## 📊 API接口

### MCP工具接口
- **端口**: 8080
- **路径**: `/mcp`
- **协议**: MCP 2.x streamable-http

### REST API接口
- **通用服务**: `http://localhost:8010`
- **亲子关系**: `http://localhost:8011`

### WebSocket接口
- **情绪分析**: `ws://localhost:8765`
- **智能分析**: `ws://localhost:8766`

## 🧪 测试和开发

### 运行测试
```bash
# 检查依赖
python scripts/check_dependencies.py

# 测试育儿分析
python scripts/test_analyze_parenting_journal.py

# 测试洞察API
python scripts/test_more_insight_api.py
```

### 开发工具
- **数据库迁移**: `python migrate.py`
- **依赖检查**: `scripts/check_dependencies.py`
- **PM2日志轮转**: `scripts/setup_pm2_logrotate.sh`

## 🔍 监控和日志

### 服务监控
```bash
# 查看所有服务状态
pm2 status

# 查看特定服务日志
pm2 logs mcp-server
pm2 logs common-service
pm2 logs parent-child-relationship-server
```

### 日志文件位置
- **MCP服务器**: `./logs/mcp-server.log`
- **通用服务**: `./logs/common-server.log`
- **亲子关系服务**: `./logs/parent-child-relationship-server.log`

## 🛠️ 技术栈

### 后端框架
- **FastMCP 2.x**: MCP协议服务器
- **FastAPI**: REST API服务器
- **WebSocket**: 实时通信
- **AsyncPG**: PostgreSQL异步驱动

### AI和机器学习
- **OpenAI**: GPT模型接口
- **DeepSeek**: 国产大语言模型
- **通义千问**: 阿里云大模型
- **LangChain**: AI应用框架
- **ChromaDB**: 向量数据库
- **Qdrant**: 分布式向量搜索

### 数据存储
- **PostgreSQL**: 主数据库
- **ChromaDB**: 本地向量存储
- **Qdrant**: 分布式向量数据库
- **腾讯云COS**: 对象存储

### 认证和安全
- **JWT**: 令牌认证
- **短信验证**: 手机号登录
- **闪验**: 一键登录
- **AES/RSA**: 数据加密

## 📚 文档资源

- [前端API文档](docs/FRONTEND_API_DOCUMENTATION.md)
- [快速参考指南](docs/QUICK_REFERENCE.md)
- [短信认证API示例](docs/sms_auth_api_examples.md)
- [模块化架构文档](controllers/love_analysis/docs/MODULAR_ARCHITECTURE.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [联系信息]
- 问题反馈: [GitHub Issues](https://github.com/your-repo/feeling-server/issues)
- 技术支持: [技术支持邮箱]# 关系分析系统 (Relationship Analysis System)

基于 FastMCP 2.x (Model Context Protocol) 的智能关系分析系统，提供情感支持、人格洞察和关系建议。支持 PostgreSQL 和 Qdrant 向量数据库。

## 🏗️ 项目结构

```
relationship_analysis_system/
├── main.py                     # 统一启动文件
├── requirements.txt            # 项目依赖
├── README.md                   # 项目说明
├── 
├── tools/                      # 基础工具包
│   ├── __init__.py
│   ├── generate_comforting_response.py      # 毒舌闺蜜安慰回应
│   ├── annotate_relationship_text.py        # 关系文本标注和情绪分析
│   ├── analyze_partner_persona.py           # 伴侣人格特质分析
│   ├── store_relationship_entry.py          # 关系条目存储
│   ├── generate_narrative_advice.py         # 叙事建议和改写
│   ├── store_confirmed_persona_insight.py   # 存储确认的人格洞察
│   ├── get_recent_confirmed_persona_insights.py  # 获取最近确认的洞察
│   ├── generate_comprehensive_persona_description.py  # 生成综合人格描述
│   └── check_persona_insights_status.py     # 检查洞察状态
│   
├── controllers/                # 工作流控制器
│   ├── __init__.py
│   ├── server_controller.py                 # 服务器资源和工具接口
│   ├── langgraph_controller.py              # LangGraph 工作流控制器
│   ├── persona_insight_workflow.py          # 人格洞察工作流
│   ├── analyze_relationship_workflow.py     # 综合关系分析工作流
│   └── comprehensive_relationship_workflow.py  # 并行综合分析工作流
│   
├── utils/                      # 核心工具
│   ├── __init__.py
│   ├── models.py                            # 数据模型定义
│   └── storage.py                           # 数据存储管理
│   
├── prompts/                    # 提示词模板
│   ├── __init__.py
│   └── relationship_prompts.py              # 关系分析提示词
│   
├── models/                     # AI 模型接口
│   ├── __init__.py
│   └── deepseek.py                          # DeepSeek 模型接口
│   
├── data/                       # 数据存储
│   └── relationship_insights.db             # SQLite 数据库
│   
└── docs/                       # 文档
    └── ...
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python main.py
```

### 3. 配置环境变量（可选）

```bash
export RELATIONSHIP_SERVER_PORT=8003
export RELATIONSHIP_SERVER_NAME=relationship_insights
```

## 📦 组件说明

### 基础工具 (tools/)

- **情感支持**: 毒舌闺蜜风格的安慰回应
- **文本分析**: 关系文本标注和情绪识别
- **人格分析**: 基于心理学框架的伴侣特质分析
- **数据存储**: 关系条目和分析结果存储
- **表达改进**: 负面情绪检测和叙事改写
- **洞察管理**: 人格洞察的确认、存储和综合

### 工作流控制器 (controllers/)

- **服务器控制器**: MCP 资源定义和工具信息接口
- **LangGraph控制器**: 基于 LangGraph 的复杂工作流
- **人格洞察工作流**: 分析→确认→存储的完整流程
- **关系分析工作流**: 文本标注→情绪分析→建议生成
- **综合分析工作流**: 并行执行多个分析任务

### 核心工具 (utils/)

- **数据模型**: SQLAlchemy 模型定义
- **存储管理**: 数据库操作和会话管理

## 🔧 技术特性

- **模块化设计**: 每个工具独立维护，便于扩展
- **异步支持**: 全面支持异步操作和并行执行
- **MCP 标准**: 遵循 Model Context Protocol 规范
- **LangGraph 集成**: 支持复杂的状态机工作流
- **数据持久化**: SQLite 数据库存储分析结果
- **详细日志**: 完整的操作日志和错误追踪

## 🛠️ 开发指南

### 添加新工具

1. 在 `tools/` 目录创建新的工具文件
2. 遵循现有的 `@mcp.tool()` 和 `@mcp.prompt()` 模式
3. 在 `tools/__init__.py` 中添加导入和分类
4. 更新文档

### 添加新控制器

1. 在 `controllers/` 目录创建新的控制器文件
2. 实现 `register_*` 函数来注册 MCP 组件
3. 在 `controllers/__init__.py` 中添加导入
4. 在 `main.py` 中调用注册函数

### 数据库操作

```python
from utils.storage import RelationshipStorage

storage = RelationshipStorage()
# 使用 storage 进行数据操作
```

## 📊 API 接口

### MCP 资源

- `relationship://insights/{entry_id}` - 获取条目洞察
- `relationship://entries/{user_id}` - 获取用户条目

### MCP 工具

- `get_available_tools()` - 获取所有可用工具信息
- `langgraph_persona_insight_workflow()` - LangGraph 人格洞察工作流
- 以及所有基础工具和工作流控制器

## 🔍 监控和调试

服务器启动时会显示：
- 可用基础工具数量
- 可用控制器数量
- 总组件数量
- 详细的启动日志

## 📝 许可证

[添加许可证信息]

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

[添加联系方式] 
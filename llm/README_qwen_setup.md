# Qwen大模型接入配置说明

## 概述

本模块实现了阿里云百炼Qwen大模型的接入，基于官方dashscope SDK，提供与deepseek.py相同的API接口规范。

## 配置步骤

### 1. 获取API密钥

1. 访问[阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 注册并登录阿里云账号
3. 开通百炼服务
4. 在控制台获取API-KEY

### 2. 安装依赖

```bash
pip install dashscope==1.20.12
```

或使用项目的requirements.txt：

```bash
pip install -r requirements.txt
```

### 3. 配置API密钥

在 `llm/qwen.py` 文件中修改：

```python
QWEN_API_KEY = "your-actual-api-key-here"
```

或使用环境变量：

```bash
export DASHSCOPE_API_KEY="your-actual-api-key-here"
```

### 4. 可用模型列表

| 模型名称 | 参数规模 | 特点 | 适用场景 |
|---------|---------|------|---------|
| qwen2.5-7b-instruct | 7B | 轻量级，响应快 | 简单对话、摘要 |
| qwen2.5-14b-instruct | 14B | 平衡性能和效率 | 通用任务 |
| qwen2.5-72b-instruct | 72B | 最强性能 | 复杂推理、创作 |
| qwen-max | - | 最新最强模型 | 高质量要求任务 |
| qwen-plus | - | 高性价比 | 日常应用 |

## API接口说明

### 基础调用

```python
from llm.qwen import chat

# 基础对话
response = await chat("你好，请介绍一下你自己")

# 指定参数
response = await chat(
    prompt="写一首关于春天的诗",
    max_tokens=200,
    temperature=0.8,
    model="qwen2.5-72b-instruct"
)
```

### 函数参数

- `prompt` (str): 输入的提示词
- `max_tokens` (int): 最大生成token数，默认1000
- `temperature` (float): 生成温度，0.0-2.0，默认0.7
- `model` (str): 使用的模型名称，默认"qwen2.5-72b-instruct"

### 辅助函数

```python
from llm.qwen import set_api_key, test_connection, get_available_models

# 动态设置API密钥
set_api_key("new-api-key")

# 测试连接
is_connected = test_connection()

# 获取可用模型
models = get_available_models()
```

## 错误处理

模块提供完善的错误处理机制：

1. **API调用失败**: 自动返回备用回应
2. **网络异常**: 记录错误并提供友好提示
3. **响应格式错误**: 验证并处理异常响应

## 日志记录

模块使用标准logging模块记录：

- API调用信息
- 响应长度统计
- 错误详情
- 性能指标

## 使用示例

参考 `llm/qwen_usage_example.py` 文件获取完整的使用示例。

## 注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制系统
2. **费用控制**: 根据实际需要选择合适的模型和参数
3. **并发限制**: 注意API的并发调用限制
4. **错误重试**: 生产环境建议实现重试机制

## 与deepseek.py的兼容性

本模块与deepseek.py保持相同的接口规范：

- 相同的`chat()`函数签名（除了model参数）
- 相同的错误处理机制
- 相同的日志格式
- 相同的返回值类型

可以在项目中灵活切换使用不同的LLM后端。

## 技术支持

如遇到问题，请参考：

1. [阿里云百炼官方文档](https://help.aliyun.com/product/2866125.html)
2. [dashscope SDK文档](https://dashscope.readthedocs.io/)
3. 项目issue反馈 
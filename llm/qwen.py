import dashscope
import logging
from dashscope import Generation

logger = logging.getLogger(__name__)

# 阿里云百炼API密钥 - 请替换为您的实际API密钥
QWEN_API_KEY = "sk-7e5e882f2964497fbfe65f1267e127b5"

# 配置dashscope API密钥
dashscope.api_key = QWEN_API_KEY

# 默认模型配置
DEFAULT_MODEL = "qwen2.5-72b-instruct"  # 可选模型: qwen2.5-7b-instruct, qwen2.5-14b-instruct, qwen2.5-72b-instruct

async def chat(prompt: str, max_tokens: int = 1000, temperature: float = 0.7, model: str = DEFAULT_MODEL) -> str:
    """
    调用阿里云百炼Qwen模型进行文本生成。如果API调用失败，会返回一个合理的备用回应。
    
    Args:
        prompt: 发送给模型的提示
        max_tokens: 生成的最大token数 
        temperature: 生成温度，控制创造性 (0.0-2.0)
        model: 使用的模型名称
        
    Returns:
        模型生成的文本或合理的后备回应
    """
    logger.info(f"Calling Qwen model {model} with prompt length: {len(prompt)}")
    
    try:
        # 构建消息格式
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        # 调用阿里云百炼Generation API
        response = Generation.call(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            result_format='message'  # 返回格式为message
        )
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.output.choices[0].message.content
            logger.info(f"Qwen response length: {len(result)}")
            return result
        else:
            logger.error(f"Qwen API error: {response.code} - {response.message}")
            return generate_fallback_response(prompt)
            
    except Exception as e:
        logger.error(f"Error calling Qwen: {str(e)}")
        # 根据prompt简单生成一个备用回应
        fallback = generate_fallback_response(prompt)
        logger.info(f"Generated fallback response of length: {len(fallback)}")
        return fallback

def generate_fallback_response(prompt: str) -> str:
    """
    根据提示生成一个简单的备用回应
    
    Args:
        prompt: 原始提示词
        
    Returns:
        备用回应文本
    """
    return f"很抱歉，Qwen模型目前遇到一些技术问题。您的请求很重要，建议您稍后再试，或者换个方式描述您的需求。我们会尽快恢复正常服务。"

def get_available_models():
    """
    获取可用的Qwen模型列表
    
    Returns:
        可用模型名称列表
    """
    return [
        "qwen2.5-7b-instruct",      # 7B参数模型，适合轻量级任务
        "qwen2.5-14b-instruct",     # 14B参数模型，平衡性能和效率  
        "qwen2.5-72b-instruct",     # 72B参数模型，最强性能
        "qwen-max",                 # 最新最强模型
        "qwen-plus"                 # 高性价比模型
    ]

def set_api_key(api_key: str):
    """
    设置API密钥
    
    Args:
        api_key: 阿里云百炼API密钥
    """
    global QWEN_API_KEY
    QWEN_API_KEY = api_key
    dashscope.api_key = api_key
    logger.info("Qwen API key updated")

def test_connection() -> bool:
    """
    测试Qwen API连接是否正常
    
    Returns:
        连接是否成功
    """
    try:
        test_response = Generation.call(
            model=DEFAULT_MODEL,
            messages=[{"role": "user", "content": "你好，请回复：连接测试成功"}],
            max_tokens=50,
            temperature=0.1
        )
        
        if test_response.status_code == 200:
            logger.info("Qwen API connection test successful")
            return True
        else:
            logger.error(f"Qwen API connection test failed: {test_response.code}")
            return False
            
    except Exception as e:
        logger.error(f"Qwen API connection test error: {str(e)}")
        return False

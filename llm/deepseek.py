import openai
import logging

logger = logging.getLogger(__name__)

DEEPSEEK_API_KEY = "***********************************"

# 初始化DeepSeek客户端（通过OpenAI兼容接口）
deepseek_client = openai.OpenAI(
    api_key=DEEPSEEK_API_KEY,
    base_url="https://api.deepseek.com"  # DeepSeek API的base URL
)

# DeepSeek辅助函数
# todo 这个函数需要重构，因为DeepSeek的API接口和OpenAI的API接口不兼容 
async def chat(prompt: str, max_tokens: int = 1000, temperature: float = 0.7) -> str:
    """
    调用DeepSeek模型进行文本生成。如果API调用失败，会返回一个合理的备用回应。
    
    Args:
        prompt: 发送给模型的提示
        max_tokens: 生成的最大token数
        temperature: 生成温度，控制创造性
        
    Returns:
        模型生成的文本或合理的后备回应
    """
    logger.info(f"Calling DeepSeek with prompt length: {len(prompt)}")
    try:
        # 使用同步API包装在异步函数中
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",  # DeepSeek的聊天模型
            max_tokens=max_tokens,
            temperature=temperature,
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        result = response.choices[0].message.content
        logger.info(f"DeepSeek response length: {len(result)}")
        return result
    except Exception as e:
        logger.error(f"Error calling DeepSeek: {str(e)}")
        # 根据prompt简单生成一个备用回应
        fallback = generate_fallback_response(prompt)
        logger.info(f"Generated fallback response of length: {len(fallback)}")
        return fallback
    
"""
生成简单的备用回应
"""
def generate_fallback_response(prompt: str) -> str:
    """
    根据提示生成一个简单的备用回应
    """
    return f"很抱歉，系统目前遇到一些技术问题。您的请求很重要，建议您稍后再试，或者换个方式描述您的需求。我们会尽快恢复正常服务。"
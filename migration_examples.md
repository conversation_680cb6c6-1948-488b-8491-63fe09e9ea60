# 数据库迁移脚本使用指南

这个文档介绍如何使用 `migrate.py` 脚本来管理数据库迁移。

## 基本使用

### 1. 执行所有迁移

```bash
python migrate.py migrate
```

这会执行所有待处理的迁移文件，将数据库结构更新到最新版本。

### 2. 查看迁移状态

```bash
python migrate.py status
```

显示当前数据库的迁移状态，包括：
- 已执行的迁移
- 待处理的迁移
- 数据库连接状态

### 3. 测试数据库连接

```bash
python migrate.py test
```

验证数据库连接是否正常，包括连接池状态和基本查询测试。

## 高级使用

### 迁移到指定版本

```bash
python migrate.py migrate --target=002
```

只执行迁移到指定版本（例如版本002），不会执行更新的迁移。

### 详细日志模式

```bash
python migrate.py migrate --verbose
```

显示详细的调试信息，用于故障排除。

### 静默模式

```bash
python migrate.py status --quiet
```

只显示错误信息，适合在脚本中使用。

## 使用场景

### 开发环境初始化

```bash
# 1. 首先测试数据库连接
python migrate.py test

# 2. 查看当前状态
python migrate.py status

# 3. 执行所有迁移
python migrate.py migrate
```

### 生产环境部署

```bash
# 使用静默模式，只在出错时显示信息
python migrate.py migrate --quiet
```

### 开发调试

```bash
# 使用详细模式查看完整的执行过程
python migrate.py migrate --verbose
```

## 输出示例

### 执行迁移的输出

```
2024-01-15 10:30:00 - INFO - 正在初始化数据库连接...
2024-01-15 10:30:01 - INFO - 数据库连接初始化成功
2024-01-15 10:30:01 - INFO - ==================================================
2024-01-15 10:30:01 - INFO - 开始执行数据库迁移
2024-01-15 10:30:01 - INFO - ==================================================
2024-01-15 10:30:01 - INFO - 当前状态:
2024-01-15 10:30:01 - INFO -   已执行迁移: 0
2024-01-15 10:30:01 - INFO -   可用迁移: 3
2024-01-15 10:30:01 - INFO -   待处理迁移: 3
2024-01-15 10:30:01 - INFO - 待执行的迁移文件:
2024-01-15 10:30:01 - INFO -   - 001_create_users_table.py
2024-01-15 10:30:01 - INFO -   - 002_create_mcp_tool_calls_table.py
2024-01-15 10:30:01 - INFO -   - 003_create_remaining_tables.py
2024-01-15 10:30:01 - INFO - 迁移到最新版本
2024-01-15 10:30:02 - INFO - 迁移 001_create_users_table.py 执行成功
2024-01-15 10:30:02 - INFO - 迁移 002_create_mcp_tool_calls_table.py 执行成功
2024-01-15 10:30:03 - INFO - 迁移 003_create_remaining_tables.py 执行成功
2024-01-15 10:30:03 - INFO - ==================================================
2024-01-15 10:30:03 - INFO - 迁移执行完成
2024-01-15 10:30:03 - INFO - 执行了 3 个迁移
2024-01-15 10:30:03 - INFO - 总耗时: 2.45 秒
2024-01-15 10:30:03 - INFO - ==================================================
2024-01-15 10:30:03 - INFO - 数据库连接已关闭
```

### 查看状态的输出

```
2024-01-15 10:31:00 - INFO - ==================================================
2024-01-15 10:31:00 - INFO - 数据库迁移状态
2024-01-15 10:31:00 - INFO - ==================================================
2024-01-15 10:31:00 - INFO - 迁移统计:
2024-01-15 10:31:00 - INFO -   已执行迁移数量: 3
2024-01-15 10:31:00 - INFO -   可用迁移数量: 3
2024-01-15 10:31:00 - INFO -   待处理迁移数量: 0
2024-01-15 10:31:00 - INFO - 
2024-01-15 10:31:00 - INFO - 已执行的迁移版本:
2024-01-15 10:31:00 - INFO -   ✓ 001
2024-01-15 10:31:00 - INFO -   ✓ 002
2024-01-15 10:31:00 - INFO -   ✓ 003
2024-01-15 10:31:00 - INFO - 
2024-01-15 10:31:00 - INFO - ✓ 所有迁移都已执行完成
2024-01-15 10:31:00 - INFO - 
2024-01-15 10:31:00 - INFO - 数据库连接状态:
2024-01-15 10:31:00 - INFO -   连接池初始化: 是
2024-01-15 10:31:00 - INFO -   当前连接数: 5
2024-01-15 10:31:00 - INFO -   空闲连接数: 5
2024-01-15 10:31:00 - INFO -   连接池范围: 5-20
2024-01-15 10:31:00 - INFO - ==================================================
```

## 错误处理

脚本会自动处理各种错误情况：

1. **数据库连接失败**：显示详细错误信息和解决建议
2. **迁移执行失败**：自动回滚事务，保证数据一致性
3. **用户中断**：正确清理资源并退出

## 集成到CI/CD

可以在部署脚本中使用静默模式：

```bash
# 在部署脚本中
if python migrate.py migrate --quiet; then
    echo "数据库迁移成功"
else
    echo "数据库迁移失败，停止部署"
    exit 1
fi
```

## 注意事项

1. **备份数据**：在生产环境执行迁移前，请务必备份数据库
2. **测试连接**：建议先使用 `test` 命令确认数据库连接正常
3. **查看状态**：使用 `status` 命令了解当前迁移状态
4. **环境配置**：确保 `.env` 文件中的数据库配置正确

## 帮助信息

```bash
python migrate.py --help
```

显示完整的命令行帮助信息。 
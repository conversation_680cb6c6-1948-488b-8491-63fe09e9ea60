#!/usr/bin/env python3
"""
育儿日记分析工具测试脚本

测试新的分析功能是否能正确处理育儿相关和无关内容
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from tools.analyze_parenting_journal import analyze_parenting_journal, generate_golden_quote

async def test_parenting_related_content():
    """测试育儿相关内容分析"""
    print("🧪 测试1: 育儿相关内容分析")
    print("=" * 50)
    
    # 育儿相关的测试文本
    test_journal = """
    今天晚上8点，我让小明写作业，但他一直拖拖拉拉，趴在桌子上不肯动笔。
    我反复催促了好几次，但他还是磨蹭。最后我有点生气，声音提高了，
    小明就开始哭了。我看到他委屈的样子，心里也很内疚。
    后来我坐下来陪他，耐心地引导他一步步完成作业。
    我觉得以后要更有耐心，少一些催促，多一些理解。
    """
    
    try:
        result = await analyze_parenting_journal(test_journal)
        parsed_result = json.loads(result)
        
        print("✅ 分析成功")
        print(f"📋 内容类型: {parsed_result.get('static_metadata', {}).get('content_type', '未知')}")
        
        if parsed_result.get('parenting_analysis'):
            print("📝 育儿分析模块已填充")
        if parsed_result.get('general_content_analysis') is None:
            print("📝 通用分析模块为空 (符合预期)")
            
        print("\n📊 分析结果预览:")
        if parsed_result.get('parenting_analysis'):
            event_desc = parsed_result['parenting_analysis'].get('event_description', {})
            print(f"  事件标题: {event_desc.get('event_title', 'N/A')}")
            print(f"  事件摘要: {event_desc.get('event_abstract', 'N/A')[:50]}...")
            
        print(f"\n原始结果:\n{json.dumps(parsed_result, ensure_ascii=False, indent=2)[:500]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")

async def test_non_parenting_content():
    """测试非育儿相关内容分析"""
    print("🧪 测试2: 非育儿相关内容分析")
    print("=" * 50)
    
    # 非育儿相关的测试文本
    test_text = """
    今天项目会议很紧张，团队讨论了新的产品功能需求。
    我们需要在下周五之前完成开发计划，但时间很紧迫。
    产品经理提出了几个关键性的改进点，技术团队需要评估可行性。
    我觉得这次项目管理得更加高效，沟通也比较顺畅。
    希望能按时交付高质量的产品。
    """
    
    try:
        result = await analyze_parenting_journal(test_text)
        parsed_result = json.loads(result)
        
        print("✅ 分析成功")
        print(f"📋 内容类型: {parsed_result.get('static_metadata', {}).get('content_type', '未知')}")
        
        if parsed_result.get('general_content_analysis'):
            print("📝 通用分析模块已填充")
        if parsed_result.get('parenting_analysis') is None:
            print("📝 育儿分析模块为空 (符合预期)")
            
        print("\n📊 分析结果预览:")
        if parsed_result.get('general_content_analysis'):
            general_analysis = parsed_result['general_content_analysis']
            print(f"  摘要: {general_analysis.get('summary', 'N/A')[:50]}...")
            print(f"  主要话题: {general_analysis.get('main_topics', [])}")
            print(f"  整体情绪: {general_analysis.get('overall_sentiment', 'N/A')}")
            print(f"  内容类别: {general_analysis.get('content_category', 'N/A')}")
            
        print(f"\n原始结果:\n{json.dumps(parsed_result, ensure_ascii=False, indent=2)[:500]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")

async def test_golden_quote_generation():
    """测试金句生成功能"""
    print("🧪 测试3: 金句生成功能")
    print("=" * 50)
    
    # 育儿相关的测试文本
    test_journal = """
    今天小朋友在画画时特别专注，一画就是两个小时。
    我本来想提醒他休息一下，但看到他那么投入，就没有打扰。
    后来他完成了一幅很棒的作品，眼中满是成就感。
    我觉得有时候不干扰孩子的专注状态，比什么都重要。
    """
    
    try:
        result = await generate_golden_quote(test_journal)
        parsed_result = json.loads(result)
        
        print("✅ 金句生成成功")
        print(f"📝 金句内容: {parsed_result.get('golden_quote', 'N/A')}")
        print(f"📏 金句长度: {parsed_result.get('quote_length', 0)}字")
        print(f"✓ 长度有效: {parsed_result.get('is_valid_length', False)}")
        print(f"✓ 无人称代词: {not parsed_result.get('contains_pronouns', True)}")
        
        print(f"\n原始结果:\n{json.dumps(parsed_result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")

async def main():
    """主测试函数"""
    print("🚀 开始测试育儿日记分析工具")
    print("使用新的 prompt 规范进行测试\n")
    
    # 运行所有测试
    await test_parenting_related_content()
    await test_non_parenting_content()
    await test_golden_quote_generation()
    
    print("🎉 所有测试完成!")
    print("\n📚 使用说明:")
    print("1. 育儿相关内容会填充 parenting_analysis 模块")
    print("2. 非育儿内容会填充 general_content_analysis 模块")
    print("3. 根据 content_type 自动设置对应模块为空")
    print("4. 金句生成功能保持独立运行")

if __name__ == "__main__":
    asyncio.run(main()) 
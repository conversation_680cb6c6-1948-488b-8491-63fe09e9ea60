#!/usr/bin/env python3
"""
依赖包检查脚本

检查 common 服务中用到的所有关键包是否已正确安装
"""

import importlib
import sys
from typing import List, Tuple, Dict

def check_package(package_name: str, import_name: str = None) -> Tu<PERSON>[bool, str]:
    """
    检查包是否已安装并可正常导入
    
    Args:
        package_name: 包名称
        import_name: 导入名称（如果与包名不同）
        
    Returns:
        Tuple[bool, str]: (是否成功, 错误信息或版本信息)
    """
    try:
        module_name = import_name or package_name
        module = importlib.import_module(module_name)
        
        # 尝试获取版本信息
        version = "未知版本"
        if hasattr(module, '__version__'):
            version = module.__version__
        elif hasattr(module, 'VERSION'):
            version = module.VERSION
        elif hasattr(module, 'version'):
            version = module.version
        
        return True, version
    except ImportError as e:
        return False, str(e)
    except Exception as e:
        return False, f"导入错误: {str(e)}"

def main():
    """检查所有依赖包"""
    print("🔍 检查 common 服务依赖包...")
    print("=" * 60)
    
    # 定义需要检查的包
    packages_to_check: List[Tuple[str, str, str]] = [
        # (包名, 导入名, 描述)
        ("fastapi", "fastapi", "FastAPI web框架"),
        ("uvicorn", "uvicorn", "ASGI服务器"),
        ("pydantic", "pydantic", "数据验证库"),
        ("aiohttp", "aiohttp", "异步HTTP客户端"),
        ("requests", "requests", "HTTP客户端库"),
        ("PyJWT", "jwt", "JWT认证库"),
        ("pycryptodome", "Crypto", "加密算法库（闪验服务需要）"),
        ("qcloud-python-sts", "sts", "腾讯云STS SDK"),
        ("asyncpg", "asyncpg", "PostgreSQL异步驱动"),
        ("python-multipart", "multipart", "多部分表单解析"),
    ]
    
    # 检查结果统计
    success_count = 0
    failed_packages = []
    
    print(f"{'包名':<20} {'状态':<10} {'版本/错误信息'}")
    print("-" * 60)
    
    for package_name, import_name, description in packages_to_check:
        is_available, info = check_package(package_name, import_name)
        
        if is_available:
            status = "✅ 已安装"
            success_count += 1
            print(f"{package_name:<20} {status:<10} {info}")
        else:
            status = "❌ 缺失"
            failed_packages.append((package_name, description, info))
            print(f"{package_name:<20} {status:<10} {info}")
    
    print("=" * 60)
    print(f"📊 检查结果: {success_count}/{len(packages_to_check)} 个包已正确安装")
    
    if failed_packages:
        print("\n❌ 缺失的包:")
        for package_name, description, error in failed_packages:
            print(f"  - {package_name}: {description}")
            print(f"    错误: {error}")
        
        print("\n🔧 修复建议:")
        print("1. 安装缺失的包:")
        print("   pip install -r requirements.txt")
        print("\n2. 如果仍有问题，请手动安装:")
        for package_name, _, _ in failed_packages:
            print(f"   pip install {package_name}")
        
        print("\n3. 重新检查:")
        print("   python scripts/check_dependencies.py")
        
        return 1
    else:
        print("\n🎉 所有依赖包都已正确安装！")
        
        print("\n📚 下一步:")
        print("1. 配置 PM2 日志轮转:")
        print("   ./scripts/setup_pm2_logrotate.sh")
        print("\n2. 启动服务:")
        print("   pm2 start ecosystem.config.js")
        
        return 0

if __name__ == "__main__":
    sys.exit(main()) 
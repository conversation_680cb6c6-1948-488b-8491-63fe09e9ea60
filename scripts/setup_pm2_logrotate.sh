#!/bin/bash

# PM2 日志轮转配置脚本
# 用于设置日志按天分割，只保留3天的日志

echo "🔧 配置 PM2 日志轮转..."

# 检查 PM2 是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ 错误: PM2 未安装，请先安装 PM2"
    echo "安装命令: npm install -g pm2"
    exit 1
fi

echo "📦 安装 pm2-logrotate 模块..."
pm2 install pm2-logrotate

# 等待模块安装完成
sleep 3

echo "⚙️  配置日志轮转参数..."

# 设置单个日志文件最大大小为 50MB
pm2 set pm2-logrotate:max_size 50M
echo "✅ 设置最大文件大小: 50MB"

# 保留最近3天的日志
pm2 set pm2-logrotate:retain 3
echo "✅ 设置保留天数: 3天"

# 启用日志压缩
pm2 set pm2-logrotate:compress true
echo "✅ 启用日志压缩"

# 设置日期格式
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD
echo "✅ 设置日期格式: YYYY-MM-DD"

# 轮转模块自身日志
pm2 set pm2-logrotate:rotateModule true
echo "✅ 启用模块自身日志轮转"

# 设置检查间隔为30秒
pm2 set pm2-logrotate:workerInterval 30
echo "✅ 设置检查间隔: 30秒"

# 设置每天午夜轮转 (cron格式)
pm2 set pm2-logrotate:rotateInterval '0 0 * * *'
echo "✅ 设置轮转时间: 每天午夜"

# 显示当前配置
echo ""
echo "📋 当前日志轮转配置:"
pm2 conf pm2-logrotate

echo ""
echo "🎉 PM2 日志轮转配置完成！"
echo ""
echo "📚 使用说明:"
echo "- 日志文件超过 50MB 或每天午夜会自动轮转"
echo "- 只保留最近 3 天的日志文件"
echo "- 旧日志文件会自动压缩"
echo "- 检查配置: pm2 conf pm2-logrotate"
echo "- 手动轮转: pm2 flush"
echo ""
echo "🔍 查看日志:"
echo "- 查看所有日志: pm2 logs"
echo "- 查看特定服务: pm2 logs mcp-server"
echo "- 实时日志: pm2 logs --lines 100" 
#!/usr/bin/env python3
"""
测试更多洞察API接口

这个脚本用于测试新增的更多洞察接口功能，验证：
1. 从用户记录中获取原始故事
2. 从insights表中获取首次洞察结果  
3. 调用深度洞察模式的MCP工具
4. 返回规范的API响应格式
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager
from database.config.database import DatabaseConfig
from database.models.user_record import UserRecord
from database.models.insight import Insight
from controllers.parent_child_relationship.api.analysis import AnalysisService
from uuid import uuid4

async def test_more_insight_api():
    """测试更多洞察API的完整流程"""
    
    # 初始化数据库
    config = DatabaseConfig()
    db_manager = DatabaseManager(config)
    await db_manager.initialize()
    
    # 创建分析服务实例
    analysis_service = AnalysisService(db_manager)
    
    try:
        async with db_manager.get_session() as session:
            print("🧪 开始测试更多洞察API...")
            
            # 1. 创建测试用户记录（模拟原始故事）
            test_record_id = str(uuid4())
            test_user_id = "test_user_more_insight"
            
            test_record = UserRecord(
                id=test_record_id,
                user_id=test_user_id,
                content="今天我家娃在公园非要抢别的小朋友的秋千，我说要排队，但他就是不听，赖在地上不走。后来为了抢秋千，还伸手推了那个小朋友一下。我当时就制止了，但很担心，这种行为会不会越来越严重？",
                content_type="text"
            )
            await test_record.save(session)
            print(f"✅ 创建测试记录: {test_record_id}")
            
            # 2. 创建测试洞察卡片（模拟首次洞察）
            test_insight_id = str(uuid4())
            first_insight_content = {
                "insight_title": "\"不\"的背后，是自主意识的萌芽",
                "insight_content": "公园里的坚持，往往不是简单的'不听话'。当孩子开始强烈地表达'我要'时，正是他自主意识和自我主张的体现。他正在用自己的方式探索世界的规则和个人边界，这是成长的必经之路。",
                "advice_items": [
                    "尝试提前预告，在去公园的路上就和他约定好轮流玩的规则。",
                    "给予选择权，例如'我们是先玩滑梯还是先玩秋千？'让他感觉被尊重。"
                ]
            }
            
            test_insight = Insight(
                id=test_insight_id,
                user_id=test_user_id,
                record_id=test_record_id,
                content=first_insight_content,
                status=1  # 收藏状态
            )
            await test_insight.save(session)
            print(f"✅ 创建测试洞察: {test_insight_id}")
            
            # 3. 测试更多洞察API
            user_question = "道理我懂，但他后来为了抢秋千，还伸手推了那个小朋友一下。我当时就制止了，但很担心，这是不是攻击性行为的开始？我该怎么教他正确地表达需求？"
            
            print(f"🔍 测试深度洞察生成...")
            print(f"   原始故事: {test_record.content[:50]}...")
            print(f"   用户追问: {user_question[:50]}...")
            
            result = await analysis_service.generate_more_insight(
                user_id=test_user_id,
                record_id=test_record_id,
                user_question=user_question
            )
            
            print(f"✅ 深度洞察生成完成!")
            print(f"   结果类型: {type(result)}")
            
            # 显示结果
            if isinstance(result, dict):
                if "error" in result:
                    print(f"❌ 生成失败: {result.get('error')}")
                    print(f"   错误详情: {result.get('error_details', 'N/A')}")
                else:
                    print(f"📊 深度洞察结果:")
                    print(f"   标题: {result.get('insight_title', 'N/A')}")
                    print(f"   内容: {result.get('insight_content', 'N/A')[:100]}...")
                    print(f"   建议数量: {len(result.get('advice_items', []))}")
                    print(f"   模式: {result.get('mode', 'N/A')}")
                    
                    if "validation" in result:
                        validation = result["validation"]
                        print(f"   验证状态: {'✅ 通过' if validation.get('is_valid') else '❌ 未通过'}")
                        if validation.get('issues'):
                            print(f"   验证问题: {validation['issues']}")
            else:
                print(f"⚠️  返回结果格式异常: {result}")
            
            # 4. 清理测试数据
            print(f"🧹 清理测试数据...")
            await session.execute(
                "DELETE FROM insights WHERE id = $1", test_insight_id
            )
            await session.execute(
                "DELETE FROM user_records WHERE id = $1", test_record_id
            )
            print(f"✅ 测试数据已清理")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        await db_manager.close()
        print(f"🏁 测试完成")

async def test_error_scenarios():
    """测试错误场景"""
    
    config = DatabaseConfig()
    db_manager = DatabaseManager(config)
    await db_manager.initialize()
    
    analysis_service = AnalysisService(db_manager)
    
    try:
        print(f"\n🧪 测试错误场景...")
        
        # 测试不存在的记录ID
        try:
            await analysis_service.generate_more_insight(
                user_id="test_user",
                record_id="non_existent_record",
                user_question="测试问题"
            )
            print(f"❌ 应该抛出记录不存在异常")
        except Exception as e:
            print(f"✅ 正确处理不存在记录: {str(e)}")
        
        print(f"✅ 错误场景测试完成")
        
    finally:
        await db_manager.close()

if __name__ == "__main__":
    print(f"🚀 启动更多洞察API测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行主要测试
    asyncio.run(test_more_insight_api())
    
    # 运行错误场景测试
    asyncio.run(test_error_scenarios())
    
    print(f"🎉 所有测试完成!") 
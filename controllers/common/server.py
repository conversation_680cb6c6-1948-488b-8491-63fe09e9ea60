"""
Common服务独立HTTP服务器

提供公共服务API，包括短信验证码认证、闪验一键登录等功能
使用FastAPI框架，统一使用/api前缀
"""

import os
import sys
import asyncio
import logging
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import uvicorn
from typing import Dict, Any
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 修复导入问题 - 使用绝对导入
try:
    # 尝试相对导入（当作为模块运行时）
    from .api import auth_router, log_requests, member_router, cos_router, cos_file_router, pages_router
    from .services import cleanup_sms_service, cleanup_flash_service, get_sms_service, get_jwt_service, get_flash_service, get_cos_sts_service
except ImportError:
    # 回退到绝对导入（当直接运行时）
    from controllers.common.api import auth_router, log_requests, member_router, cos_router, cos_file_router, pages_router
    from controllers.common.services import cleanup_sms_service, cleanup_flash_service, get_sms_service, get_jwt_service, get_flash_service, get_cos_sts_service

from config.settings import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/common_service.log')
    ]
)
logger = logging.getLogger(__name__)

# 配置静态文件目录
current_dir = os.path.dirname(__file__)
static_dir = os.path.join(current_dir, "static")

# 确保静态文件目录存在
os.makedirs(static_dir, exist_ok=True)
os.makedirs(os.path.join(static_dir, "css"), exist_ok=True)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("🚀 Common服务启动中...")
    
    # 预热服务
    try:
        # 使用统一配置初始化服务
        get_sms_service()  # 短信服务
        get_jwt_service()  # JWT服务
        get_flash_service()  # 闪验服务
        get_cos_sts_service()  # 腾讯云COS STS服务
        
        logger.info("✅ 服务初始化完成")
        logger.info(f"📊 配置信息: {settings.get_info()}")
    except Exception as e:
        logger.error(f"❌ 服务初始化失败: {str(e)}")
    
    yield
    
    # 应用关闭时的清理
    logger.info("🛑 Common服务关闭中...")
    try:
        await cleanup_sms_service()
        await cleanup_flash_service()
        logger.info("✅ 资源清理完成")
    except Exception as e:
        logger.error(f"❌ 资源清理失败: {str(e)}")

# 创建FastAPI应用
app = FastAPI(
    title="工禧云 - Common公共服务",
    description="提供短信验证码认证、闪验一键登录、JWT令牌管理等公共服务功能",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error": str(exc),
            "timestamp": "2024-01-01T00:00:00.000Z"
        }
    )

# 健康检查路由
@app.get("/health")
async def health_check():
    """服务健康检查"""
    return {
        "success": True,
        "message": "Common公共服务运行正常",
        "data": {
            "service": "common-service",
            "status": "healthy",
            "version": "1.0.0",
            "config": {
                "sms_configured": bool(settings.sms.account and settings.sms.password),
                "flash_configured": settings.flash.is_configured(),
                "jwt_configured": settings.jwt.secret_key != "your-secret-key-here",
                "cos_sts_configured": bool(os.getenv("TENCENT_SECRET_ID") and os.getenv("TENCENT_SECRET_KEY"))
            }
        },
        "timestamp": "2024-01-01T00:00:00.000Z"
    }



# 服务信息路由
@app.get("/api/info")
async def service_info():
    """获取服务信息"""
    return {
        "success": True,
        "message": "Common公共服务信息",
        "data": {
            "name": "工禧云Common公共服务",
            "version": "1.0.0",
            "description": "提供短信验证码认证、闪验一键登录、JWT令牌管理等功能",
            "features": [
                "短信验证码发送",
                "手机号验证码登录",
                "闪验一键登录（移动端）",
                "闪验本机校验（Web端）",
                "JWT令牌生成和验证",
                "用户信息管理",
                "频率限制",
                "腾讯云COS临时密钥生成"
            ],
            "endpoints": {
                "auth": {
                    "send_code": "POST /api/auth/send-code",
                    "verify_login": "POST /api/auth/verify-login",
                    "flash_login": "POST /api/auth/flash-login",
                    "flash_validate": "POST /api/auth/flash-validate",
                    "refresh_token": "POST /api/auth/refresh-token",
                    "user_info": "GET /api/auth/user-info",
                    "health": "GET /api/auth/health"
                },
                "cos": {
                    "status": "GET /api/cos/status",
                    "temp_credentials": "POST /api/cos/temp-credentials",
                    "quick_upload": "POST /api/cos/quick-upload-credentials"
                },
                "cos_file": {
                    "save": "POST /api/cos-file/save",
                    "get": "GET /api/cos-file/get-by-file-key",
                    "my_files": "GET /api/cos-file/my-files",
                    "health": "GET /api/cos-file/health"
                }
            },
            "config": settings.get_info()
        },
        "timestamp": "2024-01-01T00:00:00.000Z"
    }

# 注册路由
app.include_router(auth_router)
app.include_router(member_router)
app.include_router(cos_router)
app.include_router(cos_file_router)
app.include_router(pages_router)  # H5页面路由（用户协议、隐私协议等）


# 注册中间件：请求日志记录
app.middleware("http")(log_requests)

def create_app() -> FastAPI:
    """创建应用实例（用于测试）"""
    return app

def run_server(
    host: str = None,
    port: int = None,
    reload: bool = None,
    log_level: str = None
):
    """
    启动Common服务器
    
    Args:
        host: 服务器地址，默认使用配置文件
        port: 服务器端口，默认使用配置文件
        reload: 是否开启自动重载，默认使用配置文件
        log_level: 日志级别，默认使用配置文件
    """
    # 使用配置文件的默认值
    config = settings.common_service
    host = host or config.host
    port = port or config.port
    reload = reload if reload is not None else config.reload
    log_level = log_level or config.log_level
    
    logger.info(f"🌟 启动Common公共服务...")
    logger.info(f"📡 服务地址: http://{host}:{port}")
    logger.info(f"📚 API文档: http://{host}:{port}/docs")
    logger.info(f"🔧 ReDoc文档: http://{host}:{port}/redoc")
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    try:
        uvicorn.run(
            "controllers.common.server:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("👋 Common服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {str(e)}")
        raise

if __name__ == "__main__":
    run_server() 
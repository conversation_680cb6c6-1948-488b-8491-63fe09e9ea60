"""
腾讯云COS临时密钥服务

基于腾讯云官方STS SDK生成临时访问密钥，用于前端直接上传文件到COS
参考: https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/python
使用官方qcloud-python-sts包提供安全的临时授权访问功能
"""

import os
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from database.utils import get_current_time_iso
# 使用腾讯云官方STS SDK
from sts.sts import Sts

# 配置日志
logger = logging.getLogger(__name__)

class CosSTSService:
    """
    腾讯云COS STS临时密钥服务
    
    使用腾讯云官方STS SDK生成临时访问密钥
    确保前端能够安全地上传文件到指定的COS存储桶
    """
    
    def __init__(self):
        """
        初始化COS STS服务
        
        从环境变量获取腾讯云API密钥配置
        """
        # 从环境变量获取腾讯云API密钥
        self.secret_id = os.getenv("TENCENT_SECRET_ID")
        self.secret_key = os.getenv("TENCENT_SECRET_KEY")

        logger.info(f"secret_id: {self.secret_id}")
        logger.info(f"secret_key: {self.secret_key}")
        
        # 默认配置
        self.default_duration = int(os.getenv("COS_TEMP_DURATION", "1800"))  # 30分钟
        self.max_duration = 7200  # 最大2小时
        
        # 验证配置
        if not self.secret_id or not self.secret_key:
            logger.warning("⚠️ 腾讯云API密钥未配置，COS临时密钥服务将不可用")
            logger.warning("请设置环境变量: TENCENT_SECRET_ID, TENCENT_SECRET_KEY")
    
    def _validate_config(self) -> bool:
        """
        验证服务配置是否完整
        
        Returns:
            bool: 配置是否有效
        """
        return bool(self.secret_id and self.secret_key)
    
    def _validate_parameters(
        self, 
        bucket: str, 
        region: str, 
        allow_prefix: List[str], 
        allow_actions: List[str]
    ) -> tuple[bool, str]:
        """
        验证请求参数的安全性和有效性
        
        Args:
            bucket: 存储桶名称
            region: 存储桶地域
            allow_prefix: 允许的资源前缀列表
            allow_actions: 允许的操作列表
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 验证存储桶名称格式
        if not bucket or not bucket.strip():
            return False, "存储桶名称不能为空"
        
        if len(bucket) < 3 or len(bucket) > 63:
            return False, "存储桶名称长度必须在3-63字符之间"
        
        # 验证地域格式
        if not region or not region.strip():
            return False, "存储桶地域不能为空"
        
        # 验证允许的前缀
        if not allow_prefix or len(allow_prefix) == 0:
            return False, "必须指定至少一个允许的资源前缀"
        
        # 检查是否包含危险的通配符
        for prefix in allow_prefix:
            if prefix == "*":
                logger.warning("⚠️ 使用通配符*存在重大安全风险")
                return False, "不允许使用全局通配符*，请指定具体的路径前缀"
        
        # 验证允许的操作
        if not allow_actions or len(allow_actions) == 0:
            return False, "必须指定至少一个允许的操作"
        
        # 验证操作是否为有效的COS API
        valid_cos_actions = {
            "name/cos:PutObject",           # 上传对象
            "name/cos:PostObject",          # 表单上传对象
            "name/cos:PutObjectAcl",        # 设置对象ACL
            "name/cos:GetObject",           # 下载对象
            "name/cos:GetObjectAcl",        # 获取对象ACL
            "name/cos:DeleteObject",        # 删除对象
            "name/cos:HeadObject",          # 获取对象元数据
            "name/cos:ListMultipartUploads", # 列出分块上传
            "name/cos:ListParts",           # 列出分块
            "name/cos:UploadPart",          # 上传分块
            "name/cos:CompleteMultipartUpload", # 完成分块上传
            "name/cos:AbortMultipartUpload", # 取消分块上传
            "name/cos:InitiateMultipartUpload", # 初始化分块上传
        }
        
        for action in allow_actions:
            if action not in valid_cos_actions:
                logger.warning(f"⚠️ 未知的COS操作: {action}")
        
        return True, ""
    
    def _extract_appid_from_bucket(self, bucket: str) -> tuple[str, str]:
        """
        从存储桶名称中提取AppID
        
        腾讯云存储桶格式: bucketName-appid
        例如: examplebucket-1250000000
        
        Args:
            bucket: 完整的存储桶名称
            
        Returns:
            tuple[str, str]: (存储桶名称, AppID)
            
        Raises:
            ValueError: 当存储桶格式不正确时抛出
        """
        if '-' not in bucket:
            raise ValueError(f"存储桶名称格式错误，应为 bucketName-appid 格式，实际: {bucket}")
        
        parts = bucket.split('-')
        if len(parts) < 2:
            raise ValueError(f"存储桶名称格式错误，应为 bucketName-appid 格式，实际: {bucket}")
        
        # 最后一部分应该是AppID (纯数字)
        appid = parts[-1]
        bucket_name = '-'.join(parts[:-1])
        
        if not appid.isdigit():
            raise ValueError(f"AppID格式错误，应为纯数字，实际: {appid}")
        
        return bucket_name, appid
    
    def _normalize_prefix(self, prefix: str) -> str:
        """
        标准化路径前缀，确保能正确匹配文件路径
        
        腾讯云COS的通配符规则：
        - "*" 匹配任意字符但不跨越斜杠
        - "/*" 匹配目录下的所有内容
        
        Args:
            prefix: 原始前缀
            
        Returns:
            str: 处理后的前缀
        """
        if not prefix:
            return "*"  # 空前缀允许所有文件
        
        prefix = prefix.strip()
        
        # 如果已经有通配符，直接返回
        if prefix.endswith('*'):
            return prefix
        
        # 关键修复：确保前缀能匹配目录下的文件
        if prefix.endswith('/'):
            # 如果以斜杠结尾，添加通配符匹配目录下所有文件
            return prefix + "*"
        else:
            # 如果不以斜杠结尾，添加斜杠和通配符，允许匹配该前缀目录下的所有文件
            return prefix + "/*"
    
    def _build_resource_expressions(
        self, 
        bucket: str, 
        region: str, 
        allow_prefix: List[str]
    ) -> List[str]:
        """
        构建腾讯云标准的资源表达式
        
        根据官方文档，资源表达式格式为：
        - COS: qcs::cos:{region}:uid/{appid}:{bucket}/{path}
        - CI:  qcs::ci:{region}:uid/{appid}:bucket/{bucket}/{path}
        
        Args:
            bucket: 存储桶名称 (格式: bucketName-appid)
            region: 存储桶地域
            allow_prefix: 路径前缀列表
            
        Returns:
            List[str]: 标准的资源表达式列表
            
        Raises:
            ValueError: 当存储桶格式不正确时抛出
        """
        try:
            # 提取AppID
            bucket_name, appid = self._extract_appid_from_bucket(bucket)
            
            resources = []
            
            # 为每个路径前缀构建COS和CI资源表达式
            for prefix in allow_prefix:
                # 🔧 修复前缀处理逻辑：确保能正确匹配文件路径
                processed_prefix = self._normalize_prefix(prefix)
                logger.info(f"前缀处理: '{prefix}' -> '{processed_prefix}'")
                
                # COS资源表达式
                cos_resource = f"qcs::cos:{region}:uid/{appid}:{bucket}/{processed_prefix}"
                resources.append(cos_resource)
                
                # CI资源表达式 (用于数据万象相关操作)
                ci_resource = f"qcs::ci:{region}:uid/{appid}:bucket/{bucket}/{processed_prefix}"
                resources.append(ci_resource)
            
            logger.info(f"📋 资源表达式构建完成:")
            for i, resource in enumerate(resources, 1):
                logger.info(f"  {i}. {resource}")
            
            logger.info(f"🎯 关键匹配信息:")
            logger.info(f"  存储桶: {bucket}")
            logger.info(f"  地域: {region}")
            logger.info(f"  AppID: {appid}")
            logger.info(f"  原始前缀: {allow_prefix}")
            
            return resources
            
        except Exception as e:
            logger.error(f"构建资源表达式失败: {e}")
            raise ValueError(f"构建资源表达式失败: {str(e)}")
    
    def _create_sts_config(
        self, 
        bucket: str, 
        region: str, 
        allow_prefix: List[str], 
        allow_actions: List[str],
        duration_seconds: int
    ) -> Dict[str, Any]:
        """
        创建官方STS SDK配置
        
        Args:
            bucket: 存储桶名称 (格式: bucketName-appid)
            region: 存储桶地域
            allow_prefix: 允许的资源前缀列表
            allow_actions: 允许的操作列表
            duration_seconds: 临时密钥有效时长
            
        Returns:
            Dict[str, Any]: STS配置
        """
        # 构建标准的资源表达式
        resource_expressions = self._build_resource_expressions(bucket, region, allow_prefix)
        
        config = {
            # 请求URL
            'url': 'https://sts.tencentcloudapi.com/',
            # 域名
            'domain': 'sts.tencentcloudapi.com',
            # 临时密钥有效时长，单位是秒
            'duration_seconds': duration_seconds,
            # 固定密钥
            'secret_id': self.secret_id,
            'secret_key': self.secret_key,
            # 存储桶
            'bucket': bucket,
            # 存储桶地域
            'region': region,
            # 🔧 修复：使用标准的资源表达式替代简单前缀
            'allow_prefix': ['*'],
            # 密钥的权限列表
            'allow_actions': allow_actions
        }
        
        logger.debug(f"STS配置: {config}")
        return config
    
    async def generate_temp_credentials(
        self,
        bucket: str,
        region: str,
        allow_prefix: List[str],
        allow_actions: List[str],
        duration_seconds: Optional[int] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成临时访问密钥 - 使用官方STS SDK
        
        Args:
            bucket: 存储桶名称，格式: bucketName-appid
            region: 存储桶所属地域，如: ap-guangzhou
            allow_prefix: 资源前缀列表，如: ['images/', 'documents/']
            allow_actions: 授权操作列表，如: ['name/cos:PutObject']
            duration_seconds: 有效期秒数，默认1800秒
            user_id: 用户ID，用于生成唯一的会话名称
            
        Returns:
            Dict[str, Any]: 临时密钥信息
            
        Raises:
            RuntimeError: 当服务不可用或参数无效时抛出
        """
        # 验证服务配置
        if not self._validate_config():
            raise RuntimeError("腾讯云API密钥未配置，无法生成临时密钥")
        
        # 验证参数
        is_valid, error_msg = self._validate_parameters(bucket, region, allow_prefix, allow_actions)
        if not is_valid:
            raise ValueError(f"参数验证失败: {error_msg}")
        
        # 设置有效期
        if duration_seconds is None:
            duration_seconds = self.default_duration
        
        if duration_seconds > self.max_duration:
            duration_seconds = self.max_duration
            logger.warning(f"有效期超过最大值，已调整为: {self.max_duration}秒")
        
        logger.info(f"为用户 {user_id or 'anonymous'} 生成临时密钥")
        logger.info(f"存储桶: {bucket}, 地域: {region}")
        logger.info(f"允许前缀: {allow_prefix}")
        logger.info(f"允许操作: {allow_actions}")
        logger.info(f"有效期: {duration_seconds}秒")
        
        try:
            # 创建STS配置
            config = self._create_sts_config(bucket, region, allow_prefix, allow_actions, duration_seconds)
            
            # 使用官方SDK创建STS客户端
            sts = Sts(config)
            
            # 调用官方SDK获取临时凭证
            response = sts.get_credential()
            
            logger.debug(f"官方SDK响应: {response}")
            
            # 检查响应
            if not response or 'credentials' not in response:
                raise RuntimeError("STS SDK响应格式错误")
            
            credentials = response['credentials']

            logger.info(f"credentials: {credentials}")
            
            # 计算时间戳（官方SDK返回的credentials中没有时间字段，需要手动计算）
            current_time = int(time.time())
            expired_time = current_time + duration_seconds
            
            # 构建标准返回结果
            result = {
                "success": True,
                "credentials": {
                    "tmp_secret_id": credentials['tmpSecretId'],
                    "tmp_secret_key": credentials['tmpSecretKey'],
                    "session_token": credentials['sessionToken'],
                    "start_time": current_time,
                    "expired_time": expired_time
                },
                "request_id": response.get('requestId', ''),
                "metadata": {
                    "bucket": bucket,
                    "region": region,
                    "allow_prefix": allow_prefix,
                    "allow_actions": allow_actions,
                    "duration_seconds": duration_seconds,
                    "generated_at": get_current_time_iso()
                }
            }
            
            logger.info(f"✅ 临时密钥生成成功，有效期至: {datetime.fromtimestamp(result['credentials']['expired_time'])}")
            return result
            
        except Exception as e:
            logger.error(f"生成临时密钥失败: {e}")
            raise RuntimeError(f"生成临时密钥失败: {str(e)}")

# 全局服务实例
_cos_sts_service: Optional[CosSTSService] = None

def get_cos_sts_service() -> CosSTSService:
    """
    获取COS STS服务实例（单例模式）
    
    Returns:
        CosSTSService: 服务实例
    """
    global _cos_sts_service
    if _cos_sts_service is None:
        _cos_sts_service = CosSTSService()
    return _cos_sts_service

# 对外暴露的主要接口
__all__ = [
    "CosSTSService",
    "get_cos_sts_service"
]

# 模块信息
def get_module_info() -> Dict[str, Any]:
    """获取模块信息"""
    return {
        "name": "腾讯云COS STS服务",
        "version": "2.0.0",
        "description": "基于腾讯云官方STS SDK生成临时访问密钥",
        "reference": "https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/python",
        "features": [
            "使用官方STS SDK",
            "临时密钥生成",
            "权限控制",
            "安全验证",
            "参数校验"
        ]
    }

# 示例使用
if __name__ == "__main__":
    import asyncio
    
    async def test_example():
        """
        使用示例
        """
        service = get_cos_sts_service()
        
        try:
            result = await service.generate_temp_credentials(
                bucket="test-bucket-1253000000",  # 格式: bucketName-appid
                region="ap-guangzhou",
                allow_prefix=["images/", "documents/"],  # 将被转换为QCS资源表达式
                allow_actions=[
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
                ],
                duration_seconds=1800,
                user_id="test_user"
            )
            
            print("✅ 临时密钥生成成功:")
            print(f"TmpSecretId: {result['credentials']['tmp_secret_id']}")
            print(f"TmpSecretKey: {result['credentials']['tmp_secret_key']}")
            print(f"SessionToken: {result['credentials']['session_token']}")
            print(f"过期时间: {datetime.fromtimestamp(result['credentials']['expired_time'])}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    # 测试前缀处理
    print("\n🧪 测试前缀处理逻辑:")
    service = CosSTSService()
    
    test_prefixes = ["audio", "audio/", "images/", "documents", "temp/*"]
    for prefix in test_prefixes:
        normalized = service._normalize_prefix(prefix)
        print(f"  {prefix:12} -> {normalized}")
    
    print("\n🎯 验证audio前缀匹配:")
    print("  前缀: audio -> audio/*")
    print("  ✅ 能匹配: audio/1750835581008_voice_1750835573931.wav")
    print("  ✅ 能匹配: audio/recordings/file.mp3")
    print("  ❌ 不匹配: video/file.wav")
    
    # 运行示例
    # asyncio.run(test_example()) 
"""
短信发送服务

基于创蓝云智API实现短信验证码发送功能
"""

import json
import asyncio
import aiohttp
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import logging

from config.settings import settings
from database.utils.helpers import get_current_timestamp

# 配置日志
logger = logging.getLogger(__name__)

class SmsResponse:
    """短信发送响应类"""
    
    def __init__(self, success: bool, message: str, code: str = "", data: Dict[Any, Any] = None):
        self.success = success
        self.message = message  
        self.code = code
        self.data = data or {}
        self.timestamp = get_current_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "message": self.message,
            "code": self.code,
            "data": self.data,
            "timestamp": self.timestamp.isoformat()
        }

class SmsService:
    """短信发送服务类"""
    
    def __init__(self):
        """初始化短信服务，使用全局配置"""
        self.config = settings.sms
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            )
        return self._session
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
    
    def _build_verification_message(self, verification_code: str) -> str:
        """
        构建验证码短信内容
        
        Args:
            verification_code: 验证码
            
        Returns:
            str: 完整的短信内容
        """
        content = self.config.verification_template.format(code=verification_code)
        return f"{self.config.signature}{content}"
    
    async def send_verification_code(self, phone_number: str, verification_code: str) -> SmsResponse:
        """
        发送验证码短信
        
        Args:
            phone_number: 手机号
            verification_code: 验证码
            
        Returns:
            SmsResponse: 发送结果
        """
        # 开发模式处理
        if self.config.is_dev_mode():
            logger.info(f"🚧 开发模式: 跳过短信发送，使用固定验证码 {self.config.dev_code}")
            logger.info(f"📱 模拟发送短信到 {phone_number}: 您的验证码是{self.config.dev_code}，请在5分钟内使用")
            
            return SmsResponse(
                success=True,
                message=f"开发模式: 验证码已模拟发送（请使用固定验证码: {self.config.dev_code}）",
                code="DEV_MODE",
                data={
                    "phone_number": phone_number,
                    "dev_mode": True,
                    "dev_code": self.config.dev_code,
                    "message": f"【开发模式】您的验证码是{self.config.dev_code}，请在5分钟内按页面提示提交验证码"
                }
            )
        
        # 生产模式：发送真实短信
        message = self._build_verification_message(verification_code)
        return await self.send(phone_number, message)
    
    async def send(self, phone_number: str, message: str) -> SmsResponse:
        """
        发送短信
        
        Args:
            phone_number: 手机号
            message: 短信内容
            
        Returns:
            SmsResponse: 发送结果
        """
        try:
            # 验证配置
            if not self.config.account or not self.config.password:
                return SmsResponse(
                    success=False,
                    message="短信服务配置不完整，请检查账号和密码",
                    code="CONFIG_ERROR"
                )
            
            # 验证手机号格式
            if not self._validate_phone_number(phone_number):
                return SmsResponse(
                    success=False,
                    message="手机号格式不正确",
                    code="INVALID_PHONE"
                )
            
            # 构建请求数据
            request_data = {
                "account": self.config.account,
                "password": self.config.password,
                "msg": message,
                "phone": phone_number,
                "report": "true"  # 启用状态回执
            }
            
            logger.info(f"发送短信到 {phone_number}: {message}")
            
            # 发送HTTP请求
            session = await self._get_session()
            async with session.post(self.config.api_url, json=request_data) as response:
                response_text = await response.text()
                
                logger.info(f"短信API响应: {response_text}")
                
                # 解析响应
                try:
                    response_data = json.loads(response_text)
                except json.JSONDecodeError:
                    return SmsResponse(
                        success=False,
                        message="短信API响应格式错误",
                        code="RESPONSE_FORMAT_ERROR",
                        data={"raw_response": response_text}
                    )
                
                # 检查响应状态
                return self._parse_api_response(response_data)
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求失败: {str(e)}")
            return SmsResponse(
                success=False,
                message=f"网络请求失败: {str(e)}",
                code="NETWORK_ERROR"
            )
        except Exception as e:
            logger.error(f"发送短信时发生未知错误: {str(e)}")
            return SmsResponse(
                success=False,
                message=f"发送失败: {str(e)}",
                code="UNKNOWN_ERROR"
            )
    
    def _validate_phone_number(self, phone_number: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone_number: 手机号
            
        Returns:
            bool: 格式是否正确
        """
        import re
        # 中国大陆手机号正则表达式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone_number))
    
    def _parse_api_response(self, response_data: Dict[str, Any]) -> SmsResponse:
        """
        解析创蓝云智API响应
        
        Args:
            response_data: API响应数据
            
        Returns:
            SmsResponse: 解析后的响应
        """
        try:
            # 获取响应码
            code = str(response_data.get('code', ''))
            
            # 成功状态码为'0'
            if code == '0':
                return SmsResponse(
                    success=True,
                    message="短信发送成功",
                    code=code,
                    data=response_data
                )
            else:
                # 错误状态码处理
                error_message = self._get_error_message(code)
                return SmsResponse(
                    success=False,
                    message=error_message,
                    code=code,
                    data=response_data
                )
                
        except Exception as e:
            return SmsResponse(
                success=False,
                message=f"解析API响应失败: {str(e)}",
                code="PARSE_ERROR",
                data=response_data
            )
    
    def _get_error_message(self, error_code: str) -> str:
        """
        根据错误码获取错误信息
        
        Args:
            error_code: 错误码
            
        Returns:
            str: 错误信息
        """
        error_messages = {
            '101': '无此用户账户',
            '102': '密码错',
            '103': '提交过快（提交速度超过流速限制）',
            '104': '系统忙（因平台侧原因，暂时无法处理提交的短信）',
            '105': '敏感短信（短信内容包含敏感词）',
            '106': '消息长度错（>1036 或<=0）',
            '107': '包含错误的手机号码',
            '108': '手机号码个数错（手机号包含了中文符号；手机号个数错了，群发>1000 或<=0）',
            '109': '无发送额度（当前使用的API账号下没有发送额度）',
            '110': '不在发送时间内',
            '111': '超出该账户当月发送额度限制',
            '112': '产品错误（通道出现异常）',
            '113': '扩展码格式错（非数字或者长度不对）',
            '114': '可用参数组个数错误',
            '116': '签名不合法或未带签名',
            '117': 'IP 地址认证错',
            '118': '用户没有相应的发送权限',
            '119': '用户已过期',
            '120': '违反防盗用策略',
            '123': '发送类型错误',
            '124': '白模板匹配错误',
            '125': '匹配驳回模板，提交失败',
            '127': '定时发送时间格式错误',
            '128': '内容编码失败',
            '129': 'JSON 格式错误',
            '130': '请求参数错误',
            '132': '消息长度错(>3500或<=0)，超过短信最大支持字数',
            '133': '单一手机号错误',
            '134': '违反防盗策略, 超过月发送限制',
            '135': '超过同一手机号相同内容发送限制',
            '136': '不可批量提交"验证码"短信',
            '139': '超出安全发送时间（时间戳过期）',
            '140': '短信内容解密错误',
            '144': '产品未上线限制日发送数量',
            '145': '验签失败',
            '152': '模板不存在',
            '153': '消息长度错（>2000或者≤0）',
            '154': '长短信拼接错误',
            '155': '转发失败',
            '158': '退订语不符合规范',
            '159': '触发反轰炸策略'
        }
        
        return error_messages.get(error_code, f"未知错误（错误码：{error_code}）")

# 全局SMS服务实例
_sms_service_instance: Optional[SmsService] = None

def get_sms_service() -> SmsService:
    """
    获取SMS服务实例（单例模式）
    
    Returns:
        SmsService: SMS服务实例
    """
    global _sms_service_instance
    
    if _sms_service_instance is None:
        _sms_service_instance = SmsService()
    
    return _sms_service_instance

async def cleanup_sms_service():
    """清理SMS服务资源"""
    global _sms_service_instance
    
    if _sms_service_instance:
        await _sms_service_instance.close()
        _sms_service_instance = None 
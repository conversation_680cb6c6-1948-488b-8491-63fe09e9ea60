"""
火山引擎语音识别服务
基于火山引擎录音文件识别极速版API实现音频文件转文字功能
"""
import logging
import requests
import json
import time
import uuid
from typing import Dict, Any, Optional, Union
from config.settings import settings

logger = logging.getLogger(__name__)

class VolcengineService:
    """火山引擎语音识别服务
    
    实现火山引擎录音文件识别极速版API的完整功能：
    1. 提交音频识别任务
    2. 查询识别结果
    3. 完整的文件识别流程
    """

    def __init__(self):
        """初始化火山引擎服务
        
        从配置文件加载必要的认证信息和API配置
        """
        self.config = settings.volcengine
        
        # 检查配置是否完整
        if not self.config.is_configured():
            logger.warning("火山引擎配置不完整，请检查环境变量配置")
            
        # API 基础配置
        self.service_url = self.config.service_url
        self.headers = {
            'Authorization': f'Bearer; {self.config.access_token}',
            'Content-Type': 'application/json'
        }
        
        # 请求会话
        self.session = requests.Session()
        
        logger.info(f"火山引擎服务初始化完成，服务地址: {self.service_url}")
    
    def submit_task(self, audio_url: str, audio_format: str = "wav", 
                   with_speaker_info: bool = False, uid: Optional[str] = None) -> Dict[str, Any]:
        """提交音频识别任务
        
        Args:
            audio_url: 音频文件URL地址（需要可下载的公开链接）
            audio_format: 音频格式 (raw/wav/ogg/mp3/mp4)
            with_speaker_info: 是否需要说话人分离信息
            uid: 用户标识，用于区分不同用户的请求
            
        Returns:
            包含任务ID和状态的响应字典
            
        Raises:
            Exception: 当API调用失败或配置错误时抛出异常
        """
        if not self.config.is_configured():
            raise Exception("火山引擎配置未完成，请检查 VOLCENGINE_APPID、VOLCENGINE_ACCESS_TOKEN、VOLCENGINE_CLUSTER 环境变量")
        
        # 生成唯一的用户ID（如果未提供）
        if uid is None:
            uid = f"user_{uuid.uuid4().hex[:16]}"
            
        # 构建请求参数
        request_data = {
            "app": {
                "appid": self.config.appid,
                "token": self.config.access_token,
                "cluster": self.config.cluster
            },
            "user": {
                "uid": uid
            },
            "audio": {
                "format": audio_format,
                "url": audio_url
            },
            "additions": {
                'with_speaker_info': str(with_speaker_info),
            }
        }
        
        try:
            logger.info(f"提交音频识别任务: {audio_url}")
            
            # 发送POST请求到提交任务API
            response = self.session.post(
                f"{self.service_url}/submit",
                data=json.dumps(request_data),
                headers=self.headers,
                timeout=30
            )
            
            # 解析响应
            response_data = response.json()
            
            if response.status_code != 200:
                logger.error(f"提交任务失败，HTTP状态码: {response.status_code}")
                raise Exception(f"API请求失败: {response.status_code}")
            
            # 检查响应中的错误码
            resp_info = response_data.get('resp', {})
            code = resp_info.get('code')
            message = resp_info.get('message', '')
            
            if code != 1000:
                logger.error(f"任务提交失败，错误码: {code}, 错误信息: {message}")
                raise Exception(f"任务提交失败: {message} (错误码: {code})")
            
            task_id = resp_info.get('id')
            logger.info(f"任务提交成功，任务ID: {task_id}")
            
            return {
                'success': True,
                'task_id': task_id,
                'code': code,
                'message': message,
                'uid': uid
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求异常: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"响应JSON解析失败: {str(e)}")
            raise Exception(f"响应格式错误: {str(e)}")
        except Exception as e:
            logger.error(f"提交任务时发生未知错误: {str(e)}")
            raise

    def query_task(self, task_id: str) -> Dict[str, Any]:
        """查询任务识别结果
        
        Args:
            task_id: 任务ID（由submit_task返回）
            
        Returns:
            包含识别结果和状态的响应字典
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        if not self.config.is_configured():
            raise Exception("火山引擎配置未完成")
            
        # 构建查询参数
        query_data = {
            'appid': self.config.appid,
            'token': self.config.access_token,
            'id': task_id,
            'cluster': self.config.cluster
        }
        
        try:
            logger.debug(f"查询任务状态: {task_id}")
            
            # 发送POST请求到查询结果API
            response = self.session.post(
                f"{self.service_url}/query",
                data=json.dumps(query_data),
                headers=self.headers,
                timeout=30
            )
            
            # 解析响应
            response_data = response.json()
            
            if response.status_code != 200:
                logger.error(f"查询任务失败，HTTP状态码: {response.status_code}")
                raise Exception(f"API请求失败: {response.status_code}")
            
            # 提取响应信息
            resp_info = response_data.get('resp', {})
            code = resp_info.get('code')
            message = resp_info.get('message', '')
            
            # 根据状态码判断任务状态
            if code == 1000:
                # 识别成功
                logger.info(f"任务 {task_id} 识别完成")
                return {
                    'success': True,
                    'status': 'completed',
                    'code': code,
                    'message': message,
                    'text': resp_info.get('text', ''),
                    'utterances': resp_info.get('utterances', []),
                    'task_id': task_id
                }
            elif code == 2000:
                # 正在处理
                return {
                    'success': True,
                    'status': 'processing',
                    'code': code,
                    'message': message,
                    'task_id': task_id
                }
            elif code == 2001:
                # 排队中
                return {
                    'success': True,
                    'status': 'queued',
                    'code': code,
                    'message': message,
                    'task_id': task_id
                }
            else:
                # 处理失败
                logger.error(f"任务 {task_id} 处理失败，错误码: {code}, 错误信息: {message}")
                return {
                    'success': False,
                    'status': 'failed',
                    'code': code,
                    'message': message,
                    'task_id': task_id
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求异常: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"响应JSON解析失败: {str(e)}")
            raise Exception(f"响应格式错误: {str(e)}")
        except Exception as e:
            logger.error(f"查询任务时发生未知错误: {str(e)}")
            raise

    def file_recognize(self, audio_url: str, audio_format: str = "wav", 
                      with_speaker_info: bool = False, uid: Optional[str] = None,
                      max_wait_time: int = 300, check_interval: int = 2) -> Dict[str, Any]:
        """完整的音频文件识别流程
        
        提交任务并等待识别完成，自动轮询结果直到完成或超时
        
        Args:
            audio_url: 音频文件URL地址
            audio_format: 音频格式
            with_speaker_info: 是否需要说话人分离信息
            uid: 用户标识
            max_wait_time: 最大等待时间（秒），默认300秒
            check_interval: 查询间隔（秒），默认2秒
            
        Returns:
            包含完整识别结果的字典
            
        Raises:
            Exception: 当识别失败或超时时抛出异常
        """
        logger.info(f"开始音频文件识别流程: {audio_url}")
        
        # 1. 提交任务
        submit_result = self.submit_task(audio_url, audio_format, with_speaker_info, uid)
        task_id = submit_result['task_id']
        
        # 2. 轮询查询结果
        start_time = time.time()
        
        while True:
            # 检查是否超时
            elapsed_time = time.time() - start_time
            if elapsed_time > max_wait_time:
                logger.error(f"任务 {task_id} 等待超时（{max_wait_time}秒）")
                raise Exception(f"识别超时，等待时间超过 {max_wait_time} 秒")
            
            # 查询任务状态
            query_result = self.query_task(task_id)
            status = query_result.get('status')
            
            if status == 'completed':
                # 识别完成
                logger.info(f"音频识别完成，总耗时: {elapsed_time:.2f}秒")
                
                return {
                    'success': True,
                    'task_id': task_id,
                    'text': query_result.get('text', ''),
                    'utterances': query_result.get('utterances', []),
                    'elapsed_time': elapsed_time,
                    'audio_url': audio_url,
                    'uid': uid or submit_result.get('uid')
                }
                
            elif status == 'failed':
                # 识别失败
                logger.error(f"音频识别失败: {query_result.get('message')}")
                raise Exception(f"识别失败: {query_result.get('message')}")
                
            else:
                # 处理中或排队中，继续等待
                logger.debug(f"任务状态: {status}, 已等待: {elapsed_time:.2f}秒")
                time.sleep(check_interval)

    def get_supported_formats(self) -> list:
        """获取支持的音频格式列表
        
        Returns:
            支持的音频格式列表
        """
        return ['raw', 'wav', 'ogg', 'mp3', 'mp4']

    def get_error_message(self, error_code: int) -> str:
        """根据错误码获取错误信息
        
        Args:
            error_code: 火山引擎API返回的错误码
            
        Returns:
            对应的错误信息描述
        """
        error_messages = {
            1000: "识别成功",
            1001: "请求参数无效",
            1002: "无访问权限",
            1003: "访问超频",
            1004: "访问超额",
            1005: "服务器繁忙",
            1006: "请求中断",
            1010: "音频过长",
            1011: "音频过大",
            1012: "音频格式无效",
            1013: "音频静音",
            1014: "空音频",
            1015: "下载失败",
            1020: "识别等待超时",
            1021: "识别处理超时",
            1022: "识别错误",
            1099: "未知错误",
            2000: "正在处理",
            2001: "排队中"
        }
        
        return error_messages.get(error_code, f"未知错误码: {error_code}")

    def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        检查服务配置和连接状态
        
        Returns:
            健康检查结果
        """
        try:
            # 检查配置
            if not self.config.is_configured():
                return {
                    'healthy': False,
                    'message': '配置不完整',
                    'config_status': {
                        'appid': bool(self.config.appid),
                        'access_token': bool(self.config.access_token),
                        'cluster': bool(self.config.cluster)
                    }
                }
            
            # 测试网络连接（简单的HEAD请求）
            response = self.session.head(self.service_url, timeout=10)
            
            return {
                'healthy': True,
                'message': '服务正常',
                'config_status': {
                    'appid': True,
                    'access_token': True,
                    'cluster': True
                },
                'network_status': response.status_code < 500
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'healthy': False,
                'message': f'健康检查失败: {str(e)}',
                'config_status': {
                    'appid': bool(self.config.appid),
                    'access_token': bool(self.config.access_token),
                    'cluster': bool(self.config.cluster)
                }
            }
"""
创蓝云智闪验一键登录服务

提供移动端和Web端的一键登录功能：
- 置换手机号：将SDK返回的token置换为真实手机号
- 本机校验：验证当前流量卡的手机号与传入的手机号是否一致
- 手机号解密：支持AES和RSA解密算法
"""

import hashlib
import hmac
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import aiohttp
import base64

from config.settings import settings

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class FlashResponse:
    """闪验API响应结果"""
    success: bool
    code: str
    message: str
    data: Optional[Dict[str, Any]] = None
    charge_status: int = 0  # 0=不收费，1=收费
    trade_no: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "success": self.success,
            "code": self.code,
            "message": self.message,
            "charge_status": self.charge_status
        }
        if self.data:
            result["data"] = self.data
        if self.trade_no:
            result["trade_no"] = self.trade_no
        return result

class FlashService:
    """
    创蓝云智闪验服务
    
    支持移动端和Web端的一键登录功能
    """
    
    def __init__(self):
        """初始化闪验服务"""
        self.config = settings.flash
        self._session: Optional[aiohttp.ClientSession] = None
        
        if not self.config.is_configured():
            logger.warning("闪验服务未配置，请设置FLASH_APP_ID和FLASH_APP_KEY")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'FlashLoginService/1.0.0'
                }
            )
        return self._session
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
    
    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """
        生成HMAC-SHA256签名
        
        Args:
            params: 请求参数
            
        Returns:
            str: 签名字符串
        """
        # 按字段名正序排序并拼接
        sorted_params = sorted(params.items())
        param_string = ''.join([f"{k}{v}" for k, v in sorted_params])
        
        # 使用HMAC-SHA256加密
        signature = hmac.new(
            self.config.app_key.encode('utf-8'),
            param_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest().upper()
        
        logger.debug(f"签名参数: {param_string}")
        logger.debug(f"生成签名: {signature}")
        
        return signature
    
    def _decrypt_phone_aes(self, encrypted_phone: str) -> str:
        """
        使用AES算法解密手机号
        
        Args:
            encrypted_phone: 加密的手机号（16进制字符串）
            
        Returns:
            str: 解密后的手机号
        """
        try:
            # 使用md5(appKey)前16位作为密钥，后16位作为初始化向量
            app_key_md5 = hashlib.md5(self.config.app_key.encode('utf-8')).hexdigest()
            key = app_key_md5[:16].encode('utf-8')
            iv = app_key_md5[16:32].encode('utf-8')
            
            # 将16进制字符串转换为字节
            encrypted_bytes = bytes.fromhex(encrypted_phone)
            
            # AES-CBC解密
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted_bytes = cipher.decrypt(encrypted_bytes)
            
            # 去除PKCS7填充并解码
            padding_length = decrypted_bytes[-1]
            phone_number = decrypted_bytes[:-padding_length].decode('utf-8')
            
            logger.debug(f"AES解密成功: {encrypted_phone} -> {phone_number}")
            return phone_number
            
        except Exception as e:
            logger.error(f"AES解密失败: {str(e)}")
            raise ValueError(f"手机号解密失败: {str(e)}")
    
    def _decrypt_phone_rsa(self, encrypted_phone: str) -> str:
        """
        使用RSA算法解密手机号
        
        Args:
            encrypted_phone: 加密的手机号（Base64字符串）
            
        Returns:
            str: 解密后的手机号
        """
        try:
            if not self.config.rsa_private_key:
                raise ValueError("RSA私钥未配置")
            
            # 加载RSA私钥
            private_key = RSA.import_key(self.config.rsa_private_key)
            cipher = PKCS1_v1_5.new(private_key)
            
            # 解码Base64并解密
            encrypted_bytes = base64.b64decode(encrypted_phone)
            phone_bytes = cipher.decrypt(encrypted_bytes, None)
            
            if phone_bytes is None:
                raise ValueError("RSA解密失败")
            
            phone_number = phone_bytes.decode('utf-8')
            
            logger.debug(f"RSA解密成功: {encrypted_phone} -> {phone_number}")
            return phone_number
            
        except Exception as e:
            logger.error(f"RSA解密失败: {str(e)}")
            raise ValueError(f"手机号解密失败: {str(e)}")
    
    def _decrypt_phone(self, encrypted_phone: str, encrypt_type: int = None) -> str:
        """
        解密手机号
        
        Args:
            encrypted_phone: 加密的手机号
            encrypt_type: 加密类型（0=AES，1=RSA），如果为None则使用配置的默认值
            
        Returns:
            str: 解密后的手机号
        """
        if encrypt_type is None:
            encrypt_type = self.config.encrypt_type
        
        if encrypt_type == 0:
            return self._decrypt_phone_aes(encrypted_phone)
        elif encrypt_type == 1:
            return self._decrypt_phone_rsa(encrypted_phone)
        else:
            raise ValueError(f"不支持的加密类型: {encrypt_type}")
    
    async def exchange_mobile_token(
        self,
        token: str,
        client_ip: Optional[str] = None,
        out_id: Optional[str] = None,
        encrypt_type: Optional[int] = None
    ) -> FlashResponse:
        """
        置换手机号（移动端）
        
        将SDK返回的token置换为真实手机号
        
        Args:
            token: SDK返回的token
            client_ip: 客户端IP（可选）
            out_id: 客户方流水号（可选）
            encrypt_type: 加密类型（可选，0=AES，1=RSA）
            
        Returns:
            FlashResponse: 置换结果
        """
        try:
            if not self.config.is_configured():
                return FlashResponse(
                    success=False,
                    code="CONFIG_ERROR",
                    message="闪验服务未配置，请检查FLASH_APP_ID和FLASH_APP_KEY"
                )
            
            # 构建请求参数
            params = {
                "appId": self.config.app_id,
                "token": token
            }
            
            if client_ip:
                params["clientIp"] = client_ip
            
            if encrypt_type is not None:
                params["encryptType"] = str(encrypt_type)
            elif self.config.encrypt_type is not None:
                params["encryptType"] = str(self.config.encrypt_type)
            
            if out_id:
                params["outId"] = out_id
            
            # 生成签名
            params["sign"] = self._generate_sign(params)
            
            logger.info(f"请求置换手机号: token={token[:10]}...")
            
            # 发送HTTP请求
            session = await self._get_session()
            async with session.post(self.config.mobile_query_url, data=params) as response:
                response_text = await response.text()
                
                logger.info(f"闪验API响应: {response_text}")
                
                # 解析响应
                try:
                    response_data = json.loads(response_text)
                except json.JSONDecodeError:
                    return FlashResponse(
                        success=False,
                        code="RESPONSE_FORMAT_ERROR",
                        message="API响应格式错误",
                        data={"raw_response": response_text}
                    )
                
                # 检查响应状态
                code = response_data.get('code', '')
                if code == '200000':
                    # 成功获取手机号
                    data = response_data.get('data', {})
                    encrypted_phone = data.get('mobileName', '')
                    trade_no = data.get('tradeNo', '')
                    
                    if not encrypted_phone:
                        return FlashResponse(
                            success=False,
                            code="NO_PHONE_DATA",
                            message="未返回手机号数据"
                        )
                    
                    # 解密手机号
                    try:
                        phone_number = self._decrypt_phone(
                            encrypted_phone, 
                            int(params.get("encryptType", self.config.encrypt_type))
                        )
                        
                        return FlashResponse(
                            success=True,
                            code=code,
                            message="手机号置换成功",
                            data={
                                "phone_number": phone_number,
                                "encrypted_phone": encrypted_phone,
                                "trade_no": trade_no
                            },
                            charge_status=response_data.get('chargeStatus', 0),
                            trade_no=trade_no
                        )
                        
                    except ValueError as e:
                        return FlashResponse(
                            success=False,
                            code="DECRYPT_ERROR",
                            message=str(e),
                            data={"encrypted_phone": encrypted_phone}
                        )
                else:
                    # 错误响应
                    error_message = self._get_error_message(code)
                    return FlashResponse(
                        success=False,
                        code=code,
                        message=error_message,
                        charge_status=response_data.get('chargeStatus', 0)
                    )
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求失败: {str(e)}")
            return FlashResponse(
                success=False,
                code="NETWORK_ERROR",
                message=f"网络请求失败: {str(e)}"
            )
        except Exception as e:
            logger.error(f"置换手机号时发生未知错误: {str(e)}")
            return FlashResponse(
                success=False,
                code="UNKNOWN_ERROR",
                message=f"置换失败: {str(e)}"
            )
    
    async def validate_mobile_web(
        self,
        token: str,
        mobile: str,
        out_id: Optional[str] = None
    ) -> FlashResponse:
        """
        本机校验（Web端）
        
        校验当前流量卡的手机号与传入的手机号是否一致
        
        Args:
            token: SDK返回的token
            mobile: 待校验的手机号
            out_id: 客户方流水号（可选）
            
        Returns:
            FlashResponse: 校验结果
        """
        try:
            if not self.config.is_configured():
                return FlashResponse(
                    success=False,
                    code="CONFIG_ERROR",
                    message="闪验服务未配置，请检查FLASH_APP_ID和FLASH_APP_KEY"
                )
            
            # 构建请求参数
            params = {
                "appId": self.config.app_id,
                "token": token,
                "mobile": mobile
            }
            
            if out_id:
                params["outId"] = out_id
            
            # 生成签名
            params["sign"] = self._generate_sign(params)
            
            logger.info(f"请求本机校验: mobile={mobile}, token={token[:10]}...")
            
            # 发送HTTP请求
            session = await self._get_session()
            async with session.post(self.config.web_validate_url, data=params) as response:
                response_text = await response.text()
                
                logger.info(f"闪验API响应: {response_text}")
                
                # 解析响应
                try:
                    response_data = json.loads(response_text)
                except json.JSONDecodeError:
                    return FlashResponse(
                        success=False,
                        code="RESPONSE_FORMAT_ERROR",
                        message="API响应格式错误",
                        data={"raw_response": response_text}
                    )
                
                # 检查响应状态
                code = response_data.get('code', '')
                if code == '200000':
                    # 成功校验
                    data = response_data.get('data', {})
                    is_verify = data.get('isVerify', '0')
                    trade_no = data.get('tradeNo', '')
                    
                    return FlashResponse(
                        success=True,
                        code=code,
                        message="本机校验成功",
                        data={
                            "is_verify": is_verify == '1',
                            "mobile": mobile,
                            "trade_no": trade_no
                        },
                        charge_status=response_data.get('chargeStatus', 0),
                        trade_no=trade_no
                    )
                else:
                    # 错误响应
                    error_message = self._get_error_message(code)
                    return FlashResponse(
                        success=False,
                        code=code,
                        message=error_message,
                        charge_status=response_data.get('chargeStatus', 0)
                    )
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求失败: {str(e)}")
            return FlashResponse(
                success=False,
                code="NETWORK_ERROR",
                message=f"网络请求失败: {str(e)}"
            )
        except Exception as e:
            logger.error(f"本机校验时发生未知错误: {str(e)}")
            return FlashResponse(
                success=False,
                code="UNKNOWN_ERROR",
                message=f"校验失败: {str(e)}"
            )
    
    def _get_error_message(self, error_code: str) -> str:
        """
        根据错误码获取错误信息
        
        Args:
            error_code: 错误码
            
        Returns:
            str: 错误信息
        """
        error_messages = {
            '200400': '取号失败，号码补填不正确',
            '400001': '参数校验异常',
            '403000': '用户校验失败（一般为签名问题）',
            '415000': '请求数据转换异常',
            '500000': '系统异常',
            '500002': '数据处理异常',
            '500003': '业务操作失败',
            '500004': '远程调用失败',
            '500005': '账户余额异常',
            '500006': '请求外部系统失败',
            '504000': '系统超时',
            '400101': '在下游系统中的商户信息不存在',
            '403101': '账户被下游系统禁用',
            '403102': '账户在下游系统中没有被激活',
            '510101': '在下游系统中的用户产品可用数量不足',
            '400102': '商户IP地址在下游系统中不合法',
            '400200': '黑名单列表',
            '400201': '手机号码不能为空',
            '400901': '账户信息不存在',
            '400902': '应用类型信息不存在',
            '500901': '邮箱未设置',
            '500902': '账户信息已存在',
            '500903': '账户相关能力已激活'
        }
        
        return error_messages.get(error_code, f"未知错误（错误码：{error_code}）")

# 全局闪验服务实例
_flash_service_instance: Optional[FlashService] = None

def get_flash_service() -> FlashService:
    """
    获取闪验服务实例（单例模式）
    
    Returns:
        FlashService: 闪验服务实例
    """
    global _flash_service_instance
    
    if _flash_service_instance is None:
        _flash_service_instance = FlashService()
    
    return _flash_service_instance

async def cleanup_flash_service():
    """清理闪验服务资源"""
    global _flash_service_instance
    
    if _flash_service_instance:
        await _flash_service_instance.close()
        _flash_service_instance = None 
# 火山引擎语音识别服务使用指南

## 概述

`VolcengineService` 是基于火山引擎录音文件识别极速版API实现的语音识别服务，提供音频文件转文字的完整功能。

## 功能特性

- ✅ **音频文件识别**: 支持多种格式的音频文件转文字
- ✅ **说话人分离**: 可选择是否启用说话人分离功能
- ✅ **异步处理**: 提交任务后自动轮询获取结果
- ✅ **完整错误处理**: 详细的错误码和异常处理
- ✅ **健康检查**: 服务状态监控功能
- ✅ **配置管理**: 统一的配置管理和验证

## 支持的音频格式

- `raw` - PCM原始音频
- `wav` - WAV格式
- `ogg` - OGG格式  
- `mp3` - MP3格式
- `mp4` - MP4格式

## 配置说明

### 环境变量配置

在项目根目录的 `.env` 文件中添加以下配置：

```bash
# 火山引擎语音识别配置
VOLCENGINE_APPID=你的应用ID
VOLCENGINE_ACCESS_TOKEN=你的访问令牌
VOLCENGINE_CLUSTER=你的集群ID
VOLCENGINE_SERVICE_URL=https://openspeech.bytedance.com/api/v1/auc
```

### 获取配置信息

1. 登录 [火山引擎控制台](https://console.volcengine.com/speech/app)
2. 创建或选择应用
3. 开通录音文件识别极速版服务
4. 获取应用ID、访问令牌和集群ID

## 使用示例

### 基础使用

```python
from controllers.common.services.volcengine_service import VolcengineService

# 初始化服务
service = VolcengineService()

# 完整的音频识别流程
try:
    result = service.file_recognize(
        audio_url="https://example.com/audio.wav",
        audio_format="wav",
        with_speaker_info=True
    )
    
    print(f"识别结果: {result['text']}")
    print(f"耗时: {result['elapsed_time']:.2f}秒")
    
    # 获取详细的说话人信息
    for utterance in result['utterances']:
        speaker = utterance.get('additions', {}).get('speaker', '未知')
        text = utterance['text']
        start_time = utterance['start_time'] / 1000  # 转换为秒
        end_time = utterance['end_time'] / 1000
        
        print(f"说话人{speaker}: {text} ({start_time:.1f}s - {end_time:.1f}s)")
        
except Exception as e:
    print(f"识别失败: {e}")
```

### 分步操作

```python
# 1. 提交任务
submit_result = service.submit_task(
    audio_url="https://example.com/audio.wav",
    audio_format="wav"
)

task_id = submit_result['task_id']
print(f"任务ID: {task_id}")

# 2. 查询结果
import time
while True:
    query_result = service.query_task(task_id)
    
    if query_result['status'] == 'completed':
        print(f"识别完成: {query_result['text']}")
        break
    elif query_result['status'] == 'failed':
        print(f"识别失败: {query_result['message']}")
        break
    else:
        print(f"状态: {query_result['status']}")
        time.sleep(2)
```

### 健康检查

```python
health = service.health_check()

if health['healthy']:
    print("服务正常")
else:
    print(f"服务异常: {health['message']}")
    print(f"配置状态: {health['config_status']}")
```

## API 方法说明

### `submit_task(audio_url, audio_format="wav", with_speaker_info=False, uid=None)`

提交音频识别任务。

**参数:**
- `audio_url` (str): 音频文件URL地址（需要可下载的公开链接）
- `audio_format` (str): 音频格式，默认 "wav"
- `with_speaker_info` (bool): 是否需要说话人分离信息，默认 False
- `uid` (str, optional): 用户标识，未提供时自动生成

**返回:** 包含任务ID和状态的字典

### `query_task(task_id)`

查询任务识别结果。

**参数:**
- `task_id` (str): 任务ID

**返回:** 包含识别结果和状态的字典

### `file_recognize(audio_url, audio_format="wav", with_speaker_info=False, uid=None, max_wait_time=300, check_interval=2)`

完整的音频文件识别流程，自动轮询直到完成。

**参数:**
- `audio_url` (str): 音频文件URL地址
- `audio_format` (str): 音频格式，默认 "wav"
- `with_speaker_info` (bool): 是否需要说话人分离信息，默认 False
- `uid` (str, optional): 用户标识
- `max_wait_time` (int): 最大等待时间（秒），默认 300秒
- `check_interval` (int): 查询间隔（秒），默认 2秒

**返回:** 包含完整识别结果的字典

### 辅助方法

- `get_supported_formats()`: 获取支持的音频格式列表
- `get_error_message(error_code)`: 根据错误码获取错误信息
- `health_check()`: 健康检查

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1000 | 识别成功 |
| 1001 | 请求参数无效 |
| 1002 | 无访问权限 |
| 1003 | 访问超频 |
| 1004 | 访问超额 |
| 1005 | 服务器繁忙 |
| 1010 | 音频过长 |
| 1011 | 音频过大 |
| 1012 | 音频格式无效 |
| 1013 | 音频静音 |
| 1014 | 空音频 |
| 1015 | 下载失败 |
| 1020 | 识别等待超时 |
| 1021 | 识别处理超时 |
| 1022 | 识别错误 |
| 2000 | 正在处理 |
| 2001 | 排队中 |

## 注意事项

1. **音频要求**:
   - 文件大小小于 512MB
   - 时长小于 5 小时
   - 半小时内总时长不超过 500 小时

2. **URL要求**:
   - 必须是可下载的公开链接
   - 支持HTTP/HTTPS协议

3. **结果保存**:
   - 转写结果在服务端保存 24 小时
   - 超时后查询会失败

4. **配置安全**:
   - 不要在代码中硬编码敏感信息
   - 使用环境变量管理配置

## 集成到其他服务

### 在API中使用

```python
from controllers.common.services.volcengine_service import VolcengineService

def audio_recognition_api(audio_url: str):
    """音频识别API接口"""
    service = VolcengineService()
    
    try:
        result = service.file_recognize(audio_url)
        return {
            "success": True,
            "text": result["text"],
            "task_id": result["task_id"],
            "elapsed_time": result["elapsed_time"]
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
```

### 批量处理

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def batch_recognize(audio_urls: list):
    """批量音频识别"""
    service = VolcengineService()
    
    def recognize_single(url):
        return service.file_recognize(url)
    
    with ThreadPoolExecutor(max_workers=5) as executor:
        loop = asyncio.get_event_loop()
        tasks = [
            loop.run_in_executor(executor, recognize_single, url) 
            for url in audio_urls
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results
```

## 参考链接

- [火山引擎语音技术文档](https://www.volcengine.com/docs/6561/192519)
- [火山引擎控制台](https://console.volcengine.com/speech/app)
- [录音文件识别极速版API文档](https://www.volcengine.com/docs/6561/192519#_6-%E6%8E%A5%E5%85%A5-demo) 
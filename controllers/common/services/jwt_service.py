"""
JWT认证服务

用于生成和验证JWT token，支持用户登录状态管理
"""

import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging
import zoneinfo

from config.settings import settings

logger = logging.getLogger(__name__)

class TokenType:
    """Token类型常量"""
    ACCESS = "access"
    REFRESH = "refresh"

class JwtService:
    """JWT认证服务类"""
    
    def __init__(self):
        """初始化JWT认证服务，使用全局配置"""
        self.config = settings.jwt
    
    def generate_access_token(
        self, 
        user_id: str, 
        user_type: str = "phone",
        phone_number: str = None,
        nickname: str = None,
        extra_data: Dict[str, Any] = None
    ) -> str:
        """
        生成访问令牌
        
        Args:
            user_id: 用户ID
            user_type: 用户类型
            phone_number: 手机号
            nickname: 昵称
            extra_data: 额外数据
            
        Returns:
            str: JWT访问令牌
        """
        return self._generate_token(
            user_id=user_id,
            user_type=user_type,
            phone_number=phone_number,
            nickname=nickname,
            token_type=TokenType.ACCESS,
            expire_minutes=self.config.access_token_expire_minutes,
            extra_data=extra_data
        )
    
    def generate_refresh_token(
        self, 
        user_id: str, 
        user_type: str = "phone",
        extra_data: Dict[str, Any] = None
    ) -> str:
        """
        生成刷新令牌
        
        Args:
            user_id: 用户ID
            user_type: 用户类型
            extra_data: 额外数据
            
        Returns:
            str: JWT刷新令牌
        """
        return self._generate_token(
            user_id=user_id,
            user_type=user_type,
            token_type=TokenType.REFRESH,
            expire_minutes=self.config.refresh_token_expire_minutes,
            extra_data=extra_data
        )
    
    def _generate_token(
        self,
        user_id: str,
        user_type: str,
        token_type: str,
        expire_minutes: int,
        phone_number: str = None,
        nickname: str = None,
        extra_data: Dict[str, Any] = None
    ) -> str:
        """
        生成JWT令牌
        
        Args:
            user_id: 用户ID
            user_type: 用户类型
            token_type: 令牌类型
            expire_minutes: 过期时间（分钟）
            phone_number: 手机号
            nickname: 昵称
            extra_data: 额外数据
            
        Returns:
            str: JWT令牌
        """
        # 使用上海时区
        shanghai_tz = zoneinfo.ZoneInfo("Asia/Shanghai")
        now = datetime.now(shanghai_tz)
        
        # 构建payload
        payload = {
            "user_id": user_id,
            "user_type": user_type,
            "token_type": token_type,
            "iat": now,  # 签发时间
            "exp": now + timedelta(minutes=expire_minutes),  # 过期时间
            "jti": f"{user_id}_{token_type}_{int(now.timestamp())}"  # JWT ID
        }
        
        # 添加可选字段
        if phone_number:
            payload["phone_number"] = phone_number
        if nickname:
            payload["nickname"] = nickname
        if extra_data:
            payload.update(extra_data)
        
        # 生成token
        try:
            token = jwt.encode(
                payload, 
                self.config.secret_key, 
                algorithm=self.config.algorithm
            )
            return token
        except Exception as e:
            logger.error(f"生成JWT令牌失败: {str(e)}")
            raise ValueError(f"生成令牌失败: {str(e)}")
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            Optional[Dict[str, Any]]: 解码后的payload，验证失败返回None
        """
        try:
            payload = jwt.decode(
                token,
                self.config.secret_key,
                algorithms=[self.config.algorithm]
            )
            
            # 检查过期时间
            exp_timestamp = payload.get("exp")
            if exp_timestamp:
                # 使用上海时区
                shanghai_tz = zoneinfo.ZoneInfo("Asia/Shanghai")
                current_time = datetime.now(shanghai_tz)
                exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=shanghai_tz)
                
                if exp_datetime < current_time:
                    logger.warning("JWT令牌已过期")
                    return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT令牌已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"JWT令牌无效: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"验证JWT令牌时发生错误: {str(e)}")
            return None
    
    def verify_access_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证访问令牌
        
        Args:
            token: 访问令牌
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息，验证失败返回None
        """
        payload = self.verify_token(token)
        if payload and payload.get("token_type") == TokenType.ACCESS:
            return payload
        return None
    
    def verify_refresh_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证刷新令牌
        
        Args:
            token: 刷新令牌
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息，验证失败返回None
        """
        payload = self.verify_token(token)
        if payload and payload.get("token_type") == TokenType.REFRESH:
            return payload
        return None
    
    def get_user_from_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        从令牌中获取用户信息
        
        Args:
            token: JWT令牌
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        payload = self.verify_access_token(token)
        if payload:
            return {
                "user_id": payload.get("user_id"),
                "user_type": payload.get("user_type"),
                "phone_number": payload.get("phone_number"),
                "nickname": payload.get("nickname")
            }
        return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """
        使用刷新令牌生成新的访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            Optional[str]: 新的访问令牌，刷新失败返回None
        """
        payload = self.verify_refresh_token(refresh_token)
        if payload:
            return self.generate_access_token(
                user_id=payload.get("user_id"),
                user_type=payload.get("user_type"),
                phone_number=payload.get("phone_number"),
                nickname=payload.get("nickname")
            )
        return None

# 全局JWT认证服务实例
_jwt_service_instance: Optional[JwtService] = None

def get_jwt_service() -> JwtService:
    """
    获取JWT认证服务实例（单例模式）
    
    Returns:
        JwtService: JWT认证服务实例
    """
    global _jwt_service_instance
    
    if _jwt_service_instance is None:
        _jwt_service_instance = JwtService()
    
    return _jwt_service_instance

def extract_token_from_header(authorization_header: str) -> Optional[str]:
    """
    从Authorization头部提取token
    
    Args:
        authorization_header: Authorization头部值
        
    Returns:
        Optional[str]: 提取的token，提取失败返回None
    """
    if not authorization_header:
        return None
    
    # 支持 "Bearer <token>" 格式
    parts = authorization_header.split()
    if len(parts) == 2 and parts[0].lower() == "bearer":
        return parts[1]
    
    # 支持直接传token
    return authorization_header 
"""
频率限制器

用于限制API调用频率，防止恶意请求
"""

from datetime import datetime
from typing import Dict
import logging

from database.utils import get_current_timestamp

logger = logging.getLogger(__name__)

class RateLimiter:
    """频率限制器类"""
    
    def __init__(self, min_interval: int = 60):
        """
        初始化频率限制器
        
        Args:
            min_interval: 最小间隔时间（秒）
        """
        self._requests: Dict[str, float] = {}  # key -> last_request_time
        self._min_interval = min_interval
        
    def check_rate_limit(self, key: str) -> bool:
        """
        检查是否超过频率限制
        
        Args:
            key: 限制键（如手机号、IP地址等）
            
        Returns:
            bool: True表示允许请求，False表示超过限制
        """
        now = get_current_timestamp().timestamp()
        last_request = self._requests.get(key, 0)
        
        if now - last_request < self._min_interval:
            logger.warning(f"频率限制触发: {key}, 间隔: {now - last_request:.2f}秒")
            return False
        
        self._requests[key] = now
        return True
    
    def reset_limit(self, key: str):
        """
        重置指定键的限制
        
        Args:
            key: 要重置的键
        """
        if key in self._requests:
            del self._requests[key]
            logger.info(f"重置频率限制: {key}")
    
    def get_remaining_time(self, key: str) -> float:
        """
        获取剩余等待时间
        
        Args:
            key: 要查询的键
            
        Returns:
            float: 剩余等待时间（秒），0表示可以立即请求
        """
        now = get_current_timestamp().timestamp()
        last_request = self._requests.get(key, 0)
        remaining = self._min_interval - (now - last_request)
        return max(0, remaining)
    
    def cleanup_old_records(self, max_age: int = 3600):
        """
        清理过期记录
        
        Args:
            max_age: 记录最大保存时间（秒）
        """
        now = get_current_timestamp().timestamp()
        expired_keys = [
            key for key, timestamp in self._requests.items()
            if now - timestamp > max_age
        ]
        
        for key in expired_keys:
            del self._requests[key]
        
        if expired_keys:
            logger.info(f"清理过期频率限制记录: {len(expired_keys)}条")

# 全局频率限制器实例
_global_rate_limiter: RateLimiter = None

def get_global_rate_limiter(min_interval: int = 60) -> RateLimiter:
    """
    获取全局频率限制器实例
    
    Args:
        min_interval: 最小间隔时间（秒）
        
    Returns:
        RateLimiter: 频率限制器实例
    """
    global _global_rate_limiter
    
    if _global_rate_limiter is None:
        _global_rate_limiter = RateLimiter(min_interval)
    
    return _global_rate_limiter 
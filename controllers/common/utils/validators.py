"""
验证器工具类

提供各种数据验证功能
"""

import re
from typing import Optional

class PhoneValidator:
    """手机号验证器"""
    
    # 中国大陆手机号正则表达式
    CHINA_MOBILE_PATTERN = r'^1[3-9]\d{9}$'
    
    @classmethod
    def validate(cls, phone_number: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone_number: 手机号字符串
            
        Returns:
            bool: 格式是否正确
        """
        if not phone_number:
            return False
        
        return bool(re.match(cls.CHINA_MOBILE_PATTERN, phone_number))
    
    @classmethod
    def normalize(cls, phone_number: str) -> Optional[str]:
        """
        标准化手机号（去除空格、短横线等）
        
        Args:
            phone_number: 原始手机号
            
        Returns:
            Optional[str]: 标准化后的手机号，无效返回None
        """
        if not phone_number:
            return None
        
        # 去除所有非数字字符
        normalized = re.sub(r'\D', '', phone_number)
        
        # 如果是+86开头，去掉+86
        if normalized.startswith('86') and len(normalized) == 13:
            normalized = normalized[2:]
        
        # 验证标准化后的手机号
        if cls.validate(normalized):
            return normalized
        
        return None
    
    @classmethod
    def mask(cls, phone_number: str) -> str:
        """
        脱敏显示手机号
        
        Args:
            phone_number: 手机号
            
        Returns:
            str: 脱敏后的手机号（如：138****1234）
        """
        if not cls.validate(phone_number):
            return "***"
        
        return f"{phone_number[:3]}****{phone_number[-4:]}"

class VerificationCodeValidator:
    """验证码验证器"""
    
    @classmethod
    def validate(cls, code: str, length: int = 6) -> bool:
        """
        验证验证码格式
        
        Args:
            code: 验证码
            length: 期望长度
            
        Returns:
            bool: 格式是否正确
        """
        if not code:
            return False
        
        return len(code) == length and code.isdigit()

class EmailValidator:
    """邮箱验证器"""
    
    EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    @classmethod
    def validate(cls, email: str) -> bool:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 格式是否正确
        """
        if not email:
            return False
        
        return bool(re.match(cls.EMAIL_PATTERN, email))

class NicknameValidator:
    """昵称验证器"""
    
    @classmethod
    def validate(cls, nickname: str, min_length: int = 1, max_length: int = 20) -> bool:
        """
        验证昵称格式
        
        Args:
            nickname: 昵称
            min_length: 最小长度
            max_length: 最大长度
            
        Returns:
            bool: 格式是否正确
        """
        if not nickname:
            return False
        
        # 去除首尾空格
        nickname = nickname.strip()
        
        # 检查长度
        if len(nickname) < min_length or len(nickname) > max_length:
            return False
        
        # 检查是否包含非法字符（可根据需要调整）
        illegal_chars = ['<', '>', '"', "'", '&', '\n', '\r', '\t']
        for char in illegal_chars:
            if char in nickname:
                return False
        
        return True 
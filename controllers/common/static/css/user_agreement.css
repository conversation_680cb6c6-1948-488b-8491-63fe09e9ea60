/**
 * 菲玲用户协议页面样式
 * 移动端优化，响应式设计
 */

/* 重置样式和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* 页面头部样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 0;
    box-sizing: border-box;
}

.header-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    height: 56px;
    box-sizing: border-box;
}

.back-button {
    background: none;
    border: none;
    padding: 8px;
    margin-right: 12px;
    color: #666;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    box-sizing: border-box;
}

.back-button:hover {
    background-color: #f5f5f5;
}

.back-button:active {
    background-color: #e0e0e0;
}

.page-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex: 1;
}

/* 主要内容区域 */
.main-content {
    padding-top: 56px; /* 为固定头部留空间 */
    min-height: 100vh;
    box-sizing: border-box;
}

.content-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px 16px 40px;
    background: #fff;
    box-sizing: border-box;
}

/* 章节样式 */
.section {
    margin-bottom: 32px;
    box-sizing: border-box;
}

.section:last-child {
    margin-bottom: 0;
}

/* 标题样式 */
h1 {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 20px;
    line-height: 1.4;
}

h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e8f4fd;
    line-height: 1.4;
}

h3 {
    font-size: 16px;
    font-weight: 600;
    color: #34495e;
    margin: 20px 0 12px 0;
    line-height: 1.4;
}

h4 {
    font-size: 15px;
    font-weight: 600;
    color: #5a6c7d;
    margin: 16px 0 10px 0;
    line-height: 1.4;
}

/* 段落样式 */
p {
    font-size: 14px;
    line-height: 1.8;
    color: #333;
    margin-bottom: 12px;
    text-align: justify;
    word-break: break-word;
}

/* 强调文本样式 */
strong {
    font-weight: 600;
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    padding: 1px 4px;
    border-radius: 3px;
    box-sizing: border-box;
}

/* 列表样式 */
ul, ol {
    margin: 12px 0;
    padding-left: 20px;
    box-sizing: border-box;
}

li {
    font-size: 14px;
    line-height: 1.8;
    color: #333;
    margin-bottom: 8px;
    text-align: justify;
    word-break: break-word;
}

/* 子列表缩进 */
ul ul, ol ol {
    margin: 8px 0;
    padding-left: 20px;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
    transition: all 0.3s ease;
    z-index: 999;
    box-sizing: border-box;
}

.back-to-top:hover {
    background: #0056cc;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.back-to-top:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

/* 底部留白 */
.bottom-spacer {
    height: 60px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    html {
        font-size: 14px;
    }
    
    .header-content {
        padding: 10px 12px;
        height: 52px;
    }
    
    .page-title {
        font-size: 16px;
    }
    
    .main-content {
        padding-top: 52px;
    }
    
    .content-container {
        padding: 16px 12px 32px;
    }
    
    h1 {
        font-size: 22px;
    }
    
    h2 {
        font-size: 18px;
    }
    
    h3 {
        font-size: 15px;
    }
    
    h4 {
        font-size: 14px;
    }
    
    p, li {
        font-size: 13px;
        line-height: 1.7;
    }
    
    .back-to-top {
        bottom: 16px;
        right: 16px;
        width: 44px;
        height: 44px;
    }
}

@media (max-width: 360px) {
    .content-container {
        padding: 12px 8px 24px;
    }
    
    h2 {
        font-size: 17px;
    }
    
    .section {
        margin-bottom: 24px;
    }
}

/* 高分辨率屏幕适配 */
@media (min-width: 768px) {
    .content-container {
        max-width: 680px;
        padding: 24px 32px 48px;
        margin: 0 auto;
        border-radius: 8px;
        margin-top: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .main-content {
        background: #f8f9fa;
        padding-top: 76px;
    }
    
    p, li {
        font-size: 15px;
    }
    
    h2 {
        font-size: 22px;
    }
    
    h3 {
        font-size: 18px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .header {
        background: #2a2a2a;
        border-bottom-color: #404040;
    }
    
    .content-container {
        background: #2a2a2a;
    }
    
    .page-title {
        color: #e0e0e0;
    }
    
    h2 {
        color: #4a90e2;
        border-bottom-color: #404040;
    }
    
    h3 {
        color: #c0c0c0;
    }
    
    h4 {
        color: #a0a0a0;
    }
    
    p, li {
        color: #e0e0e0;
    }
    
    strong {
        color: #ff6b6b;
        background: rgba(255, 107, 107, 0.15);
    }
    
    .back-button {
        color: #c0c0c0;
    }
    
    .back-button:hover {
        background-color: #404040;
    }
    
    .back-button:active {
        background-color: #4a4a4a;
    }
}

/* 触摸友好的交互 */
@media (hover: none) and (pointer: coarse) {
    .back-button {
        padding: 12px;
        min-height: 44px;
        min-width: 44px;
    }
    
    .back-to-top {
        width: 52px;
        height: 52px;
    }
}

/* 打印样式 */
@media print {
    .header,
    .back-to-top {
        display: none;
    }
    
    .main-content {
        padding-top: 0;
    }
    
    .content-container {
        max-width: none;
        padding: 0;
        box-shadow: none;
        background: #fff;
    }
    
    body {
        background: #fff;
        color: #000;
    }
    
    strong {
        background: none;
        color: #000;
        font-weight: bold;
    }
} 
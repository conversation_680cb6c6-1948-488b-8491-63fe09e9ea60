"""
短信验证码认证API

提供发送验证码、验证登录、闪验一键登录等功能的HTTP API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, validator, <PERSON>
from typing import Optional, Dict, Any
import logging
from datetime import datetime

# 修复数据库导入问题
from database import DatabaseManager
from database.connection.session_manager import DatabaseSession
from database.models.user import User
from database.models.sms_verification import SmsVerification
from database.utils.helpers import get_current_timestamp
from database.utils import get_current_time_iso
from ..services import get_sms_service, get_jwt_service, get_flash_service
from ..utils import get_global_rate_limiter, PhoneValidator, VerificationCodeValidator

# 配置日志
logger = logging.getLogger(__name__)

# 创建全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None

async def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
    return _db_manager

async def get_db_session():
    """获取数据库会话依赖"""
    db_manager = await get_database_manager()
    async with db_manager.get_session() as session:
        yield session

# 创建路由器
router = APIRouter(prefix="/api/auth", tags=["认证"])

# JWT token验证函数（需要在路由定义之前定义）
async def get_current_user_id(
    request: Request,
    jwt_service = Depends(get_jwt_service)
) -> str:
    """
    从JWT token中获取当前用户ID（只验证token，不查询数据库）
    
    参考cos.py的实现，避免在用户已删除后仍然查询数据库导致的错误
    
    Args:
        request: FastAPI请求对象
        jwt_service: JWT服务实例
        
    Returns:
        str: 用户ID
        
    Raises:
        HTTPException: 当未认证或认证失败时抛出401错误
    """
    try:
        # 从请求头获取Authorization token
        authorization = request.headers.get("Authorization")
        if not authorization:
            logger.warning("请求缺少Authorization头部")
            raise HTTPException(
                status_code=401,
                detail="未提供认证令牌"
            )
        
        # 提取token
        from ..services.jwt_service import extract_token_from_header
        token = extract_token_from_header(authorization)
        
        if not token:
            logger.warning("Token格式错误")
            raise HTTPException(
                status_code=401,
                detail="认证令牌格式错误"
            )
        
        # 验证token并获取用户信息
        user_info = jwt_service.get_user_from_token(token)
        
        if not user_info:
            logger.warning("Token验证失败")
            raise HTTPException(
                status_code=401,
                detail="认证令牌无效"
            )
        
        user_id = user_info.get("user_id")
        if not user_id:
            logger.warning("Token中缺少用户ID信息")
            raise HTTPException(
                status_code=401,
                detail="认证令牌中缺少用户信息"
            )
        
        logger.debug(f"用户认证成功: {user_id}")
        return user_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户ID失败: {e}")
        raise HTTPException(
            status_code=401,
            detail="认证验证失败"
        )

# Pydantic模型定义
class SendCodeRequest(BaseModel):
    """发送验证码请求模型"""
    phone_number: str
    purpose: str = "login"  # login, register等
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        """验证手机号格式"""
        if not PhoneValidator.validate(v):
            raise ValueError('手机号格式不正确')
        return v
    
    @validator('purpose')
    def validate_purpose(cls, v):
        """验证用途"""
        if v not in ['login', 'register']:
            raise ValueError('用途只能是login或register')
        return v

class VerifyCodeRequest(BaseModel):
    """验证码登录请求模型"""
    phone_number: str
    verification_code: str
    nickname: Optional[str] = None
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        """验证手机号格式"""
        if not PhoneValidator.validate(v):
            raise ValueError('手机号格式不正确')
        return v
    
    @validator('verification_code')
    def validate_verification_code(cls, v):
        """验证验证码格式"""
        if not VerificationCodeValidator.validate(v, 6):
            raise ValueError('验证码必须是6位数字')
        return v

class FlashLoginRequest(BaseModel):
    """闪验一键登录请求模型"""
    token: str
    client_ip: Optional[str] = None
    nickname: Optional[str] = None
    encrypt_type: Optional[int] = None  # 0=AES, 1=RSA
    out_id: Optional[str] = None
    
    @validator('token')
    def validate_token(cls, v):
        """验证token格式"""
        if not v or len(v.strip()) == 0:
            raise ValueError('token不能为空')
        return v.strip()

class FlashValidateRequest(BaseModel):
    """闪验本机校验请求模型"""
    token: str
    mobile: str
    out_id: Optional[str] = None
    
    @validator('token')
    def validate_token(cls, v):
        """验证token格式"""
        if not v or len(v.strip()) == 0:
            raise ValueError('token不能为空')
        return v.strip()
    
    @validator('mobile')
    def validate_mobile(cls, v):
        """验证手机号格式"""
        if not PhoneValidator.validate(v):
            raise ValueError('手机号格式不正确')
        return v

class RefreshTokenRequest(BaseModel):
    """刷新token请求模型"""
    refresh_token: str

# API响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str

def create_response(success: bool, message: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """创建标准响应"""
    return {
        "success": success,
        "message": message,
        "data": data or {},
        "timestamp": get_current_time_iso()
    }

@router.post("/send-code", response_model=ApiResponse)
async def send_verification_code(
    request: SendCodeRequest,
    session: DatabaseSession = Depends(get_db_session)
):
    """
    发送短信验证码
    
    Args:
        request: 发送验证码请求
        session: 数据库会话
        
    Returns:
        ApiResponse: 发送结果
    """
    try:
        # 检查频率限制
        rate_limiter = get_global_rate_limiter(60)  # 60秒间隔
        if not rate_limiter.check_rate_limit(request.phone_number):
            remaining_time = rate_limiter.get_remaining_time(request.phone_number)
            raise HTTPException(
                status_code=429,
                detail=f"发送太频繁，请等待{int(remaining_time)}秒后再试"
            )
        
        # 创建验证码记录
        verification = await SmsVerification.create_verification(
            session=session,
            phone_number=request.phone_number,
            purpose=request.purpose,
            expires_minutes=5
        )
        
        # 发送短信
        sms_service = get_sms_service()
        sms_result = await sms_service.send_verification_code(
            phone_number=request.phone_number,
            verification_code=verification.verification_code
        )
        
        if sms_result.success:
            logger.info(f"验证码发送成功: {request.phone_number}")
            response_data = {
                "phone_number": request.phone_number,
                "expires_in": 300,  # 5分钟
                "verification_id": verification.id
            }
            
            # 如果是开发模式，添加开发信息
            if sms_result.data and sms_result.data.get("dev_mode"):
                response_data.update({
                    "dev_mode": True,
                    "dev_code": sms_result.data.get("dev_code"),
                    "dev_message": sms_result.data.get("message")
                })
            
            return create_response(
                success=True,
                message="验证码发送成功",
                data=response_data
            )
        else:
            logger.error(f"验证码发送失败: {sms_result.message}")
            # 发送失败时重置频率限制
            rate_limiter.reset_limit(request.phone_number)
            raise HTTPException(
                status_code=500,
                detail=f"短信发送失败: {sms_result.message}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送验证码时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.post("/verify-login", response_model=ApiResponse)
async def verify_code_login(
    request: VerifyCodeRequest,
    session: DatabaseSession = Depends(get_db_session)
):
    """
    验证码登录
    
    Args:
        request: 验证码登录请求
        session: 数据库会话
        
    Returns:
        ApiResponse: 登录结果，包含JWT token
    """
    try:
        logger.info(f"开始验证码登录流程: {request.phone_number}")

        # 如果是手机号15099999999 直接登录进入

        if request.phone_number != '15099999999':
            # 验证验证码
            verification = await SmsVerification.find_valid_code(
                session=session,
                phone_number=request.phone_number,
                verification_code=request.verification_code,
                purpose="login"
            )
            
            if not verification:
                logger.warning(f"验证码无效: {request.phone_number}")
                raise HTTPException(
                    status_code=400,
                    detail="验证码无效或已过期"
                )
            
            logger.info(f"验证码有效，开始验证: {verification.id}")
            
            # 验证验证码
            is_valid = await verification.verify(session, request.verification_code)
            if not is_valid:
                logger.warning(f"验证码验证失败: {request.phone_number}")
                raise HTTPException(
                    status_code=400,
                    detail="验证码错误"
                )
        
        logger.info(f"验证码验证成功，查找或创建用户: {request.phone_number}")
        
        # 查找或创建用户
        user = await User.find_by_phone(session, request.phone_number)
        if not user:
            logger.info(f"用户不存在，创建新用户: {request.phone_number}")
            # 创建新用户
            nickname = request.nickname or PhoneValidator.mask(request.phone_number)
            try:
                user = await User.create_phone_user(
                    session=session,
                    phone_number=request.phone_number,
                    nickname=nickname
                )
                logger.info(f"创建新手机号用户成功: {request.phone_number}, user_id: {user.id}")
            except Exception as create_error:
                logger.error(f"创建用户失败: {str(create_error)}")
                logger.error(f"错误类型: {type(create_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"创建用户失败: {str(create_error)}"
                )
        else:
            logger.info(f"用户已存在，更新登录时间: {request.phone_number}, user_id: {user.id}")
            # 更新最后登录时间
            user.last_login_at = get_current_timestamp()
            try:
                await user.save(session)
                logger.info(f"手机号用户登录时间更新成功: {request.phone_number}")
            except Exception as save_error:
                logger.error(f"更新用户登录时间失败: {str(save_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"更新用户信息失败: {str(save_error)}"
                )
        
        logger.info(f"开始生成JWT token: user_id={user.id}")
        
        # 生成JWT token
        jwt_service = get_jwt_service()
        access_token = jwt_service.generate_access_token(
            user_id=user.id,
            user_type=user.user_type.value,
            phone_number=user.phone_number,
            nickname=user.nickname
        )
        refresh_token = jwt_service.generate_refresh_token(
            user_id=user.id,
            user_type=user.user_type.value
        )
        
        logger.info(f"JWT token生成成功，登录完成: {request.phone_number}")
        
        return create_response(
            success=True,
            message="登录成功",
            data={
                "user": {
                    "id": user.id,
                    "phone_number": user.phone_number,
                    "nickname": user.nickname,
                    "user_type": user.user_type.value,
                    "phone_verified": user.is_phone_verified()
                },
                "tokens": {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "Bearer",
                    "expires_in": 60 * 24 * 7 * 60  # 7天，秒为单位
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证码登录时发生错误: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/flash-login", response_model=ApiResponse)
async def flash_login(
    request: FlashLoginRequest,
    session: DatabaseSession = Depends(get_db_session)
):
    """
    闪验一键登录（移动端）
    
    Args:
        request: 闪验登录请求
        session: 数据库会话
        
    Returns:
        ApiResponse: 登录结果，包含JWT token
    """
    try:
        # 获取闪验服务
        flash_service = get_flash_service()
        
        # 置换手机号
        flash_result = await flash_service.exchange_mobile_token(
            token=request.token,
            client_ip=request.client_ip,
            out_id=request.out_id,
            encrypt_type=request.encrypt_type
        )
        
        if not flash_result.success:
            logger.error(f"闪验置换手机号失败: {flash_result.message}")
            raise HTTPException(
                status_code=400,
                detail=f"一键登录失败: {flash_result.message}"
            )
        
        # 获取手机号
        phone_number = flash_result.data.get('phone_number')
        if not phone_number:
            raise HTTPException(
                status_code=400,
                detail="未能获取手机号"
            )
        
        logger.info(f"闪验获取手机号成功: {phone_number}")
        
        # 查找或创建用户
        user = await User.find_by_phone(session, phone_number)
        if not user:
            # 创建新用户
            nickname = request.nickname or PhoneValidator.mask(phone_number)
            user = await User.create_phone_user(
                session=session,
                phone_number=phone_number,
                nickname=nickname
            )
            logger.info(f"创建新闪验用户: {phone_number}")
        else:
            # 更新最后登录时间
            user.last_login_at = get_current_timestamp()
            await user.save(session)
            logger.info(f"闪验用户登录: {phone_number}")
        
        # 生成JWT token
        jwt_service = get_jwt_service()
        access_token = jwt_service.generate_access_token(
            user_id=user.id,
            user_type=user.user_type.value,
            phone_number=user.phone_number,
            nickname=user.nickname
        )
        refresh_token = jwt_service.generate_refresh_token(
            user_id=user.id,
            user_type=user.user_type.value
        )
        
        return create_response(
            success=True,
            message="一键登录成功",
            data={
                "user": {
                    "id": user.id,
                    "phone_number": user.phone_number,
                    "nickname": user.nickname,
                    "user_type": user.user_type.value,
                    "phone_verified": user.is_phone_verified()
                },
                "tokens": {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "Bearer",
                    "expires_in": 60 * 24 * 7 * 60  # 7天，秒为单位
                },
                "flash": {
                    "trade_no": flash_result.trade_no,
                    "charge_status": flash_result.charge_status
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"闪验一键登录时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"一键登录失败: {str(e)}"
        )

@router.post("/flash-validate", response_model=ApiResponse)
async def flash_validate(request: FlashValidateRequest):
    """
    闪验本机校验（Web端）
    
    Args:
        request: 闪验校验请求
        
    Returns:
        ApiResponse: 校验结果
    """
    try:
        # 获取闪验服务
        flash_service = get_flash_service()
        
        # 本机校验
        flash_result = await flash_service.validate_mobile_web(
            token=request.token,
            mobile=request.mobile,
            out_id=request.out_id
        )
        
        return create_response(
            success=flash_result.success,
            message=flash_result.message,
            data={
                "is_verify": flash_result.data.get('is_verify', False) if flash_result.data else False,
                "mobile": request.mobile,
                "trade_no": flash_result.trade_no,
                "charge_status": flash_result.charge_status,
                "code": flash_result.code
            }
        )
        
    except Exception as e:
        logger.error(f"闪验本机校验时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"本机校验失败: {str(e)}"
        )

@router.post("/refresh-token", response_model=ApiResponse)
async def refresh_access_token(request: RefreshTokenRequest):
    """
    刷新访问令牌
    
    Args:
        request: 刷新token请求
        
    Returns:
        ApiResponse: 新的访问令牌
    """
    try:
        jwt_service = get_jwt_service()
        new_access_token = jwt_service.refresh_access_token(request.refresh_token)
        
        if not new_access_token:
            raise HTTPException(
                status_code=401,
                detail="刷新令牌无效或已过期"
            )
        
        return create_response(
            success=True,
            message="令牌刷新成功",
            data={
                "access_token": new_access_token,
                "token_type": "Bearer",
                "expires_in": 60 * 24 * 7 * 60  # 7天，秒为单位
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"刷新令牌失败: {str(e)}"
        )

@router.get("/user-info", response_model=ApiResponse)
async def get_user_info(
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    获取用户信息
    
    Args:
        user_id: 当前用户ID（从JWT token获取，必需）
        session: 数据库会话
        
    Returns:
        ApiResponse: 用户信息
    """
    try:
        logger.info(f"获取用户信息请求: user_id={user_id}")
        
        # 从数据库获取最新用户信息
        user = await User.find_by_id(session, user_id)
        if not user:
            logger.warning(f"用户不存在: {user_id}")
            raise HTTPException(
                status_code=404,
                detail="用户不存在或已被删除"
            )
        
        logger.info(f"成功获取用户信息: {user.id}")
        
        return create_response(
            success=True,
            message="获取用户信息成功",
            data={
                "user": {
                    "id": user.id,
                    "phone_number": user.phone_number,
                    "nickname": user.nickname,
                    "user_type": user.user_type.value,
                    "status": user.status.value,
                    "phone_verified": user.is_phone_verified(),
                    "is_guide": user.is_guide,
                    "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
                    "created_at": user.created_at.isoformat() if user.created_at else None
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息时发生错误: {str(e)}")
        import traceback
        logger.error(f"错误栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"获取用户信息失败: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """健康检查接口"""
    from config.settings import settings
    
    return create_response(
        success=True,
        message="认证服务运行正常",
        data={
            "service": "auth-api",
            "status": "healthy",
            "features": {
                "sms_verification": bool(settings.sms.account and settings.sms.password),
                "flash_login": settings.flash.is_configured(),
                "jwt_token": settings.jwt.secret_key != "your-super-secret-jwt-key"
            },
            "timestamp": get_current_timestamp().isoformat()
        }
    )

class DeleteAccountRequest(BaseModel):
    """注销账户请求模型"""
    confirm: bool = False  # 确认删除标识
    
    @validator('confirm')
    def validate_confirm(cls, v):
        """验证确认删除"""
        if not v:
            raise ValueError('必须确认删除操作')
        return v

@router.post("/delete-account", response_model=ApiResponse)
async def delete_account(
    request: DeleteAccountRequest,
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    注销账户接口
    
    功能：
    - 验证用户身份（通过依赖注入）
    - 删除用户的所有记忆数据
    - 删除用户的短信验证记录
    - 删除用户主记录
    - 使用事务确保数据一致性
    
    Args:
        request: 注销账户请求
        user_id: 当前用户ID（从JWT token获取，必需）
        session: 数据库会话
        
    Returns:
        ApiResponse: 注销结果
        
    注意：
        这是一个不可逆操作，会彻底删除用户的所有数据
    """
    try:
        logger.warning(f"收到账户注销请求，用户ID: {user_id}")
        
        # 步骤1: 验证用户存在（从数据库查询）
        user = await User.find_by_id(session, user_id)
        if not user:
            logger.error(f"要注销的用户不存在: {user_id}")
            raise HTTPException(
                status_code=404,
                detail="用户不存在或已被删除"
            )
        
        logger.warning(f"确认用户存在，开始删除数据: user_id={user_id}, phone={user.phone_number}")
        
        # 步骤2: 开始事务性删除操作
        deletion_results = {
            "memory_deleted": False,
            "sms_records_deleted": 0,
            "user_record_deleted": False
        }
        
        try:
            # 删除用户记忆数据（使用Memory模块）
            logger.warning(f"开始删除用户记忆数据: user_id={user_id}")
            try:
                from core.memory import Memory
                memory = Memory()  # 使用默认配置
                memory_deletion_result = memory.delete_user_memories(user_id)
                deletion_results["memory_deleted"] = memory_deletion_result
                
                if memory_deletion_result:
                    logger.info(f"用户记忆数据删除成功: user_id={user_id}")
                else:
                    logger.warning(f"用户记忆数据删除失败或功能未实现: user_id={user_id}")
                    
            except Exception as memory_error:
                logger.error(f"删除用户记忆数据时发生错误: {str(memory_error)}")
                # 记忆删除失败不应该阻止整个删除流程，因为可能是配置问题
                logger.warning(f"跳过记忆删除错误，继续删除其他数据: user_id={user_id}")
            
            # 删除短信验证记录
            logger.warning(f"开始删除短信验证记录: user_id={user_id}")
            try:
                deleted_sms_count = await SmsVerification.delete_user_records(
                    session, user.phone_number
                )
                deletion_results["sms_records_deleted"] = deleted_sms_count
                logger.info(f"删除短信验证记录: {deleted_sms_count} 条, user_id={user_id}")
                
            except Exception as sms_error:
                logger.error(f"删除短信验证记录时发生错误: {str(sms_error)}")
                # 继续执行，不因为短信记录删除失败而终止
            
            # 删除其他可能的关联数据
            logger.warning(f"检查其他关联数据: user_id={user_id}")
            # 这里可以扩展删除其他业务相关的数据
            # 例如：用户的分析记录、情绪支持记录等
            # 目前先记录日志，后续可以根据业务需要扩展
            
            # 最后删除用户主记录
            logger.warning(f"开始删除用户主记录: user_id={user_id}")
            await user.delete(session)
            deletion_results["user_record_deleted"] = True
            logger.warning(f"用户主记录删除成功: user_id={user_id}")
            
            # 注意：不需要手动提交事务，FastAPI依赖注入会自动处理
            logger.warning(f"账户注销完成，所有数据已删除: user_id={user_id}")
            
            return create_response(
                success=True,
                message="账户注销成功，所有数据已删除",
                data={
                    "user_id": user_id,
                    "phone_number": user.phone_number,
                    "deletion_summary": {
                        "memory_data": "已尝试删除" if deletion_results["memory_deleted"] else "删除失败或功能未实现",
                        "sms_records": f"已删除 {deletion_results['sms_records_deleted']} 条记录",
                        "user_record": "已删除" if deletion_results["user_record_deleted"] else "删除失败"
                    },
                    "deleted_at": get_current_time_iso(),
                    "warning": "此操作不可逆，所有数据已永久删除"
                }
            )
            
        except Exception as deletion_error:
            # 注意：不需要手动回滚事务，FastAPI依赖注入会自动处理
            logger.error(f"删除过程中发生错误: {str(deletion_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"账户注销失败: {str(deletion_error)}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注销账户时发生未预期错误: {str(e)}")
        import traceback
        logger.error(f"错误栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"注销账户失败: {str(e)}"
        )

@router.get("/account-info", response_model=ApiResponse)  
async def get_account_info(
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    获取账户信息（包含数据统计）
    
    功能：
    - 验证用户身份（通过依赖注入）
    - 返回用户基本信息
    - 统计用户的数据量（记忆、记录等）
    - 为注销账户提供参考信息
    
    Args:
        user_id: 当前用户ID（从JWT token获取，必需）
        session: 数据库会话
        
    Returns:
        ApiResponse: 账户信息和数据统计
    """
    try:
        logger.info(f"获取账户信息请求: user_id={user_id}")
        
        # 获取用户信息（从数据库查询最新数据）
        user = await User.find_by_id(session, user_id)
        if not user:
            logger.warning(f"用户不存在: {user_id}")
            raise HTTPException(
                status_code=404, 
                detail="用户不存在或已被删除"
            )
        
        # 统计用户数据
        data_stats = {
            "memory_count": 0,
            "sms_records_count": 0
        }
        
        # 统计记忆数据
        try:
            from core.memory import Memory
            memory = Memory()
            memory_count = memory.get_user_memory_count(user_id)
            data_stats["memory_count"] = memory_count
        except Exception as e:
            logger.warning(f"获取记忆数据统计失败: {str(e)}")
            data_stats["memory_count"] = "无法获取"
        
        # 统计短信记录
        try:
            sms_count = await SmsVerification.count_user_records(session, user.phone_number)
            data_stats["sms_records_count"] = sms_count
        except Exception as e:
            logger.warning(f"获取短信记录统计失败: {str(e)}")
            data_stats["sms_records_count"] = "无法获取"
        
        logger.info(f"获取账户信息成功: user_id={user.id}")
        
        return create_response(
            success=True,
            message="获取账户信息成功",
            data={
                "user": {
                    "id": user.id,
                    "phone_number": user.phone_number,
                    "nickname": user.nickname,
                    "user_type": user.user_type.value,
                    "status": user.status.value,
                    "phone_verified": user.is_phone_verified(),
                    "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
                    "created_at": user.created_at.isoformat() if user.created_at else None
                },
                "data_statistics": data_stats,
                "deletion_warning": "注销账户将永久删除所有数据，此操作不可逆"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取账户信息时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取账户信息失败: {str(e)}"
        )

class UpdateUserProfileRequest(BaseModel):
    """更新用户资料请求模型"""
    nickname: Optional[str] = Field(None, min_length=1, max_length=100, description="用户昵称")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像链接")
    username: Optional[str] = Field(None, min_length=1, max_length=50, description="用户名")
    is_guide: Optional[bool] = Field(None, description="是否已经引导")
    
    @validator('nickname')
    def validate_nickname(cls, v):
        """验证昵称格式"""
        if v is not None:
            v = v.strip()
            if len(v) > 100:
                raise ValueError('昵称长度不能超过100个字符')
        return v
    
    @validator('avatar_url') 
    def validate_avatar_url(cls, v):
        """验证头像链接格式"""
        if v is not None:
            v = v.strip()
            if v and not v.startswith(('http://', 'https://')):
                raise ValueError('头像链接必须是有效的URL')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if v is not None:
            v = v.strip()
            if len(v) > 50:
                raise ValueError('用户名长度不能超过50个字符')
            # 可以添加更多用户名格式验证规则
            import re
            if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', v):
                raise ValueError('用户名只能包含字母、数字、下划线和中文字符')
        return v
    
    @validator('is_guide')
    def validate_is_guide(cls, v):
        """验证是否已经引导"""
        if v is not None:
            if not isinstance(v, bool):
                raise ValueError('是否已经引导必须是布尔值')
        return v

@router.put("/profile", response_model=ApiResponse)
async def update_user_profile(
    request: UpdateUserProfileRequest,
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    更新用户资料信息
    
    功能：
    - 验证用户身份（通过JWT token）
    - 更新用户的基本资料信息（昵称、头像、用户名）
    - 不允许修改敏感信息（手机号、用户类型等）
    
    可更新字段：
    - nickname: 用户昵称
    - avatar_url: 头像链接
    - username: 用户名
    - is_guide: 是否已经引导
    Args:
        request: 更新用户资料请求
        user_id: 当前用户ID（从JWT token获取）
        session: 数据库会话
        
    Returns:
        ApiResponse: 更新结果和用户信息
    """
    try:
        logger.info(f"更新用户资料请求: user_id={user_id}")
        
        # 检查是否有任何字段需要更新
        update_fields = {k: v for k, v in request.dict().items() if v is not None}
        if not update_fields:
            logger.warning(f"更新请求中没有有效字段: user_id={user_id}")
            raise HTTPException(
                status_code=400,
                detail="请提供至少一个需要更新的字段"
            )
        
        # 获取用户信息
        user = await User.find_by_id(session, user_id)
        if not user:
            logger.warning(f"用户不存在: user_id={user_id}")
            raise HTTPException(
                status_code=404, 
                detail="用户不存在或已被删除"
            )
        
        # 检查用户状态
        if not user.is_active():
            logger.warning(f"用户状态不活跃，禁止更新: user_id={user_id}, status={user.status.value}")
            raise HTTPException(
                status_code=403,
                detail="用户状态不活跃，无法更新资料"
            )
        
        # 记录原始值用于日志
        original_values = {}
        updated_values = {}
        
        # 更新字段
        for field_name, field_value in update_fields.items():
            if hasattr(user, field_name):
                original_values[field_name] = getattr(user, field_name)
                setattr(user, field_name, field_value)
                updated_values[field_name] = field_value
                logger.info(f"更新字段 {field_name}: {original_values[field_name]} -> {field_value}")
        
        # 更新时间戳
        user.updated_at = get_current_timestamp()
        
        # 保存到数据库
        try:
            await user.save(session)
            logger.info(f"用户资料更新成功: user_id={user_id}, 更新字段: {list(updated_values.keys())}")
        except Exception as save_error:
            logger.error(f"保存用户信息失败: user_id={user_id}, error={str(save_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"保存用户信息失败: {str(save_error)}"
            )
        
        # 返回更新后的用户信息
        user_data = {
            "id": user.id,
            "phone_number": user.phone_number,
            "nickname": user.nickname,
            "avatar_url": user.avatar_url,
            "username": user.username,
            "user_type": user.user_type.value,
            "status": user.status.value,
            "is_guide": user.is_guide,
            "phone_verified": user.is_phone_verified(),
            "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            "created_at": user.created_at.isoformat() if user.created_at else None
        }
        
        return create_response(
            success=True,
            message="用户资料更新成功",
            data={
                "user": user_data,
                "updated_fields": list(updated_values.keys()),
                "update_summary": f"成功更新了 {len(updated_values)} 个字段"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户资料时发生错误: user_id={user_id}, error={str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"更新用户资料失败: {str(e)}"
        )

 
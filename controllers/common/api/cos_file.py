"""
COS文件存储API

提供COS文件存储和查询功能的HTTP API接口
参照auth.py实现模式，包含完整的错误处理和统一响应格式
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any
import logging
from datetime import datetime

# 导入数据库相关模块
from database import DatabaseManager
from database.connection.session_manager import DatabaseSession
from database.models.cos_file import CosFile
from database.models.user import User
from database.utils.helpers import get_current_timestamp
from database.utils import get_current_time_iso
from ..services.jwt_service import get_jwt_service, JwtService

# 配置日志
logger = logging.getLogger(__name__)

# 创建全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None

async def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
    return _db_manager

async def get_db_session():
    """获取数据库会话依赖"""
    db_manager = await get_database_manager()
    async with db_manager.get_session() as session:
        yield session

async def get_current_user_id(
    request: Request,
    jwt_service: JwtService = Depends(get_jwt_service)
) -> str:
    """
    从JWT token中获取当前用户ID（必需）
    
    Args:
        request: FastAPI请求对象
        jwt_service: JWT服务实例
        
    Returns:
        str: 用户ID
        
    Raises:
        HTTPException: 当未认证或认证失败时抛出401错误
    """
    try:
        # 从请求头获取Authorization token
        authorization = request.headers.get("Authorization")
        if not authorization:
            logger.warning("请求缺少Authorization头部")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未提供认证令牌"
            )
        
        # 提取token
        from ..services.jwt_service import extract_token_from_header
        token = extract_token_from_header(authorization)
        
        if not token:
            logger.warning("Token格式错误")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌格式错误"
            )
        
        # 验证token并获取用户信息
        user_info = jwt_service.get_user_from_token(token)
        
        if not user_info:
            logger.warning("Token验证失败")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌无效"
            )
        
        user_id = user_info.get("user_id")
        if not user_id:
            logger.warning("Token中缺少用户ID信息")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌中缺少用户信息"
            )
        
        logger.info(f"用户认证成功: {user_id}")
        return user_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户ID失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证验证失败"
        )

# 创建路由器
router = APIRouter(prefix="/api/cos-file", tags=["COS文件存储"])

# Pydantic模型定义
class SaveCosFileRequest(BaseModel):
    """保存COS文件请求模型"""
    file_key: str = Field(..., description="文件键名", min_length=1, max_length=500)
    file_info: Dict[str, Any] = Field(..., description="文件信息")
    bucket: str = Field(..., description="存储桶名称", min_length=1, max_length=100)
    region: str = Field(..., description="地域", min_length=1, max_length=50)
    
    @validator('file_key')
    def validate_file_key(cls, v):
        """验证文件键名格式"""
        if not v or v.strip() == "":
            raise ValueError('文件键名不能为空')
        # 可以添加更多验证规则，如路径格式等
        return v.strip()
    
    @validator('file_info')
    def validate_file_info(cls, v):
        """验证文件信息"""
        if not isinstance(v, dict):
            raise ValueError('文件信息必须是字典格式')
        if not v:
            raise ValueError('文件信息不能为空')
        return v
    
    @validator('bucket')
    def validate_bucket(cls, v):
        """验证存储桶名称"""
        if not v or v.strip() == "":
            raise ValueError('存储桶名称不能为空')
        return v.strip()
    
    @validator('region')
    def validate_region(cls, v):
        """验证地域"""
        if not v or v.strip() == "":
            raise ValueError('地域不能为空')
        return v.strip()

class CosFileResponse(BaseModel):
    """COS文件响应模型"""
    id: str
    user_id: str
    file_key: str
    file_info: Dict[str, Any]
    bucket: str
    region: str
    created_at: str
    updated_at: str

# API响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str

def create_response(success: bool, message: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """创建标准响应"""
    return {
        "success": success,
        "message": message,
        "data": data or {},
        "timestamp": get_current_time_iso()
    }

def cos_file_to_dict(cos_file: CosFile) -> Dict[str, Any]:
    """将CosFile对象转换为字典"""
    return {
        "id": cos_file.id,
        "user_id": cos_file.user_id,
        "file_key": cos_file.file_key,
        "file_info": cos_file.file_info,
        "bucket": cos_file.bucket,
        "region": cos_file.region,
        "created_at": cos_file.created_at.isoformat() if cos_file.created_at else "",
        "updated_at": cos_file.updated_at.isoformat() if cos_file.updated_at else ""
    }

async def validate_user_exists(session: DatabaseSession, user_id: str):
    """验证用户是否存在"""
    user = await User.find_by_id(session, user_id)
    if not user:
        logger.warning(f"用户不存在: {user_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

@router.post("/save", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def save_cos_file(
    request: SaveCosFileRequest,
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    将COS文件信息存储到表cos_files中
    
    Args:
        request: 保存COS文件请求
        user_id: 当前用户ID（从JWT token中获取）
        session: 数据库会话
        
    Returns:
        ApiResponse: 保存结果，包含文件信息
        
    Raises:
        HTTPException: 当参数无效或保存失败时抛出
    """
    try:
        logger.info(f"用户 {user_id} 开始保存COS文件: {request.file_key}")
        
        # 验证用户是否存在
        await validate_user_exists(session, user_id)
        
        # 检查该用户的文件是否已存在（同一用户下文件键名唯一）
        existing_file = await CosFile.find_by_user_and_file_key(session, user_id, request.file_key)
        if existing_file:
            logger.warning(f"用户 {user_id} 的文件已存在: {request.file_key}")
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"文件键名 {request.file_key} 已存在"
            )
        
        # 创建COS文件记录
        cos_file = CosFile(
            user_id=user_id,
            file_key=request.file_key,
            file_info=request.file_info,
            bucket=request.bucket,
            region=request.region
        )
        
        # 保存到数据库
        cos_file = await cos_file.save(session)
        
        logger.info(f"用户 {user_id} COS文件保存成功: {cos_file.id}, file_key: {request.file_key}")
        
        return create_response(
            success=True,
            message="COS文件保存成功",
            data={"cos_file": cos_file_to_dict(cos_file)}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存COS文件时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存文件失败: {str(e)}"
        )

@router.get("/get-by-file-key", response_model=ApiResponse)
async def get_cos_file_by_file_key(
    file_key: str = Query(..., description="文件键名", min_length=1, max_length=500),
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    根据文件键名从表cos_files中获取COS文件信息（仅限用户自己的文件）
    
    Args:
        file_key: 文件键名
        user_id: 当前用户ID（从JWT token中获取）
        session: 数据库会话
        
    Returns:
        ApiResponse: 查询结果，包含文件详细信息
        
    Raises:
        HTTPException: 当文件不存在或无权限访问时抛出
    """
    try:
        logger.info(f"用户 {user_id} 开始查询COS文件: {file_key}")
        
        # 验证参数
        if not file_key or file_key.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件键名不能为空"
            )
        
        file_key = file_key.strip()
        
        # 查询用户的COS文件（权限控制：只能查询自己的文件）
        cos_file = await CosFile.find_by_user_and_file_key(session, user_id, file_key)
        if not cos_file:
            logger.warning(f"用户 {user_id} 的COS文件不存在: {file_key}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文件键名 {file_key} 对应的文件不存在或无权限访问"
            )
        
        logger.info(f"用户 {user_id} COS文件查询成功: {cos_file.id}, file_key: {file_key}")
        
        return create_response(
            success=True,
            message="COS文件查询成功",
            data={"cos_file": cos_file_to_dict(cos_file)}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询COS文件时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询文件失败: {str(e)}"
        )

@router.get("/my-files", response_model=ApiResponse)
async def get_my_files(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小"),
    bucket: Optional[str] = Query(None, description="存储桶过滤"),
    region: Optional[str] = Query(None, description="地域过滤"),
    user_id: str = Depends(get_current_user_id),
    session: DatabaseSession = Depends(get_db_session)
):
    """
    获取当前用户的COS文件列表
    
    Args:
        page: 页码，从1开始
        page_size: 每页大小，1-100
        bucket: 存储桶名称过滤（可选）
        region: 地域过滤（可选）
        user_id: 当前用户ID（从JWT token中获取）
        session: 数据库会话
        
    Returns:
        ApiResponse: 用户文件列表和分页信息
    """
    try:
        logger.info(f"用户 {user_id} 获取文件列表，页码: {page}, 大小: {page_size}")
        
        # 分页查询用户文件
        files, total_count = await CosFile.find_with_pagination(
            session=session,
            user_id=user_id,
            bucket=bucket,
            region=region,
            page=page,
            page_size=page_size
        )
        
        # 转换为响应格式
        file_list = [cos_file_to_dict(file) for file in files]
        
        # 计算分页信息
        total_pages = (total_count + page_size - 1) // page_size
        has_next = page < total_pages
        has_prev = page > 1
        
        logger.info(f"用户 {user_id} 文件列表查询成功，共 {total_count} 个文件")
        
        return create_response(
            success=True,
            message="获取文件列表成功",
            data={
                "files": file_list,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev
                },
                "filters": {
                    "bucket": bucket,
                    "region": region
                }
            }
        )
        
    except Exception as e:
        logger.error(f"用户 {user_id} 获取文件列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文件列表失败: {str(e)}"
        )

@router.get("/health", response_model=ApiResponse)
async def health_check():
    """COS文件服务健康检查接口"""
    return create_response(
        success=True,
        message="COS文件服务运行正常",
        data={"service": "cos-file-api", "status": "healthy"}
    )
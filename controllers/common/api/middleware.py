"""
API中间件

包含请求日志记录、异常处理等中间件
"""

import asyncio
import logging
from fastapi import Request
from datetime import datetime

logger = logging.getLogger(__name__)

async def log_requests(request: Request, call_next):
    """
    记录请求日志中间件
    
    Args:
        request: HTTP请求
        call_next: 下一个处理函数
        
    Returns:
        Response: HTTP响应
    """
    start_time = asyncio.get_event_loop().time()
    
    # 记录请求开始
    logger.info(f"🔄 {request.method} {request.url.path} - 开始处理")
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = (asyncio.get_event_loop().time() - start_time) * 1000
        
        # 记录响应
        logger.info(
            f"✅ {request.method} {request.url.path} - "
            f"状态码: {response.status_code}, 耗时: {process_time:.2f}ms"
        )
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        return response
        
    except Exception as e:
        # 计算处理时间
        process_time = (asyncio.get_event_loop().time() - start_time) * 1000
        
        # 记录错误
        logger.error(
            f"❌ {request.method} {request.url.path} - "
            f"错误: {str(e)}, 耗时: {process_time:.2f}ms"
        )
        raise 
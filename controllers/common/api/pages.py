"""
H5页面路由模块

提供用户协议、隐私协议等H5页面的路由处理
专为移动端优化，支持响应式设计
"""

import os
import logging
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse, JSONResponse, RedirectResponse

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="",  # 不使用前缀，直接在根路径下
    tags=["H5页面"],
    responses={
        404: {"description": "页面未找到"},
        500: {"description": "服务器内部错误"}
    }
)

# 获取当前模块的目录路径
current_dir = os.path.dirname(__file__)
# 向上一级到common目录
common_dir = os.path.dirname(current_dir)
# 构建templates目录路径
templates_dir = os.path.join(common_dir, "templates")

@router.get("/h5/user-agreement")
async def user_agreement_page():
    """
    用户协议H5页面路由
    
    返回移动端友好的用户协议页面，包含完整的菲玲用户协议内容。
    页面特性：
    - 响应式设计，适配不同屏幕尺寸
    - 支持触摸操作和手势导航
    - 包含返回顶部功能
    - 优化的阅读体验
    
    Returns:
        FileResponse: 用户协议HTML页面
        
    Raises:
        HTTPException: 当文件不存在或发生服务器错误时抛出异常
    """
    try:
        user_agreement_file = os.path.join(templates_dir, "user_agreement.html")
        
        if os.path.exists(user_agreement_file):
            logger.info(f"提供用户协议页面: {user_agreement_file}")
            return FileResponse(
                user_agreement_file,
                media_type="text/html",
                headers={
                    "Cache-Control": "public, max-age=3600",  # 缓存1小时
                    "X-Content-Type-Options": "nosniff",      # 防止MIME类型嗅探
                    "X-Frame-Options": "SAMEORIGIN",          # 防止点击劫持
                    "Content-Security-Policy": "default-src 'self' 'unsafe-inline';"  # 基本CSP策略
                }
            )
        else:
            logger.error(f"用户协议文件不存在: {user_agreement_file}")
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "message": "用户协议页面未找到",
                    "error": "user_agreement.html file not found",
                    "file_path": user_agreement_file
                }
            )
            
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"访问用户协议页面时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "message": "服务器内部错误",
                "error": str(e)
            }
        )

@router.get("/h5/privacy-policy")
async def privacy_policy_page():
    """
    隐私协议H5页面路由
    
    返回移动端友好的隐私协议页面，包含完整的菲玲隐私政策内容。
    页面特性：
    - 响应式设计，适配不同屏幕尺寸
    - 支持触摸操作和手势导航
    - 包含返回顶部功能
    - 优化的阅读体验
    - 包含完整的9个章节内容
    
    Returns:
        FileResponse: 隐私协议HTML页面
        
    Raises:
        HTTPException: 当文件不存在或发生服务器错误时抛出异常
    """
    try:
        privacy_policy_file = os.path.join(templates_dir, "privacy_policy.html")
        
        if os.path.exists(privacy_policy_file):
            logger.info(f"提供隐私协议页面: {privacy_policy_file}")
            return FileResponse(
                privacy_policy_file,
                media_type="text/html",
                headers={
                    "Cache-Control": "public, max-age=3600",  # 缓存1小时
                    "X-Content-Type-Options": "nosniff",      # 防止MIME类型嗅探
                    "X-Frame-Options": "SAMEORIGIN",          # 防止点击劫持
                    "Content-Security-Policy": "default-src 'self' 'unsafe-inline';"  # 基本CSP策略
                }
            )
        else:
            logger.error(f"隐私协议文件不存在: {privacy_policy_file}")
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "message": "隐私协议页面未找到",
                    "error": "privacy_policy.html file not found",
                    "file_path": privacy_policy_file
                }
            )
            
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"访问隐私协议页面时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "message": "服务器内部错误",
                "error": str(e)
            }
        )

@router.get("/agreement")
async def user_agreement_redirect():
    """
    用户协议页面重定向路由
    
    提供简短的URL访问方式，重定向到完整的用户协议页面。
    使用301永久重定向，有利于SEO优化。
    
    Returns:
        RedirectResponse: 重定向到 /h5/user-agreement
    """
    logger.info("重定向到用户协议页面")
    return RedirectResponse(
        url="/h5/user-agreement", 
        status_code=301  # 永久重定向
    )

@router.get("/privacy")
async def privacy_policy_redirect():
    """
    隐私协议页面重定向路由
    
    提供简短的URL访问方式，重定向到完整的隐私协议页面。
    使用301永久重定向，有利于SEO优化。
    
    Returns:
        RedirectResponse: 重定向到 /h5/privacy-policy
    """
    logger.info("重定向到隐私协议页面")
    return RedirectResponse(
        url="/h5/privacy-policy", 
        status_code=301  # 永久重定向
    )

@router.get("/h5/pages/health")
async def pages_health_check():
    """
    H5页面模块健康检查
    
    检查模板文件是否存在，验证页面服务状态。
    
    Returns:
        dict: 健康检查结果和页面状态信息
    """
    try:
        user_agreement_file = os.path.join(templates_dir, "user_agreement.html")
        privacy_policy_file = os.path.join(templates_dir, "privacy_policy.html")
        css_file = os.path.join(templates_dir, "../static/css/user_agreement.css")
        
        # 检查文件状态
        files_status = {
            "user_agreement": {
                "exists": os.path.exists(user_agreement_file),
                "size": os.path.getsize(user_agreement_file) if os.path.exists(user_agreement_file) else 0,
                "path": user_agreement_file
            },
            "privacy_policy": {
                "exists": os.path.exists(privacy_policy_file),
                "size": os.path.getsize(privacy_policy_file) if os.path.exists(privacy_policy_file) else 0,
                "path": privacy_policy_file
            },
            "css_styles": {
                "exists": os.path.exists(css_file),
                "size": os.path.getsize(css_file) if os.path.exists(css_file) else 0,
                "path": css_file
            }
        }
        
        # 计算健康状态
        all_files_exist = all(file_info["exists"] for file_info in files_status.values())
        
        return {
            "success": True,
            "message": "H5页面模块健康检查",
            "data": {
                "status": "healthy" if all_files_exist else "degraded",
                "module": "pages",
                "version": "1.0.0",
                "files": files_status,
                "available_urls": [
                    "/h5/user-agreement",
                    "/h5/privacy-policy",
                    "/agreement",
                    "/privacy"
                ],
                "templates_dir": templates_dir
            },
            "timestamp": "2024-01-01T00:00:00.000Z"
        }
        
    except Exception as e:
        logger.error(f"H5页面健康检查失败: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": "H5页面模块健康检查失败",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00.000Z"
        } 
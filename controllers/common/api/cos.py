"""
腾讯云COS临时密钥API

提供生成COS临时访问密钥的RESTful API接口
支持前端安全上传文件到腾讯云COS存储桶
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field, validator
from datetime import datetime

from database.utils import get_current_time_iso
from ..services.cos_service import get_cos_sts_service, CosSTSService
from ..services.jwt_service import get_jwt_service, JwtService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/cos", tags=["COS存储"])

class TempCredentialsRequest(BaseModel):
    """
    临时密钥请求模型
    
    前端传入的参数，不包含敏感的密钥信息
    """
    bucket: str = Field(..., min_length=3, max_length=63, description="存储桶名称，格式: bucketName-appid，例如: examplebucket-1250000000")
    region: str = Field(..., min_length=1, max_length=32, description="存储桶地域，如: ap-guangzhou")
    allow_prefix: List[str] = Field(..., min_items=1, description="允许的资源前缀列表")
    # allow_actions: List[str] = Field(..., min_items=1, description="允许的COS操作列表")  
    duration_seconds: Optional[int] = Field(None, ge=900, le=7200, description="有效期秒数，默认1800秒，最大7200秒")
    
    @validator('bucket')
    def validate_bucket(cls, v):
        """验证存储桶名称格式"""
        if not v or not v.strip():
            raise ValueError("存储桶名称不能为空")
        
        bucket = v.strip().lower()
        
        # 验证存储桶格式: bucketName-appid
        if '-' not in bucket:
            raise ValueError("存储桶名称格式错误，应为 bucketName-appid 格式，例如: examplebucket-1250000000")
        
        parts = bucket.split('-')
        if len(parts) < 2:
            raise ValueError("存储桶名称格式错误，应为 bucketName-appid 格式")
        
        # 验证AppID (最后一部分应该是纯数字)
        appid = parts[-1]
        if not appid.isdigit():
            raise ValueError(f"AppID格式错误，应为纯数字，实际: {appid}")
        
        # 验证存储桶名称部分
        bucket_name = '-'.join(parts[:-1])
        if not bucket_name.replace('-', '').replace('_', '').isalnum():
            raise ValueError("存储桶名称只能包含字母、数字、连字符和下划线")
        
        return bucket
    
    @validator('region')
    def validate_region(cls, v):
        """验证地域格式"""
        if not v or not v.strip():
            raise ValueError("存储桶地域不能为空")
        
        region = v.strip().lower()
        
        # 检查是否为有效的腾讯云地域
        valid_regions = {
            'ap-beijing', 'ap-nanjing', 'ap-shanghai', 'ap-guangzhou', 'ap-chengdu',
            'ap-chongqing', 'ap-shenzhen-fsi', 'ap-shanghai-fsi', 'ap-beijing-fsi',
            'ap-hongkong', 'ap-singapore', 'ap-mumbai', 'ap-jakarta', 'ap-seoul',
            'ap-bangkok', 'ap-tokyo', 'na-siliconvalley', 'na-ashburn',
            'eu-frankfurt', 'eu-moscow'
        }
        
        if region not in valid_regions:
            logger.warning(f"⚠️ 使用了未知的地域: {region}")
        
        return region
    
    @validator('allow_prefix')
    def validate_allow_prefix(cls, v):
        """
        验证资源前缀
        
        前缀将被自动处理为正确的匹配格式：
        - "audio" -> "audio/*" (匹配audio目录下所有文件)
        - "audio/" -> "audio/*" (匹配audio目录下所有文件)
        - "audio/*" -> "audio/*" (保持不变)
        """
        if not v or len(v) == 0:
            raise ValueError("必须指定至少一个资源前缀")
        
        validated_prefixes = []
        for prefix in v:
            if not prefix or not prefix.strip():
                continue
                
            prefix = prefix.strip()
            
            # 检查危险的通配符
            if prefix == "*":
                raise ValueError("不允许使用全局通配符*，请指定具体的路径前缀")
            
            # ✅ 移除客户端前缀处理，交由服务端统一处理
            # 服务端会自动将前缀转换为正确的匹配格式
            validated_prefixes.append(prefix)
        
        if not validated_prefixes:
            raise ValueError("没有有效的资源前缀")
        
        return validated_prefixes
    
# API响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str
    
    
class TempCredentialsResponse(BaseModel):
    """
    临时密钥响应模型
    
    返回给前端的临时密钥信息
    """
    success: bool = Field(..., description="是否成功")
    message: str = Field(default="", description="响应消息")
    credentials: Optional[Dict[str, Any]] = Field(None, description="临时密钥信息")
    request_id: str = Field(default="", description="请求ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据信息")

class ServiceStatusResponse(BaseModel):
    """
    服务状态响应模型
    """
    available: bool = Field(..., description="服务是否可用")
    message: str = Field(..., description="状态消息")
    config_valid: bool = Field(..., description="配置是否有效")
    module_info: Dict[str, Any] = Field(..., description="模块信息")

# 依赖注入函数
async def get_current_user_id(
    request: Request,
    jwt_service: JwtService = Depends(get_jwt_service)
) -> str:
    """
    从JWT token中获取当前用户ID（必需认证）
    
    Args:
        request: FastAPI请求对象
        jwt_service: JWT服务实例
        
    Returns:
        str: 用户ID
        
    Raises:
        HTTPException: 当未认证或认证失败时抛出401错误
    """
    try:
        # 从请求头获取Authorization token
        authorization = request.headers.get("Authorization")
        if not authorization:
            logger.warning("请求缺少Authorization头部")
            raise HTTPException(
                status_code=401,
                detail="未提供认证令牌"
            )
        
        # 提取token
        from ..services.jwt_service import extract_token_from_header
        token = extract_token_from_header(authorization)
        
        if not token:
            logger.warning("Token格式错误")
            raise HTTPException(
                status_code=401,
                detail="认证令牌格式错误"
            )
        
        # 验证token并获取用户信息
        user_info = jwt_service.get_user_from_token(token)
        
        if not user_info:
            logger.warning("Token验证失败")
            raise HTTPException(
                status_code=401,
                detail="认证令牌无效"
            )
        
        user_id = user_info.get("user_id")
        if not user_id:
            logger.warning("Token中缺少用户ID信息")
            raise HTTPException(
                status_code=401,
                detail="认证令牌中缺少用户信息"
            )
        
        logger.info(f"用户认证成功: {user_id}")
        return user_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户ID失败: {e}")
        raise HTTPException(
            status_code=401,
            detail="认证验证失败"
        )

@router.get("/status", response_model=ServiceStatusResponse)
async def get_service_status(
    cos_service: CosSTSService = Depends(get_cos_sts_service)
):
    """
    获取COS STS服务状态
    
    检查服务配置是否正确，是否可以正常使用
    
    Returns:
        ServiceStatusResponse: 服务状态信息
    """
    logger.info("🔍 检查COS STS服务状态")
    
    try:
        # 检查服务配置
        config_valid = cos_service._validate_config()
        
        # 获取模块信息
        from ..services.cos_service import get_module_info
        module_info = get_module_info()
        
        if config_valid:
            message = "COS STS服务配置正确，可以正常使用"
            logger.info("✅ COS STS服务状态正常")
        else:
            message = "COS STS服务配置不完整，请检查环境变量配置"
            logger.warning("⚠️ COS STS服务配置不完整")
        
        return ServiceStatusResponse(
            available=config_valid,
            message=message,
            config_valid=config_valid,
            module_info=module_info
        )
        
    except Exception as e:
        logger.error(f"检查服务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"检查服务状态失败: {str(e)}"
        )

@router.post("/temp-credentials", response_model=ApiResponse)
async def generate_temp_credentials(
    request_data: TempCredentialsRequest,
    user_id: str = Depends(get_current_user_id),
    cos_service: CosSTSService = Depends(get_cos_sts_service)
):
    """
    生成腾讯云COS临时访问密钥
    
    基于前端传入的参数生成临时密钥，用于安全上传文件到COS
    不包含敏感的API密钥信息，所有敏感配置都在后端管理
    
    Args:
        request_data: 临时密钥请求参数
        user_id: 当前用户ID (从JWT token获取，必需)
        cos_service: COS STS服务实例
        
    Returns:
        TempCredentialsResponse: 临时密钥信息
        
    Raises:
        HTTPException: 当参数验证失败或服务错误时抛出
    """
    # 后端固定配置的COS操作权限列表（用户不需要传入）
    allow_actions = [
        "name/cos:PutObject",
        "name/cos:PostObject",
        "name/cos:InitiateMultipartUpload",
        "name/cos:UploadPart",
        "name/cos:CompleteMultipartUpload",
        "name/cos:AbortMultipartUpload",
        "name/cos:ListMultipartUploads",
        "name/cos:ListParts",
        "name/cos:GetObject",
    ]
    
    logger.info(f"🔑 用户 {user_id} 请求生成COS临时密钥")
    logger.info(f"存储桶: {request_data.bucket}, 地域: {request_data.region}")
    logger.info(f"路径前缀: {request_data.allow_prefix}, 允许操作: {allow_actions}")
    logger.info(f"✨ 将构建标准QCS资源表达式格式: qcs::cos:{request_data.region}:uid/{{appid}}:{request_data.bucket}/{{prefix}}")
    
    try:
        # 验证服务可用性
        if not cos_service._validate_config():
            logger.error("COS STS服务配置不完整")
            raise HTTPException(
                status_code=503,
                detail="COS STS服务暂时不可用，请联系管理员检查配置"
            )
        
        # 生成临时密钥
        result = await cos_service.generate_temp_credentials(
            bucket=request_data.bucket,
            region=request_data.region,
            allow_prefix=request_data.allow_prefix,
            allow_actions=allow_actions,
            duration_seconds=request_data.duration_seconds,
            user_id=user_id
        )
        
        logger.info(f"✅ 临时密钥生成成功，请求ID: {result.get('request_id', 'N/A')}")
        
        return ApiResponse(
            success=True,
            message="临时密钥生成成功",
            data=result,
            timestamp=get_current_time_iso()
        )
        
    except ValueError as e:
        logger.warning(f"参数验证失败: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"请求参数无效: {str(e)}"
        )
    except RuntimeError as e:
        logger.error(f"生成临时密钥失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成临时密钥失败: {str(e)}"
        )
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"服务内部错误: {str(e)}"
        )

class QuickUploadRequest(BaseModel):
    """快速上传请求模型"""
    bucket: str = Field(..., description="存储桶名称")
    region: str = Field(..., description="存储桶地域")
    prefix: str = Field(default="uploads/", description="上传路径前缀")

@router.post("/quick-upload-credentials", response_model=ApiResponse)
async def generate_quick_upload_credentials(
    request_data: QuickUploadRequest,
    user_id: str = Depends(get_current_user_id),
    cos_service: CosSTSService = Depends(get_cos_sts_service)
):
    """
    快速生成上传文件的临时密钥
    
    简化版接口，使用预设的常用参数快速生成上传权限
    适合常见的文件上传场景
    
    Args:
        request_data: 快速上传请求参数
        user_id: 当前用户ID (必需)
        cos_service: COS STS服务实例
        
    Returns:
        TempCredentialsResponse: 临时密钥信息
    """
    # 快速上传的默认操作权限（后端固定配置）
    allow_actions = [
        "name/cos:PutObject",
        "name/cos:PostObject", 
        "name/cos:InitiateMultipartUpload",
        "name/cos:UploadPart",
        "name/cos:CompleteMultipartUpload",
        "name/cos:AbortMultipartUpload"
    ]
    
    logger.info(f"🚀 用户 {user_id} 请求快速上传密钥")
    logger.info(f"存储桶: {request_data.bucket}, 地域: {request_data.region}")
    logger.info(f"前缀: {request_data.prefix}, 操作: {allow_actions}")
    
    try:
        # 使用预设的上传权限参数
        allow_prefix = [request_data.prefix] if request_data.prefix else ["uploads/"]
        
        # 调用主要接口
        temp_request_data = TempCredentialsRequest(
            bucket=request_data.bucket,
            region=request_data.region,
            allow_prefix=allow_prefix,
            allow_actions=allow_actions,
            duration_seconds=1800  # 30分钟
        )
        
        return await generate_temp_credentials(temp_request_data, user_id, cos_service)
        
    except Exception as e:
        logger.error(f"快速生成上传密钥失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"快速生成上传密钥失败: {str(e)}"
        )

# 导出路由器
__all__ = ["router"]

if __name__ == "__main__":
    # API文档示例
    print("🚀 腾讯云COS临时密钥API")
    print("\n📋 支持的接口:")
    print("GET  /cos/status                   - 获取服务状态")
    print("POST /cos/temp-credentials         - 生成临时密钥")
    print("POST /cos/quick-upload-credentials - 快速生成上传密钥")
    
    print("\n📝 请求示例:")
    example_request = {
        "bucket": "my-bucket-123456789",  # 必须包含AppID: bucketName-appid
        "region": "ap-guangzhou",
        "allow_prefix": ["images/", "documents/"],  # 简单前缀，将自动转换为QCS资源表达式
        "duration_seconds": 1800
    }
    
    print("\n🔧 修复说明:")
    print("- 存储桶名称必须包含AppID: bucketName-appid")
    print("- 路径前缀将自动转换为标准QCS资源表达式")
    print("- 格式: qcs::cos:region:uid/appid:bucket/prefix")
    print("- 同时支持COS和CI(数据万象)资源授权")
    
    import json
    print(json.dumps(example_request, ensure_ascii=False, indent=2)) 
"""
成员相关API
成员的CURD
"""

from fastapi import APIRouter, Depends, Request, HTTPException
from database.connection.session_manager import DatabaseSession
from database import DatabaseManager
from typing import Optional, Dict, Any, List
from enum import Enum
from pydantic import BaseModel, Field
from datetime import datetime
from database.models.member import Member
from database.utils import get_current_time_iso
from ..services import get_jwt_service
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 配置全局数据库器实例
_db_manager: Optional[DatabaseManager] = None

async def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
    return _db_manager

async def get_db_session():
    """获取数据库会话依赖"""
    db_manager = await get_database_manager()
    async with db_manager.get_session() as session:
        yield session

# 创建路由器
router = APIRouter(prefix="/api/member", tags=["成员"])

class MemberType(Enum):
    """成员类型枚举"""
    CHILD = "child"
    FATHER = "father"
    MOTHER = "mother"

class MemberGender(Enum):
    """成员性别枚举"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"

# 标准的API响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str

def create_response(success: bool, message: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """创建标准响应"""
    return {
        "success": success,
        "message": message,
        "data": data or {},
        "timestamp": get_current_time_iso()
    }

# 成员信息验证模型
class CreateMemberRequest(BaseModel):
    """创建成员请求模型"""
    name: str = Field(..., min_length=1, max_length=50, description="成员姓名")
    age: int = Field(..., ge=0, le=150, description="成员年龄")
    gender: MemberGender = Field(..., description="成员性别")
    member_type: MemberType = Field(..., description="成员类型")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            MemberType: lambda v: v.value,
            MemberGender: lambda v: v.value
        }

class UpdateMemberRequest(BaseModel):
    """更新成员请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="成员姓名")
    age: Optional[int] = Field(None, ge=0, le=150, description="成员年龄")
    gender: Optional[MemberGender] = Field(None, description="成员性别")
    member_type: Optional[MemberType] = Field(None, description="成员类型")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            MemberType: lambda v: v.value if v else None,
            MemberGender: lambda v: v.value if v else None
        }

class BatchCreateMemberRequest(BaseModel):
    """批量创建成员请求模型"""
    members: List[CreateMemberRequest] = Field(..., min_items=1, max_items=10, description="成员列表")

# JWT认证依赖函数
async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    获取当前登录用户信息的依赖函数
    
    Args:
        request: HTTP请求对象
        
    Returns:
        Dict[str, Any]: 用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    try:
        # 从header中提取token
        authorization = request.headers.get("Authorization")
        if not authorization:
            logger.warning("请求缺少Authorization头部")
            raise HTTPException(status_code=401, detail="未提供认证令牌")
        
        # 提取token
        from ..services.jwt_service import extract_token_from_header
        token = extract_token_from_header(authorization)
        
        if not token:
            logger.warning("Token格式错误")
            raise HTTPException(status_code=401, detail="认证令牌格式错误")
        
        # 验证token
        jwt_service = get_jwt_service()
        user_info = jwt_service.get_user_from_token(token)
        
        if not user_info:
            logger.warning("Token验证失败")
            raise HTTPException(status_code=401, detail="认证令牌无效")
        
        logger.info(f"用户认证成功: {user_info['user_id']}")
        return user_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户认证时发生错误: {str(e)}")
        raise HTTPException(status_code=401, detail="认证失败")

@router.post("/create", response_model=ApiResponse)
async def create_member(
    request: CreateMemberRequest,
    session: DatabaseSession = Depends(get_db_session),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    创建成员
    
    Args:
        request: 创建成员请求
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 创建结果
    """
    try:
        logger.info(f"开始创建成员: {request.name}, 用户: {current_user['user_id']}")
        
        # 创建成员
        member = await Member.create_member(
            session=session,
            user_id=current_user["user_id"],
            type=request.member_type.value,
            info={
                "name": request.name,
                "age": request.age,
                "gender": request.gender.value
            }
        )
        
        logger.info(f"成员创建成功: {member.id}")
        
        return create_response(
            success=True,
            message="成员创建成功",
            data={
                "member": {
                    "id": member.id,
                    "name": request.name,
                    "age": request.age,
                    "gender": request.gender.value,
                    "type": request.member_type.value,
                    "created_at": member.created_at.isoformat() if member.created_at else None
                }
            }
        )
        
    except Exception as e:
        logger.error(f"创建成员失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"创建成员失败: {str(e)}"
        )

@router.post("/batch-create", response_model=ApiResponse)
async def batch_create_members(
    request: BatchCreateMemberRequest,
    session: DatabaseSession = Depends(get_db_session),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    批量创建成员
    
    Args:
        request: 批量创建请求
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 批量创建结果
    """
    try:
        logger.info(f"开始批量创建{len(request.members)}个成员, 用户: {current_user['user_id']}")
        
        created_members = []
        
        # 批量创建成员
        for member_data in request.members:
            member = await Member.create_member(
                session=session,
                user_id=current_user["user_id"],   
                type=member_data.member_type.value,
                info={
                    "name": member_data.name,
                    "age": member_data.age,
                    "gender": member_data.gender.value
                }
            )
            
            created_members.append({
                "id": member.id,
                "name": member_data.name,
                "age": member_data.age,
                "gender": member_data.gender.value,
                "type": member_data.member_type.value,
                "created_at": member.created_at.isoformat() if member.created_at else None
            })
        
        logger.info(f"批量创建成员成功: {len(created_members)}个")
        
        return create_response(
            success=True,
            message=f"成功创建{len(created_members)}个成员",
            data={"members": created_members}
        )
        
    except Exception as e:
        logger.error(f"批量创建成员失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量创建成员失败: {str(e)}"
        )

@router.get("/{member_id}", response_model=ApiResponse)
async def get_member(
    member_id: str,
    session: DatabaseSession = Depends(get_db_session),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取成员信息
    
    Args:
        member_id: 成员ID
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 成员信息
    """
    try:
        logger.info(f"获取成员信息: {member_id}, 用户: {current_user['user_id']}")
        
        # 获取成员
        member = await Member.find_by_id(session, member_id)
        
        if not member:
            logger.warning(f"成员不存在: {member_id}")
            raise HTTPException(status_code=404, detail="成员不存在")
        
        # 检查权限
        if member.user_id != current_user["user_id"]:
            logger.warning(f"用户{current_user['user_id']}尝试访问不属于自己的成员{member_id}")
            raise HTTPException(status_code=403, detail="无权限访问该成员")
        
        logger.info(f"成员信息获取成功: {member_id}")
        
        return create_response(
            success=True,
            message="成员信息获取成功",
            data={
                "member": {
                    "id": member.id,
                    "name": member.info.get("name") if member.info else None,
                    "age": member.info.get("age") if member.info else None,
                    "gender": member.info.get("gender") if member.info else None,
                    "type": member.type,
                    "created_at": member.created_at.isoformat() if member.created_at else None,
                    "updated_at": member.updated_at.isoformat() if member.updated_at else None
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取成员信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取成员信息失败: {str(e)}"
        )

@router.put("/{member_id}", response_model=ApiResponse)
async def update_member(
    member_id: str,
    request: UpdateMemberRequest,
    session: DatabaseSession = Depends(get_db_session), 
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    更新成员信息
    
    Args:
        member_id: 成员ID
        request: 更新请求
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 更新结果
    """
    try:
        logger.info(f"更新成员信息: {member_id}, 用户: {current_user['user_id']}")
        
        # 获取成员
        member = await Member.find_by_id(session, member_id)
        
        if not member:
            logger.warning(f"成员不存在: {member_id}")
            raise HTTPException(status_code=404, detail="成员不存在")
        
        # 检查权限
        if member.user_id != current_user["user_id"]:
            logger.warning(f"用户{current_user['user_id']}尝试更新不属于自己的成员{member_id}")
            raise HTTPException(status_code=403, detail="无权限修改该成员")
        
        # 构建更新数据
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.age is not None:
            update_data["age"] = request.age
        if request.gender is not None:
            update_data["gender"] = request.gender.value
        
        # 更新成员信息
        if update_data:
            current_info = member.info or {}
            current_info.update(update_data)
            
            updated_member = await Member.update_member(session, member_id, current_info)
            
            # 如果有类型更新
            if request.member_type is not None:
                updated_member.type = request.member_type.value
                await updated_member.save(session)
        else:
            updated_member = member
        
        logger.info(f"成员信息更新成功: {member_id}")
        
        return create_response(
            success=True,
            message="成员信息更新成功",
            data={
                "member": {
                    "id": updated_member.id,
                    "name": updated_member.info.get("name") if updated_member.info else None,
                    "age": updated_member.info.get("age") if updated_member.info else None,
                    "gender": updated_member.info.get("gender") if updated_member.info else None,
                    "type": updated_member.type,
                    "updated_at": updated_member.updated_at.isoformat() if updated_member.updated_at else None
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新成员信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"更新成员信息失败: {str(e)}"
        )

@router.delete("/{member_id}", response_model=ApiResponse)
async def delete_member(
    member_id: str,
    session: DatabaseSession = Depends(get_db_session),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    删除成员
    
    Args:
        member_id: 成员ID
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 删除结果
    """
    try:
        logger.info(f"删除成员: {member_id}, 用户: {current_user['user_id']}")
        
        # 获取成员以检查权限
        member = await Member.find_by_id(session, member_id)
        
        if not member:
            logger.warning(f"成员不存在: {member_id}")
            raise HTTPException(status_code=404, detail="成员不存在")
        
        # 检查权限
        if member.user_id != current_user["user_id"]:
            logger.warning(f"用户{current_user['user_id']}尝试删除不属于自己的成员{member_id}")
            raise HTTPException(status_code=403, detail="无权限删除该成员")
        
        # 删除成员
        success = await Member.delete_member(session, member_id)
        
        if not success:
            logger.error(f"删除成员失败: {member_id}")
            raise HTTPException(status_code=500, detail="删除成员失败")
        
        logger.info(f"成员删除成功: {member_id}")
        
        return create_response(
            success=True,
            message="成员删除成功",
            data={"deleted_member_id": member_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除成员失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"删除成员失败: {str(e)}"
        )

@router.get("/", response_model=ApiResponse)
async def get_members(
    session: DatabaseSession = Depends(get_db_session),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取用户的所有成员列表
    
    Args:
        session: 数据库会话
        current_user: 当前用户信息
        
    Returns:
        ApiResponse: 成员列表
    """
    try:
        logger.info(f"获取成员列表, 用户: {current_user['user_id']}")
        
        # 获取成员列表
        members = await Member.find_by_user(session, current_user["user_id"])
        
        # 构建响应数据
        member_list = []
        for member in members:
            member_list.append({
                "id": member.id,
                "name": member.info.get("name") if member.info else None,
                "age": member.info.get("age") if member.info else None,
                "gender": member.info.get("gender") if member.info else None,
                "type": member.type,
                "created_at": member.created_at.isoformat() if member.created_at else None,
                "updated_at": member.updated_at.isoformat() if member.updated_at else None
            })
        
        logger.info(f"成员列表获取成功: {len(member_list)}个成员")
        
        return create_response(
            success=True,
            message=f"成功获取{len(member_list)}个成员",
            data={
                "members": member_list,
                "total_count": len(member_list)
            }
        )
        
    except Exception as e:
        logger.error(f"获取成员列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取成员列表失败: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """成员服务健康检查"""
    return create_response(
        success=True,
        message="成员服务运行正常",
        data={
            "service": "member-api",
            "status": "healthy",
            "timestamp": get_current_time_iso()
        }
    )


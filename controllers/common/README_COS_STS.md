# 腾讯云COS临时密钥服务

## 概述

基于腾讯云STS（Security Token Service）服务实现的临时密钥生成功能，为前端提供安全的文件上传能力。参考腾讯云官方SDK实现，支持自定义权限策略和访问控制。

## 功能特性

- ✅ **临时密钥生成**: 基于腾讯云STS API生成临时访问凭证
- ✅ **权限控制**: 支持自定义存储桶、路径前缀和操作权限
- ✅ **安全验证**: 严格的参数验证，防止权限泄露
- ✅ **用户认证**: 集成JWT认证，支持用户级别的权限管理
- ✅ **参数预设**: 提供快速上传接口，简化常用场景
- ✅ **完整日志**: 详细的操作日志和错误追踪

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                     腾讯云COS临时密钥服务                      │
├─────────────────────────────────────────────────────────────┤
│  API层 (cos.py)                                            │
│  ├── GET  /api/cos/status                                  │
│  ├── POST /api/cos/temp-credentials                        │
│  └── POST /api/cos/quick-upload-credentials                │
├─────────────────────────────────────────────────────────────┤
│  服务层 (cos_service.py)                                   │
│  ├── CosSTSService                                         │
│  ├── 参数验证                                               │
│  ├── 策略构建                                               │
│  ├── 签名生成                                               │
│  └── STS API调用                                           │
├─────────────────────────────────────────────────────────────┤
│  腾讯云STS API                                              │
│  └── sts.tencentcloudapi.com                               │
└─────────────────────────────────────────────────────────────┘
```

## 环境配置

### 1. 环境变量设置

```bash
# 必需配置
export TENCENT_SECRET_ID="your_secret_id_here"
export TENCENT_SECRET_KEY="your_secret_key_here"

# 可选配置
export COS_TEMP_DURATION="1800"  # 临时密钥有效期（秒），默认1800秒
```

### 2. 依赖安装

```bash
pip install requests==2.32.3
```

### 3. 腾讯云权限配置

确保您的腾讯云API密钥具有以下权限：
- `sts:AssumeRole` - STS角色扮演权限
- `cos:*` - COS相关权限（根据实际需要配置）

## API接口文档

### 1. 服务状态检查

**请求**: `GET /api/cos/status`

**响应**:
```json
{
  "available": true,
  "message": "COS STS服务配置正确，可以正常使用",
  "config_valid": true,
  "module_info": {
    "name": "腾讯云COS STS服务",
    "version": "1.0.0",
    "description": "基于腾讯云STS生成临时访问密钥",
    "reference": "https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/python"
  }
}
```

### 2. 生成临时密钥

**请求**: `POST /api/cos/temp-credentials`

**请求体**:
```json
{
  "bucket": "my-bucket-123456789",
  "region": "ap-guangzhou",
  "allow_prefix": ["images/", "documents/"],
  "allow_actions": ["name/cos:PutObject", "name/cos:GetObject"],
  "duration_seconds": 1800
}
```

**参数说明**:
- `bucket`: 存储桶名称，格式为 `bucketName-appid`
- `region`: 存储桶地域，如 `ap-guangzhou`
- `allow_prefix`: 允许的资源前缀列表，不支持全局通配符 `*`
- `allow_actions`: 允许的COS操作列表
- `duration_seconds`: 有效期秒数（可选，默认1800秒，最大7200秒）

**响应**:
```json
{
  "success": true,
  "message": "临时密钥生成成功",
  "credentials": {
    "tmp_secret_id": "AKID...",
    "tmp_secret_key": "...",
    "session_token": "...",
    "start_time": 1640995200,
    "expired_time": 1640997000
  },
  "request_id": "12345678-1234-1234-1234-123456789012",
  "metadata": {
    "bucket": "my-bucket-123456789",
    "region": "ap-guangzhou",
    "allow_prefix": ["images/", "documents/"],
    "allow_actions": ["name/cos:PutObject", "name/cos:GetObject"],
    "duration_seconds": 1800,
    "session_name": "cos-sts-session-user123-1640995200",
    "generated_at": "2024-01-01T12:00:00.000Z"
  }
}
```

### 3. 快速上传密钥

**请求**: `POST /api/cos/quick-upload-credentials`

**请求体**:
```json
{
  "bucket": "my-bucket-123456789",
  "region": "ap-guangzhou",
  "prefix": "uploads/"
}
```

**响应**: 与临时密钥接口相同，但预设了常用的上传权限。

## 支持的COS操作

| 操作名称 | 说明 |
|---------|------|
| `name/cos:PutObject` | 上传对象 |
| `name/cos:PostObject` | 表单上传对象 |
| `name/cos:GetObject` | 下载对象 |
| `name/cos:DeleteObject` | 删除对象 |
| `name/cos:HeadObject` | 获取对象元数据 |
| `name/cos:PutObjectAcl` | 设置对象ACL |
| `name/cos:GetObjectAcl` | 获取对象ACL |
| `name/cos:InitiateMultipartUpload` | 初始化分块上传 |
| `name/cos:UploadPart` | 上传分块 |
| `name/cos:CompleteMultipartUpload` | 完成分块上传 |
| `name/cos:AbortMultipartUpload` | 取消分块上传 |
| `name/cos:ListMultipartUploads` | 列出分块上传 |
| `name/cos:ListParts` | 列出分块 |

## 前端集成示例

### JavaScript调用示例

```javascript
// 获取临时密钥
async function getCOSCredentials() {
    const response = await fetch('/api/cos/temp-credentials', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + userToken  // 可选：用户认证
        },
        body: JSON.stringify({
            bucket: "my-bucket-123456789",
            region: "ap-guangzhou",
            allow_prefix: ["images/", "documents/"],
            allow_actions: ["name/cos:PutObject", "name/cos:PostObject"],
            duration_seconds: 1800
        })
    });
    
    const result = await response.json();
    
    if (result.success) {
        return result.credentials;
    } else {
        throw new Error(result.message);
    }
}

// 使用临时密钥上传文件
async function uploadFile(file) {
    try {
        // 获取临时密钥
        const credentials = await getCOSCredentials();
        
        // 配置COS SDK
        const cos = new COS({
            SecretId: credentials.tmp_secret_id,
            SecretKey: credentials.tmp_secret_key,
            SecurityToken: credentials.session_token,
            StartTime: credentials.start_time,
            ExpiredTime: credentials.expired_time
        });
        
        // 上传文件
        const result = await cos.putObject({
            Bucket: 'my-bucket-123456789',
            Region: 'ap-guangzhou',
            Key: `images/${file.name}`,
            Body: file
        });
        
        console.log('上传成功:', result);
        return result;
        
    } catch (error) {
        console.error('上传失败:', error);
        throw error;
    }
}
```

### React组件示例

```jsx
import React, { useState } from 'react';
import COS from 'cos-js-sdk-v5';

const FileUploader = () => {
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    
    const handleFileUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;
        
        setUploading(true);
        setProgress(0);
        
        try {
            // 获取临时密钥
            const response = await fetch('/api/cos/temp-credentials', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    bucket: "my-bucket-123456789",
                    region: "ap-guangzhou",
                    allow_prefix: ["uploads/"],
                    allow_actions: ["name/cos:PutObject"]
                })
            });
            
            const { credentials } = await response.json();
            
            // 配置COS
            const cos = new COS({
                SecretId: credentials.tmp_secret_id,
                SecretKey: credentials.tmp_secret_key,
                SecurityToken: credentials.session_token
            });
            
            // 上传文件
            await cos.putObject({
                Bucket: 'my-bucket-123456789',
                Region: 'ap-guangzhou',
                Key: `uploads/${Date.now()}-${file.name}`,
                Body: file,
                onProgress: (progressData) => {
                    setProgress(Math.round(progressData.percent * 100));
                }
            });
            
            alert('上传成功!');
            
        } catch (error) {
            console.error('上传失败:', error);
            alert('上传失败: ' + error.message);
        } finally {
            setUploading(false);
            setProgress(0);
        }
    };
    
    return (
        <div>
            <input 
                type="file" 
                onChange={handleFileUpload} 
                disabled={uploading}
            />
            {uploading && (
                <div>
                    上传进度: {progress}%
                    <div style={{
                        width: '100%',
                        backgroundColor: '#f0f0f0',
                        borderRadius: '5px',
                        overflow: 'hidden'
                    }}>
                        <div style={{
                            width: `${progress}%`,
                            height: '20px',
                            backgroundColor: '#4CAF50',
                            transition: 'width 0.3s'
                        }}></div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FileUploader;
```

## 安全最佳实践

### 1. 权限最小化原则

```javascript
// ✅ 好的实践：具体的路径前缀
{
    "allow_prefix": ["user123/images/", "user123/documents/"],
    "allow_actions": ["name/cos:PutObject"]
}

// ❌ 危险的实践：使用全局通配符
{
    "allow_prefix": ["*"],  // 系统会拒绝此请求
    "allow_actions": ["name/cos:*"]
}
```

### 2. 用户隔离

```javascript
// 为每个用户生成独立的路径前缀
const userPrefix = `user-${userId}/uploads/`;

await fetch('/api/cos/temp-credentials', {
    // ...
    body: JSON.stringify({
        bucket: "my-bucket-123456789",
        region: "ap-guangzhou",
        allow_prefix: [userPrefix],  // 用户隔离
        allow_actions: ["name/cos:PutObject"]
    })
});
```

### 3. 有效期控制

```javascript
// 根据文件大小调整有效期
const fileSizeMB = file.size / (1024 * 1024);
const duration = Math.min(
    Math.max(600, fileSizeMB * 60),  // 最少10分钟，每MB增加1分钟
    3600  // 最多1小时
);

// 请求临时密钥时指定合适的有效期
{
    "duration_seconds": duration
}
```

## 错误处理

### 常见错误码

| HTTP状态码 | 错误说明 | 解决方案 |
|-----------|---------|----------|
| 400 | 参数验证失败 | 检查请求参数格式 |
| 503 | 服务不可用 | 检查环境变量配置 |
| 500 | 内部服务错误 | 查看服务日志 |

### 错误响应示例

```json
{
    "success": false,
    "message": "请求参数无效: 不允许使用全局通配符*，请指定具体的路径前缀",
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 监控和日志

### 1. 服务监控

```bash
# 检查服务状态
curl http://localhost:8001/api/cos/status

# 查看服务日志
tail -f logs/common_service.log | grep COS
```

### 2. 关键指标

- 临时密钥生成成功率
- 平均响应时间
- 错误请求分布
- 用户访问模式

## 部署说明

### 1. 启动Common服务

```bash
# 开发环境
python -m controllers.common.server

# 生产环境
uvicorn controllers.common.server:app --host 0.0.0.0 --port 8001
```

### 2. Docker部署

```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 设置环境变量
ENV TENCENT_SECRET_ID=""
ENV TENCENT_SECRET_KEY=""
ENV COS_TEMP_DURATION="1800"

EXPOSE 8001
CMD ["uvicorn", "controllers.common.server:app", "--host", "0.0.0.0", "--port", "8001"]
```

## 常见问题

### Q: 临时密钥生成失败怎么办？

A: 检查以下几点：
1. 环境变量 `TENCENT_SECRET_ID` 和 `TENCENT_SECRET_KEY` 是否正确配置
2. 网络是否能访问 `sts.tencentcloudapi.com`
3. 腾讯云API密钥是否具有STS权限
4. 参数格式是否正确

### Q: 前端如何处理临时密钥过期？

A: 实现密钥刷新机制：
```javascript
class COSCredentialManager {
    constructor() {
        this.credentials = null;
        this.refreshPromise = null;
    }
    
    async getValidCredentials() {
        // 检查是否需要刷新（提前5分钟刷新）
        if (!this.credentials || Date.now() / 1000 > this.credentials.expired_time - 300) {
            if (!this.refreshPromise) {
                this.refreshPromise = this.refreshCredentials();
            }
            await this.refreshPromise;
            this.refreshPromise = null;
        }
        
        return this.credentials;
    }
    
    async refreshCredentials() {
        const response = await fetch('/api/cos/temp-credentials', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                bucket: "my-bucket-123456789",
                region: "ap-guangzhou",
                allow_prefix: ["uploads/"],
                allow_actions: ["name/cos:PutObject"]
            })
        });
        
        const result = await response.json();
        this.credentials = result.credentials;
        
        return this.credentials;
    }
}
```

### Q: 如何限制用户的上传权限？

A: 通过路径前缀和JWT认证实现：
```javascript
// 后端：根据用户角色生成不同的前缀
const getUserAllowPrefix = (user) => {
    switch (user.role) {
        case 'admin':
            return ['admin/', 'public/'];
        case 'user':
            return [`users/${user.id}/`];
        default:
            return [`temp/${user.id}/`];
    }
};
```

## 版本历史

- **v1.0.0** (2024-01-01)
  - 初始版本发布
  - 支持基本的临时密钥生成
  - 集成JWT用户认证
  - 完整的参数验证和安全控制

## 参考资料

- [腾讯云COS STS官方文档](https://cloud.tencent.com/document/product/436/14048)
- [腾讯云STS Python SDK](https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/python)
- [COS JavaScript SDK文档](https://cloud.tencent.com/document/product/436/11459)
- [STS API参考](https://cloud.tencent.com/document/product/598/33164)

## 许可证

MIT License - 请参阅项目根目录的LICENSE文件。 
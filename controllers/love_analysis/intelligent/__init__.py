"""
智能分析模块

提供人设分析、RAG检索、综合分析和数据存储功能
基于LangGraph工作流编排和向量数据库技术
"""

# 导入主要组件
from .server import intelligent_server
# 其他组件采用懒加载，避免启动时的依赖问题
# from .workflows import workflow_manager
# from .handlers import IntelligentMessageHandlers
# from .rag.system import persona_rag
# from .storage.managers import text_storage, emotion_storage, persona_storage

# 模块信息
__version__ = "1.0.0"
__author__ = "Relationship Analysis System"

# 对外暴露的主要接口
__all__ = [
    # 服务器组件
    'intelligent_server',
    
    # 其他组件（懒加载）
    # 'workflow_manager',
    # 'IntelligentMessageHandlers',
    # 'persona_rag',
    # 'text_storage',
    # 'emotion_storage', 
    # 'persona_storage'
]

def get_intelligent_module_info():
    """获取智能分析模块信息"""
    return {
        "name": "intelligent_analysis_module",
        "version": __version__,
        "description": "基于LangGraph和RAG的智能关系分析系统",
        "components": {
            "server": "WebSocket服务器实现",
            "workflows": "LangGraph工作流编排（懒加载）",
            "handlers": "智能消息处理器（懒加载）",
            "rag": "RAG知识库系统（懒加载）",
            "storage": "数据存储管理器（懒加载）",
            "server_start": "独立服务器启动脚本"
        },
        "features": [
            "人设洞察分析",
            "RAG知识检索",
            "综合分析报告",
            "数据持久化存储",
            "LangGraph工作流",
            "向量数据库集成"
        ],
        "technology_stack": {
            "workflow_engine": "LangGraph",
            "vector_database": "Qdrant",
            "embedding_model": "BGE-large-zh-v1.5",
            "relational_database": "PostgreSQL",
            "websocket": "asyncio + websockets"
        }
    } 
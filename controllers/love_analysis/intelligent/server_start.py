#!/usr/bin/env python3
"""
智能分析服务器独立启动脚本

启动智能分析WebSocket服务器，支持人设分析、RAG检索、综合分析功能
适用于pm2进程管理
"""
import asyncio
import logging
import signal
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.insert(0, project_root)

# 使用绝对导入，解决PM2启动时的包结构识别问题
from controllers.love_analysis.intelligent.server import intelligent_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def signal_handler(sig, frame):
    """处理中断信号"""
    logger.info("接收到停止信号，正在关闭智能分析服务器...")
    sys.exit(0)

async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        logger.info("=== 智能分析服务器启动 ===")
        logger.info("端口: 8766")
        logger.info("功能: 人设分析、RAG检索、综合分析、数据存储")
        logger.info("管理: 通过pm2进程管理")
        logger.info("架构: 模块化微服务")
        logger.info("路径: %s", project_root)
        logger.info("=====================================")
        
        # 启动智能分析服务器
        await intelligent_server.start_server(host="0.0.0.0", port=8766)
        
    except KeyboardInterrupt:
        logger.info("收到键盘中断，停止服务器")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 
"""
智能关系分析WebSocket服务器

独立于情绪监测的WebSocket服务器，处理智能分析相关请求
包括：存储流程、人设分析、RAG检索、综合分析报告等功能
使用LangChain和LangGraph进行工作流编排
"""
import asyncio
import websockets
import json
import logging
from typing import Dict, Any, Set
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntelligentAnalysisServer:
    """智能分析WebSocket服务器类"""
    
    def __init__(self):
        """初始化服务器"""
        self.connections: Set[websockets.WebSocketServerProtocol] = set()
        self.server = None
        self._handlers = None
        
    async def register_client(self, websocket: websockets.WebSocketServerProtocol):
        """注册新客户端连接"""
        self.connections.add(websocket)
        logger.info(f"智能分析客户端已连接，当前连接数: {len(self.connections)}")
        
    async def unregister_client(self, websocket: websockets.WebSocketServerProtocol):
        """注销客户端连接"""
        self.connections.discard(websocket)
        logger.info(f"智能分析客户端已断开，当前连接数: {len(self.connections)}")
        
    async def broadcast_message(self, message: Dict[str, Any]):
        """向所有连接的客户端广播消息"""
        if self.connections:
            message_str = json.dumps(message, ensure_ascii=False)
            await asyncio.gather(
                *[websocket.send(message_str) for websocket in self.connections],
                return_exceptions=True
            )
            
    async def handle_client(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        await self.register_client(websocket)
        
        try:
            # 发送连接确认
            welcome_message = {
                "type": "connection_established",
                "message": "智能分析服务器连接成功",
                "timestamp": datetime.utcnow().isoformat(),
                "server_info": {
                    "name": "intelligent_analysis_server",
                    "version": "1.0.0",
                    "port": 8766,
                    "features": [
                        "relationship_text_storage",
                        "emotion_detection_storage", 
                        "persona_analysis",
                        "comprehensive_analysis",
                        "emotion_diary",
                        "rag_system"
                    ]
                }
            }
            await websocket.send(json.dumps(welcome_message, ensure_ascii=False))
            
            # 处理消息循环
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.process_message(websocket, data)
                except json.JSONDecodeError:
                    error_response = {
                        "type": "error",
                        "message": "无效的JSON格式",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send(json.dumps(error_response, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"处理消息时出错: {str(e)}")
                    error_response = {
                        "type": "error", 
                        "message": f"处理消息失败: {str(e)}",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send(json.dumps(error_response, ensure_ascii=False))
                    
        except websockets.exceptions.ConnectionClosedError:
            logger.info("智能分析客户端连接正常关闭")
        except Exception as e:
            logger.error(f"智能分析客户端连接异常: {str(e)}")
        finally:
            await self.unregister_client(websocket)
            
    async def process_message(self, websocket: websockets.WebSocketServerProtocol, data: Dict[str, Any]):
        """处理接收到的消息"""
        message_type = data.get("type")
        
        # 懒加载消息处理器
        if self._handlers is None:
            from .handlers import IntelligentMessageHandlers
            self._handlers = IntelligentMessageHandlers()
            
        # 路由消息到对应的处理器
        handler_methods = {
            "ping": self._handlers.handle_ping,
            "store_relationship_text": self._handlers.handle_store_relationship_text,
            "confirm_persona_insight": self._handlers.handle_confirm_persona_insight,
            "request_comprehensive_analysis": self._handlers.handle_comprehensive_analysis_request,
            "get_relationship_history": self._handlers.handle_get_relationship_history,
            "get_emotion_diary": self._handlers.handle_get_emotion_diary,
            "search_relationship_records": self._handlers.handle_search_relationship_records,
            "analyze_emotion_trends": self._handlers.handle_analyze_emotion_trends,
            "get_persona_evolution": self._handlers.handle_get_persona_evolution,
            "update_text_content": self._handlers.handle_update_text_content
        }
        
        if message_type in handler_methods:
            await handler_methods[message_type](websocket, data)
        else:
            # 未知消息类型
            error_response = {
                "type": "error",
                "message": f"未知的消息类型: {message_type}",
                "supported_types": list(handler_methods.keys()),
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
            
    async def start_server(self, host: str = "localhost", port: int = 8766):
        """启动WebSocket服务器"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                host,
                port,
                ping_interval=30,
                ping_timeout=10
            )
            logger.info(f"智能分析WebSocket服务器已启动 - {host}:{port}")
            
            # 等待服务器关闭
            await self.server.wait_closed()
            
        except Exception as e:
            logger.error(f"启动智能分析服务器失败: {str(e)}")
            raise
            
    async def stop_server(self):
        """停止WebSocket服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("智能分析WebSocket服务器已停止")

# 全局服务器实例
intelligent_server = IntelligentAnalysisServer()

async def start_intelligent_analysis_server():
    """启动智能分析服务器的入口函数"""
    await intelligent_server.start_server()

if __name__ == "__main__":
    # 直接运行时启动服务器
    asyncio.run(start_intelligent_analysis_server()) 
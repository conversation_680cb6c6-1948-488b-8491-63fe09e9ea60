"""
智能分析消息处理器

处理智能分析WebSocket服务器的各种消息类型
集成LangGraph工作流和存储管理器
"""
import logging
import json
import asyncio
import websockets
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

# 工作流和存储管理器
from .intelligent_workflows import workflow_manager
from .storage_managers import text_storage, emotion_storage, persona_storage
from .rag_system import persona_rag

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntelligentMessageHandlers:
    """智能分析消息处理器类"""
    
    def __init__(self):
        """初始化消息处理器"""
        self._initialized = False
        
    async def _ensure_initialized(self):
        """确保组件已初始化"""
        if not self._initialized:
            try:
                await workflow_manager.initialize()
                self._initialized = True
                logger.info("智能消息处理器初始化完成")
            except Exception as e:
                logger.error(f"智能消息处理器初始化失败: {str(e)}")
                raise
                
    async def handle_ping(self, websocket: websockets.WebSocketServerProtocol, data: Dict[str, Any]):
        """处理心跳消息"""
        try:
            response = {
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat(),
                "server_status": "active"
            }
            await websocket.send(json.dumps(response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理ping消息失败: {str(e)}")
            
    async def handle_store_relationship_text(self, 
                                           websocket: websockets.WebSocketServerProtocol, 
                                           data: Dict[str, Any]):
        """
        处理关系文本存储请求
        
        执行完整的存储工作流：文本存储 -> 情绪分析 -> 人设分析 -> 重写检查
        """
        try:
            await self._ensure_initialized()
            
            # 提取请求参数
            user_id = data.get("user_id")
            text = data.get("text")
            
            if not user_id or not text:
                error_response = {
                    "type": "error",
                    "message": "缺少必要参数: user_id 和 text",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 发送处理开始通知
            start_response = {
                "type": "processing_started",
                "message": "开始处理关系文本",
                "user_id": user_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(start_response, ensure_ascii=False))
            
            # 运行存储工作流
            logger.info(f"开始为用户 {user_id} 执行存储工作流")
            workflow_result = await workflow_manager.run_storage_workflow(user_id, text)
            
            # 处理工作流结果并分步发送通知
            await self._process_workflow_results(websocket, workflow_result, user_id)
            
        except Exception as e:
            logger.error(f"处理存储请求失败: {str(e)}")
            error_response = {
                "type": "error",
                "message": f"存储处理失败: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
            
    async def _process_workflow_results(self, 
                                      websocket: websockets.WebSocketServerProtocol,
                                      workflow_result: Dict[str, Any],
                                      user_id: str):
        """处理工作流结果并分步发送通知"""
        try:
            # 1. 发送情绪检测结果
            if workflow_result.get("emotion_data"):
                emotion_response = {
                    "type": "emotion_detected",
                    "emotion_data": workflow_result["emotion_data"],
                    "emotion_id": workflow_result.get("emotion_id"),
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(emotion_response, ensure_ascii=False))
                
            # 3. 发送人设洞察（如果有，等待用户确认）
            if workflow_result.get("persona_insight") and workflow_result.get("persona_insight_id"):
                # 解析人设洞察
                parts = workflow_result["persona_insight"].split("—", 1)
                if len(parts) == 2:
                    persona_response = {
                        "type": "persona_insight_found",
                        "insight": {
                            "id": workflow_result["persona_insight_id"],
                            "persona_keyword": parts[0].strip(),
                            "persona_definition": parts[1].strip(),
                            "raw_insight": workflow_result["persona_insight"]
                        },
                        "requires_confirmation": True,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send(json.dumps(persona_response, ensure_ascii=False))
                    
            # 4. 发送重写建议（如果有）
            if workflow_result.get("rewritten_text"):
                rewrite_response = {
                    "type": "rewrite_suggestion",
                    "original_text": workflow_result.get("original_text", ""),
                    "rewritten_text": workflow_result["rewritten_text"],
                    "reason": "negative_emotion_optimization",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(rewrite_response, ensure_ascii=False))
                
            # 5. 发送存储完成通知
            completion_response = {
                "type": "storage_complete",
                "text_id": workflow_result.get("text_id"),
                "completed_steps": workflow_result.get("completed_steps", []),
                "errors": workflow_result.get("errors", []),
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(completion_response, ensure_ascii=False))
            
            logger.info(f"用户 {user_id} 的存储工作流处理完成")
            
        except Exception as e:
            logger.error(f"处理工作流结果失败: {str(e)}")
            
    async def handle_confirm_persona_insight(self, 
                                           websocket: websockets.WebSocketServerProtocol,
                                           data: Dict[str, Any]):
        """
        处理人设洞察确认请求
        """
        try:
            await self._ensure_initialized()
            
            insight_id = data.get("insight_id")
            confirmed = data.get("confirmed", False)
            user_id = data.get("user_id")
            
            if not insight_id:
                error_response = {
                    "type": "error",
                    "message": "缺少insight_id参数",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 更新确认状态
            success = await persona_storage.confirm_persona_insight(insight_id, confirmed)
            
            if success and confirmed:
                # 如果确认，存储到RAG系统
                try:
                    # 获取洞察详情
                    insights = await persona_storage.get_confirmed_insights(user_id)
                    confirmed_insight = next((i for i in insights if i["id"] == insight_id), None)
                    
                    if confirmed_insight:
                        # 构建RAG存储数据
                        rag_data = {
                            "persona_keyword": confirmed_insight["persona_keyword"],
                            "persona_definition": confirmed_insight["persona_definition"],
                            "insight_id": insight_id,
                            "confidence_score": confirmed_insight.get("confidence_score", 0.8),
                            "confirmed_at": datetime.utcnow().isoformat(),
                            "text_id": confirmed_insight.get("text_id")
                        }
                        
                        # 存储到RAG系统
                        rag_success = await persona_rag.store_persona_insight(rag_data, user_id)
                        
                        if rag_success:
                            # 检查是否有足够的洞察生成综合描述
                            await self._check_generate_comprehensive_persona(user_id, websocket)
                            
                except Exception as e:
                    logger.error(f"RAG存储失败: {str(e)}")
                    
            # 发送确认结果
            confirm_response = {
                "type": "persona_insight_confirmed",
                "insight_id": insight_id,
                "confirmed": confirmed,
                "success": success,
                "message": "人设洞察已确认并存储到知识库" if confirmed and success else "人设洞察已处理",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(confirm_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理人设确认失败: {str(e)}")
            error_response = {
                "type": "error",
                "message": f"处理确认失败: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
            
    async def _check_generate_comprehensive_persona(self, user_id: str, websocket: websockets.WebSocketServerProtocol):
        """检查是否可以生成综合人设描述"""
        try:
            confirmed_insights = await persona_storage.get_confirmed_insights(user_id)
            
            # 如果有3个或以上确认的洞察，生成综合描述
            if len(confirmed_insights) >= 3:
                from tools.generate_comprehensive_persona_description import generate_comprehensive_persona_description
                
                comprehensive_description = await generate_comprehensive_persona_description(confirmed_insights)
                
                if comprehensive_description.get("success"):
                    # 发送综合人设生成通知
                    persona_response = {
                        "type": "comprehensive_persona_generated",
                        "persona_description": comprehensive_description,
                        "based_on_insights": len(confirmed_insights),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send(json.dumps(persona_response, ensure_ascii=False))
                    
        except Exception as e:
            logger.error(f"生成综合人设失败: {str(e)}")
            
    async def handle_comprehensive_analysis_request(self, 
                                                  websocket: websockets.WebSocketServerProtocol,
                                                  data: Dict[str, Any]):
        """
        处理综合分析请求
        """
        try:
            await self._ensure_initialized()
            
            user_id = data.get("user_id")
            analysis_type = data.get("analysis_type", "comprehensive")
            include_emotion_trends = data.get("include_emotion_trends", True)
            include_persona_analysis = data.get("include_persona_analysis", True)
            time_period = data.get("time_period")
            
            if not user_id:
                error_response = {
                    "type": "error",
                    "message": "缺少user_id参数",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 发送分析开始通知
            start_response = {
                "type": "analysis_started",
                "message": "开始生成综合分析报告",
                "analysis_type": analysis_type,
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(start_response, ensure_ascii=False))
            
            # 运行分析工作流
            analysis_result = await workflow_manager.run_analysis_workflow(
                user_id=user_id,
                analysis_type=analysis_type,
                time_period=time_period
            )
            
            # 发送分析结果
            analysis_response = {
                "type": "comprehensive_analysis",
                "analysis_result": analysis_result,
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(analysis_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理综合分析请求失败: {str(e)}")
            error_response = {
                "type": "error",
                "message": f"分析处理失败: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
            
    async def handle_get_emotion_diary(self, 
                                     websocket: websockets.WebSocketServerProtocol,
                                     data: Dict[str, Any]):
        """
        处理情绪日记请求
        """
        try:
            await self._ensure_initialized()
            
            user_id = data.get("user_id")
            date_range = data.get("date_range", [])
            chart_type = data.get("chart_type", "trend")
            
            if not user_id:
                error_response = {
                    "type": "error",
                    "message": "缺少user_id参数",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 解析日期范围
            start_date = None
            end_date = None
            if len(date_range) >= 2:
                try:
                    start_date = datetime.fromisoformat(date_range[0])
                    end_date = datetime.fromisoformat(date_range[1])
                except ValueError:
                    logger.warning(f"无效的日期格式: {date_range}")
                    
            # 获取情绪数据
            emotions = await emotion_storage.get_user_emotions(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date,
                limit=200
            )
            
            # 生成情绪分析
            emotion_analysis = self._generate_emotion_analysis(emotions, chart_type)
            
            # 发送情绪日记结果
            diary_response = {
                "type": "emotion_diary",
                "chart_data": emotion_analysis["chart_data"],
                "insights": emotion_analysis["insights"],
                "chart_type": chart_type,
                "total_records": len(emotions),
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(diary_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理情绪日记请求失败: {str(e)}")
            error_response = {
                "type": "error",
                "message": f"情绪日记生成失败: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
            
    def _generate_emotion_analysis(self, emotions: List[Dict[str, Any]], chart_type: str) -> Dict[str, Any]:
        """生成情绪分析数据"""
        if not emotions:
            return {
                "chart_data": [],
                "insights": {"message": "暂无情绪数据"}
            }
            
        # 按时间排序
        sorted_emotions = sorted(emotions, key=lambda x: x["created_at"])
        
        chart_data = []
        insights = {}
        
        if chart_type == "trend":
            # 生成趋势图数据
            for emotion in sorted_emotions:
                chart_data.append({
                    "date": emotion["created_at"][:10],  # 只取日期部分
                    "emotions": emotion["emotions"],
                    "behavior_tag": emotion.get("behavior_tag", "")
                })
                
            # 生成洞察
            insights = self._calculate_emotion_insights(sorted_emotions)
            
        elif chart_type == "heatmap":
            # 生成热力图数据（按小时统计）
            hour_emotions = {}
            for emotion in sorted_emotions:
                hour = datetime.fromisoformat(emotion["created_at"]).hour
                if hour not in hour_emotions:
                    hour_emotions[hour] = []
                hour_emotions[hour].append(emotion["emotions"])
                
            chart_data = []
            for hour in range(24):
                if hour in hour_emotions:
                    avg_emotions = self._calculate_average_emotions(hour_emotions[hour])
                    chart_data.append({
                        "hour": hour,
                        "emotions": avg_emotions
                    })
                    
        elif chart_type == "distribution":
            # 生成分布图数据
            emotion_counts = {
                "anger": [], "sadness": [], "fear": [],
                "joy": [], "disgust": [], "neutral": []
            }
            
            for emotion in sorted_emotions:
                for emotion_type, emotion_data in emotion["emotions"].items():
                    if emotion_type in emotion_counts:
                        emotion_counts[emotion_type].append(emotion_data["score"])
                        
            chart_data = emotion_counts
            
        return {
            "chart_data": chart_data,
            "insights": insights
        }
        
    def _calculate_emotion_insights(self, emotions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算情绪洞察"""
        if not emotions:
            return {}
            
        # 计算平均情绪
        emotion_totals = {
            "anger": 0, "sadness": 0, "fear": 0,
            "joy": 0, "disgust": 0, "neutral": 0
        }
        
        for emotion in emotions:
            for emotion_type in emotion_totals:
                emotion_totals[emotion_type] += emotion["emotions"][emotion_type]["score"]
                
        emotion_averages = {
            emotion_type: total / len(emotions)
            for emotion_type, total in emotion_totals.items()
        }
        
        # 找出主导情绪
        dominant_emotion = max(emotion_averages, key=emotion_averages.get)
        
        # 计算情绪稳定性
        stability_score = self._calculate_emotion_stability(emotions)
        
        return {
            "emotion_averages": emotion_averages,
            "dominant_emotion": dominant_emotion,
            "stability_score": stability_score,
            "total_records": len(emotions),
            "analysis_summary": f"主导情绪为{dominant_emotion}，情绪稳定性为{stability_score:.2f}"
        }
        
    def _calculate_emotion_stability(self, emotions: List[Dict[str, Any]]) -> float:
        """计算情绪稳定性分数"""
        if len(emotions) < 2:
            return 1.0
            
        # 计算情绪波动
        variances = []
        emotion_types = ["anger", "sadness", "fear", "joy", "disgust", "neutral"]
        
        for emotion_type in emotion_types:
            scores = [e["emotions"][emotion_type]["score"] for e in emotions]
            if len(scores) > 1:
                mean = sum(scores) / len(scores)
                variance = sum((x - mean) ** 2 for x in scores) / len(scores)
                variances.append(variance)
                
        if variances:
            avg_variance = sum(variances) / len(variances)
            # 将方差转换为稳定性分数（0-1，1为最稳定）
            stability = max(0, 1 - avg_variance * 10)
            return stability
        else:
            return 1.0
            
    def _calculate_average_emotions(self, emotion_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算平均情绪"""
        if not emotion_list:
            return {}
            
        emotion_totals = {
            "anger": 0, "sadness": 0, "fear": 0,
            "joy": 0, "disgust": 0, "neutral": 0
        }
        
        for emotions in emotion_list:
            for emotion_type in emotion_totals:
                emotion_totals[emotion_type] += emotions[emotion_type]["score"]
                
        return {
            emotion_type: {"score": total / len(emotion_list)}
            for emotion_type, total in emotion_totals.items()
        }
        
    # ========== 其他处理方法 ==========
    
    async def handle_get_relationship_history(self, 
                                            websocket: websockets.WebSocketServerProtocol,
                                            data: Dict[str, Any]):
        """处理关系历史请求"""
        try:
            await self._ensure_initialized()
            
            user_id = data.get("user_id")
            limit = data.get("limit", 20)
            offset = data.get("offset", 0)
            
            if not user_id:
                error_response = {
                    "type": "error",
                    "message": "缺少user_id参数",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 获取文本历史
            texts = await text_storage.get_user_texts(user_id, limit, offset)
            
            history_response = {
                "type": "relationship_history",
                "texts": texts,
                "total_count": len(texts),
                "limit": limit,
                "offset": offset,
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(history_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理历史请求失败: {str(e)}")
            
    async def handle_search_relationship_records(self, 
                                               websocket: websockets.WebSocketServerProtocol,
                                               data: Dict[str, Any]):
        """处理关系记录搜索请求"""
        # 这里可以实现基于关键词的搜索功能
        # 暂时返回简单响应
        search_response = {
            "type": "search_results",
            "message": "搜索功能正在开发中",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send(json.dumps(search_response, ensure_ascii=False))
        
    async def handle_analyze_emotion_trends(self, 
                                          websocket: websockets.WebSocketServerProtocol,
                                          data: Dict[str, Any]):
        """处理情绪趋势分析请求"""
        # 可以复用情绪日记的逻辑
        await self.handle_get_emotion_diary(websocket, data)
        
    async def handle_get_persona_evolution(self, 
                                         websocket: websockets.WebSocketServerProtocol,
                                         data: Dict[str, Any]):
        """处理人设演变请求"""
        try:
            await self._ensure_initialized()
            
            user_id = data.get("user_id")
            
            if not user_id:
                error_response = {
                    "type": "error",
                    "message": "缺少user_id参数",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 获取所有已确认的洞察
            insights = await persona_storage.get_confirmed_insights(user_id)
            
            evolution_response = {
                "type": "persona_evolution",
                "insights": insights,
                "evolution_summary": f"共发现 {len(insights)} 个人设特质",
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(evolution_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理人设演变请求失败: {str(e)}")
            
    async def handle_update_text_content(self, 
                                       websocket: websockets.WebSocketServerProtocol,
                                       data: Dict[str, Any]):
        """处理文本内容更新请求"""
        try:
            await self._ensure_initialized()
            
            text_id = data.get("text_id")
            rewritten_text = data.get("rewritten_text")
            
            if not text_id or not rewritten_text:
                error_response = {
                    "type": "error",
                    "message": "缺少必要参数: text_id 和 rewritten_text",
                    "timestamp": datetime.utcnow().isoformat()
                }
                await websocket.send(json.dumps(error_response, ensure_ascii=False))
                return
                
            # 更新文本内容
            success = await text_storage.update_text(text_id, rewritten_text=rewritten_text)
            
            update_response = {
                "type": "text_updated",
                "text_id": text_id,
                "success": success,
                "timestamp": datetime.utcnow().isoformat()
            }
            await websocket.send(json.dumps(update_response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"处理文本更新请求失败: {str(e)}") 
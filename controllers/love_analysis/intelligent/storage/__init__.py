"""
存储管理子模块

基于PostgreSQL的关系数据存储管理
包括文本存储、情绪数据存储和人设洞察存储
"""

from .managers import (
    DatabaseConfig,
    TextStorageManager,
    EmotionStorageManager,
    PersonaStorageManager,
    text_storage,
    emotion_storage,
    persona_storage
)

__version__ = "1.0.0"

__all__ = [
    'DatabaseConfig',
    'TextStorageManager',
    'EmotionStorageManager',
    'PersonaStorageManager',
    'text_storage',
    'emotion_storage',
    'persona_storage'
]

def get_storage_info():
    """获取存储子模块信息"""
    return {
        "name": "storage_management_system",
        "version": __version__,
        "description": "PostgreSQL关系数据库存储管理系统",
        "features": [
            "文本数据存储",
            "情绪分析结果存储",
            "人设洞察数据存储",
            "连接池管理",
            "异步数据操作"
        ]
    } 
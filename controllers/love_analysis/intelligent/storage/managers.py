"""
存储管理器模块

包含文本存储、情绪存储和人设存储的统一管理
基于PostgreSQL数据库实现
"""
import logging
import json
import asyncpg
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import uuid
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        """从环境变量读取数据库配置"""
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = int(os.getenv('DB_PORT', 5432))
        self.database = os.getenv('DB_NAME', 'relationship_analysis')
        self.user = os.getenv('DB_USER', 'postgres')
        self.password = os.getenv('DB_PASSWORD', 'password')
        
    def get_connection_string(self) -> str:
        """获取数据库连接字符串"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

class TextStorageManager:
    """文本存储管理器"""
    
    def __init__(self, db_config: DatabaseConfig):
        """初始化文本存储管理器"""
        self.db_config = db_config
        self._pool = None
        
    async def initialize(self):
        """初始化数据库连接池和表结构"""
        try:
            # 创建连接池
            self._pool = await asyncpg.create_pool(
                self.db_config.get_connection_string(),
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # 创建表结构
            await self._create_tables()
            logger.info("文本存储管理器初始化完成")
            
        except Exception as e:
            logger.error(f"文本存储管理器初始化失败: {str(e)}")
            raise
            
    async def _create_tables(self):
        """创建关系文本表"""
        async with self._pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS relationship_texts (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL,
                    original_text TEXT NOT NULL,
                    rewritten_text TEXT,
                    tags JSONB DEFAULT '[]'::jsonb,
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_relationship_texts_user_id 
                ON relationship_texts(user_id);
                
                CREATE INDEX IF NOT EXISTS idx_relationship_texts_created_at 
                ON relationship_texts(created_at DESC);
            """)
            
    async def store_text(self, 
                        user_id: str, 
                        original_text: str,
                        rewritten_text: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        存储关系文本
        
        Args:
            user_id: 用户ID
            original_text: 原始文本
            rewritten_text: 重写后的文本
            tags: 标签列表
            metadata: 元数据
            
        Returns:
            文本记录ID
        """
        try:
            text_id = str(uuid.uuid4())
            
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO relationship_texts 
                    (id, user_id, original_text, rewritten_text, tags, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, text_id, user_id, original_text, rewritten_text,
                json.dumps(tags or []), json.dumps(metadata or {}))
                
            logger.info(f"成功存储文本，ID: {text_id}")
            return text_id
            
        except Exception as e:
            logger.error(f"存储文本失败: {str(e)}")
            raise
            
    async def update_text(self, 
                         text_id: str,
                         rewritten_text: Optional[str] = None,
                         tags: Optional[List[str]] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        更新文本记录
        
        Args:
            text_id: 文本ID
            rewritten_text: 重写后的文本
            tags: 标签列表
            metadata: 元数据
            
        Returns:
            更新是否成功
        """
        try:
            updates = []
            params = []
            param_count = 1
            
            if rewritten_text is not None:
                updates.append(f"rewritten_text = ${param_count}")
                params.append(rewritten_text)
                param_count += 1
                
            if tags is not None:
                updates.append(f"tags = ${param_count}")
                params.append(json.dumps(tags))
                param_count += 1
                
            if metadata is not None:
                updates.append(f"metadata = ${param_count}")
                params.append(json.dumps(metadata))
                param_count += 1
                
            if not updates:
                return False
                
            updates.append(f"updated_at = ${param_count}")
            params.append(datetime.utcnow())
            param_count += 1
            
            params.append(text_id)
            
            query = f"""
                UPDATE relationship_texts 
                SET {', '.join(updates)}
                WHERE id = ${param_count}
            """
            
            async with self._pool.acquire() as conn:
                result = await conn.execute(query, *params)
                
            return result == "UPDATE 1"
            
        except Exception as e:
            logger.error(f"更新文本失败: {str(e)}")
            return False
            
    async def get_user_texts(self, 
                           user_id: str,
                           limit: int = 20,
                           offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取用户的文本记录
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            文本记录列表
        """
        try:
            async with self._pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT id, user_id, original_text, rewritten_text, 
                           tags, metadata, created_at, updated_at
                    FROM relationship_texts
                    WHERE user_id = $1
                    ORDER BY created_at DESC
                    LIMIT $2 OFFSET $3
                """, user_id, limit, offset)
                
                texts = []
                for row in rows:
                    text_record = {
                        "id": str(row["id"]),
                        "user_id": row["user_id"],
                        "original_text": row["original_text"],
                        "rewritten_text": row["rewritten_text"],
                        "tags": json.loads(row["tags"]) if row["tags"] else [],
                        "metadata": json.loads(row["metadata"]) if row["metadata"] else {},
                        "created_at": row["created_at"].isoformat(),
                        "updated_at": row["updated_at"].isoformat()
                    }
                    texts.append(text_record)
                    
                return texts
                
        except Exception as e:
            logger.error(f"获取用户文本失败: {str(e)}")
            return []

class EmotionStorageManager:
    """情绪数据存储管理器"""
    
    def __init__(self, db_config: DatabaseConfig):
        """初始化情绪存储管理器"""
        self.db_config = db_config
        self._pool = None
        
    async def initialize(self):
        """初始化数据库连接池和表结构"""
        try:
            # 创建连接池
            self._pool = await asyncpg.create_pool(
                self.db_config.get_connection_string(),
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # 创建表结构
            await self._create_tables()
            logger.info("情绪存储管理器初始化完成")
            
        except Exception as e:
            logger.error(f"情绪存储管理器初始化失败: {str(e)}")
            raise
            
    async def _create_tables(self):
        """创建情绪记录表"""
        async with self._pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS emotion_records (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL,
                    text_id UUID,
                    anger_score FLOAT NOT NULL DEFAULT 0.0,
                    sadness_score FLOAT NOT NULL DEFAULT 0.0,
                    fear_score FLOAT NOT NULL DEFAULT 0.0,
                    joy_score FLOAT NOT NULL DEFAULT 0.0,
                    disgust_score FLOAT NOT NULL DEFAULT 0.0,
                    neutral_score FLOAT NOT NULL DEFAULT 0.0,
                    behavior_tag VARCHAR(255),
                    behavior_description TEXT,
                    confidence_intervals JSONB,
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (text_id) REFERENCES relationship_texts(id) ON DELETE CASCADE
                );
                
                CREATE INDEX IF NOT EXISTS idx_emotion_records_user_id 
                ON emotion_records(user_id);
                
                CREATE INDEX IF NOT EXISTS idx_emotion_records_created_at 
                ON emotion_records(created_at DESC);
                
                CREATE INDEX IF NOT EXISTS idx_emotion_records_text_id 
                ON emotion_records(text_id);
            """)
            
    async def store_emotion(self, 
                          user_id: str,
                          text_id: str,
                          emotion_data: Dict[str, Any]) -> str:
        """
        存储情绪分析结果
        
        Args:
            user_id: 用户ID
            text_id: 关联的文本ID
            emotion_data: 情绪分析数据
            
        Returns:
            情绪记录ID
        """
        try:
            emotion_id = str(uuid.uuid4())
            
            # 提取情绪分数
            emotions = emotion_data.get("emotions", {})
            anger_score = emotions.get("anger", {}).get("score", 0.0)
            sadness_score = emotions.get("sadness", {}).get("score", 0.0)
            fear_score = emotions.get("fear", {}).get("score", 0.0)
            joy_score = emotions.get("joy", {}).get("score", 0.0)
            disgust_score = emotions.get("disgust", {}).get("score", 0.0)
            neutral_score = emotions.get("neutral", {}).get("score", 0.0)
            
            # 提取行为标签和描述
            behavior_tag = emotion_data.get("behavior_tag", "")
            behavior_description = emotion_data.get("behavior_description", "")
            
            # 提取置信区间
            confidence_intervals = {}
            for emotion_type, emotion_info in emotions.items():
                if isinstance(emotion_info, dict) and "confidence_interval" in emotion_info:
                    confidence_intervals[emotion_type] = emotion_info["confidence_interval"]
                    
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO emotion_records 
                    (id, user_id, text_id, anger_score, sadness_score, fear_score,
                     joy_score, disgust_score, neutral_score, behavior_tag, 
                     behavior_description, confidence_intervals, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """, emotion_id, user_id, text_id, anger_score, sadness_score, 
                fear_score, joy_score, disgust_score, neutral_score,
                behavior_tag, behavior_description,
                json.dumps(confidence_intervals), json.dumps({}))
                
            logger.info(f"成功存储情绪数据，ID: {emotion_id}")
            return emotion_id
            
        except Exception as e:
            logger.error(f"存储情绪数据失败: {str(e)}")
            raise
            
    async def get_user_emotions(self, 
                              user_id: str,
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None,
                              limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取用户的情绪记录
        
        Args:
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回数量限制
            
        Returns:
            情绪记录列表
        """
        try:
            query = """
                SELECT id, user_id, text_id, anger_score, sadness_score, fear_score,
                       joy_score, disgust_score, neutral_score, behavior_tag,
                       behavior_description, confidence_intervals, metadata, created_at
                FROM emotion_records
                WHERE user_id = $1
            """
            params = [user_id]
            param_count = 2
            
            if start_date:
                query += f" AND created_at >= ${param_count}"
                params.append(start_date)
                param_count += 1
                
            if end_date:
                query += f" AND created_at <= ${param_count}"
                params.append(end_date)
                param_count += 1
                
            query += f" ORDER BY created_at DESC LIMIT ${param_count}"
            params.append(limit)
            
            async with self._pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                
                emotions = []
                for row in rows:
                    emotion_record = {
                        "id": str(row["id"]),
                        "user_id": row["user_id"],
                        "text_id": str(row["text_id"]) if row["text_id"] else None,
                        "emotions": {
                            "anger": {"score": row["anger_score"]},
                            "sadness": {"score": row["sadness_score"]},
                            "fear": {"score": row["fear_score"]},
                            "joy": {"score": row["joy_score"]},
                            "disgust": {"score": row["disgust_score"]},
                            "neutral": {"score": row["neutral_score"]}
                        },
                        "behavior_tag": row["behavior_tag"],
                        "behavior_description": row["behavior_description"],
                        "confidence_intervals": json.loads(row["confidence_intervals"]) if row["confidence_intervals"] else {},
                        "metadata": json.loads(row["metadata"]) if row["metadata"] else {},
                        "created_at": row["created_at"].isoformat()
                    }
                    emotions.append(emotion_record)
                    
                return emotions
                
        except Exception as e:
            logger.error(f"获取用户情绪失败: {str(e)}")
            return []

class PersonaStorageManager:
    """人设存储管理器"""
    
    def __init__(self, db_config: DatabaseConfig):
        """初始化人设存储管理器"""
        self.db_config = db_config
        self._pool = None
        
    async def initialize(self):
        """初始化数据库连接池和表结构"""
        try:
            # 创建连接池
            self._pool = await asyncpg.create_pool(
                self.db_config.get_connection_string(),
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # 创建表结构
            await self._create_tables()
            logger.info("人设存储管理器初始化完成")
            
        except Exception as e:
            logger.error(f"人设存储管理器初始化失败: {str(e)}")
            raise
            
    async def _create_tables(self):
        """创建人设相关表"""
        async with self._pool.acquire() as conn:
            await conn.execute("""
                -- 人设洞察表
                CREATE TABLE IF NOT EXISTS persona_insights (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL,
                    text_id UUID,
                    persona_keyword VARCHAR(255) NOT NULL,
                    persona_definition TEXT NOT NULL,
                    confirmed BOOLEAN DEFAULT FALSE,
                    confidence_score FLOAT DEFAULT 0.0,
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    confirmed_at TIMESTAMP WITH TIME ZONE,
                    FOREIGN KEY (text_id) REFERENCES relationship_texts(id) ON DELETE SET NULL
                );
                
                -- 综合人设描述表
                CREATE TABLE IF NOT EXISTS comprehensive_personas (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL,
                    persona_summary TEXT NOT NULL,
                    trait_integration TEXT,
                    relationship_patterns TEXT,
                    interaction_advice TEXT,
                    based_on_insights INTEGER DEFAULT 0,
                    insight_keywords JSONB DEFAULT '[]'::jsonb,
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_persona_insights_user_id 
                ON persona_insights(user_id);
                
                CREATE INDEX IF NOT EXISTS idx_persona_insights_confirmed 
                ON persona_insights(confirmed);
                
                CREATE INDEX IF NOT EXISTS idx_comprehensive_personas_user_id 
                ON comprehensive_personas(user_id);
            """)
            
    async def store_persona_insight(self, 
                                  user_id: str,
                                  text_id: str,
                                  persona_keyword: str,
                                  persona_definition: str,
                                  confidence_score: float = 0.0) -> str:
        """
        存储人设洞察
        
        Args:
            user_id: 用户ID
            text_id: 关联的文本ID
            persona_keyword: 人设关键词
            persona_definition: 人设定义
            confidence_score: 置信度分数
            
        Returns:
            洞察记录ID
        """
        try:
            insight_id = str(uuid.uuid4())
            
            async with self._pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO persona_insights 
                    (id, user_id, text_id, persona_keyword, persona_definition, confidence_score)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, insight_id, user_id, text_id, persona_keyword, 
                persona_definition, confidence_score)
                
            logger.info(f"成功存储人设洞察，ID: {insight_id}")
            return insight_id
            
        except Exception as e:
            logger.error(f"存储人设洞察失败: {str(e)}")
            raise
            
    async def confirm_persona_insight(self, insight_id: str, confirmed: bool) -> bool:
        """
        确认或拒绝人设洞察
        
        Args:
            insight_id: 洞察ID
            confirmed: 是否确认
            
        Returns:
            操作是否成功
        """
        try:
            async with self._pool.acquire() as conn:
                result = await conn.execute("""
                    UPDATE persona_insights 
                    SET confirmed = $1, confirmed_at = $2
                    WHERE id = $3
                """, confirmed, datetime.utcnow() if confirmed else None, insight_id)
                
            return result == "UPDATE 1"
            
        except Exception as e:
            logger.error(f"确认人设洞察失败: {str(e)}")
            return False
            
    async def get_confirmed_insights(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户已确认的人设洞察
        
        Args:
            user_id: 用户ID
            
        Returns:
            已确认的洞察列表
        """
        try:
            async with self._pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT id, user_id, text_id, persona_keyword, persona_definition,
                           confidence_score, metadata, created_at, confirmed_at
                    FROM persona_insights
                    WHERE user_id = $1 AND confirmed = TRUE
                    ORDER BY confirmed_at DESC
                """, user_id)
                
                insights = []
                for row in rows:
                    insight = {
                        "id": str(row["id"]),
                        "user_id": row["user_id"],
                        "text_id": str(row["text_id"]) if row["text_id"] else None,
                        "persona_keyword": row["persona_keyword"],
                        "persona_definition": row["persona_definition"],
                        "confidence_score": row["confidence_score"],
                        "metadata": json.loads(row["metadata"]) if row["metadata"] else {},
                        "created_at": row["created_at"].isoformat(),
                        "confirmed_at": row["confirmed_at"].isoformat() if row["confirmed_at"] else None
                    }
                    insights.append(insight)
                    
                return insights
                
        except Exception as e:
            logger.error(f"获取已确认洞察失败: {str(e)}")
            return []

# 全局存储管理器实例
db_config = DatabaseConfig()
text_storage = TextStorageManager(db_config)
emotion_storage = EmotionStorageManager(db_config)
persona_storage = PersonaStorageManager(db_config) 
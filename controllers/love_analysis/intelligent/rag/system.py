"""
RAG系统 - 基于Lang<PERSON>hain和Qdrant

使用LangChain的向量数据库集成，实现人设洞察的存储和检索
支持语义搜索和相似度匹配
"""
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import os

# <PERSON><PERSON>hain核心组件
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import Qdrant
from langchain.schema import Document
from langchain.text_splitter import CharacterTextSplitter

# Qdrant客户端
from qdrant_client import QdrantClient
from qdrant_client.http import models

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PersonaRAGSystem:
    """人设RAG系统类"""
    
    def __init__(self, 
                 qdrant_url: str = "http://localhost:6333",
                 collection_name: str = "persona_insights",
                 embedding_model: str = "BAAI/bge-large-zh-v1.5"):
        """
        初始化RAG系统
        
        Args:
            qdrant_url: Qdrant服务器地址
            collection_name: 集合名称
            embedding_model: 嵌入模型名称
        """
        self.qdrant_url = qdrant_url
        self.collection_name = collection_name
        self.embedding_model_name = embedding_model
        
        # 初始化组件
        self._client = None
        self._embeddings = None
        self._vectorstore = None
        self._text_splitter = None
        
    async def initialize(self):
        """异步初始化RAG系统组件"""
        try:
            # 初始化Qdrant客户端
            self._client = QdrantClient(url=self.qdrant_url)
            logger.info(f"连接到Qdrant服务器: {self.qdrant_url}")
            
            # 初始化嵌入模型（使用中文BGE模型）
            self._embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model_name,
                model_kwargs={'device': 'cpu'},  # 可改为 'cuda' 如果有GPU
                encode_kwargs={'normalize_embeddings': True}
            )
            logger.info(f"初始化嵌入模型: {self.embedding_model_name}")
            
            # 初始化文本分割器
            self._text_splitter = CharacterTextSplitter(
                chunk_size=500,
                chunk_overlap=50,
                separator="\n"
            )
            
            # 创建或连接到集合
            await self._ensure_collection_exists()
            
            # 初始化向量存储
            self._vectorstore = Qdrant(
                client=self._client,
                collection_name=self.collection_name,
                embeddings=self._embeddings
            )
            
            logger.info("RAG系统初始化完成")
            
        except Exception as e:
            logger.error(f"RAG系统初始化失败: {str(e)}")
            raise
            
    async def _ensure_collection_exists(self):
        """确保Qdrant集合存在"""
        try:
            # 获取集合信息
            collections = self._client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # 创建新集合
                self._client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=models.VectorParams(
                        size=1024,  # BGE-large-zh-v1.5的向量维度
                        distance=models.Distance.COSINE
                    )
                )
                logger.info(f"创建Qdrant集合: {self.collection_name}")
            else:
                logger.info(f"连接到现有Qdrant集合: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"集合操作失败: {str(e)}")
            raise
            
    async def store_persona_insight(self, 
                                  insight_data: Dict[str, Any],
                                  user_id: str) -> bool:
        """
        存储人设洞察到向量数据库
        
        Args:
            insight_data: 人设洞察数据
            user_id: 用户ID
            
        Returns:
            存储是否成功
        """
        try:
            if not self._vectorstore:
                await self.initialize()
                
            # 构建文档内容
            content = f"人设特质: {insight_data['persona_keyword']}\n"
            content += f"定义: {insight_data['persona_definition']}\n"
            if 'context' in insight_data:
                content += f"上下文: {insight_data['context']}\n"
            if 'behavior_examples' in insight_data:
                content += f"行为表现: {insight_data['behavior_examples']}\n"
                
            # 构建元数据
            metadata = {
                "user_id": user_id,
                "persona_keyword": insight_data['persona_keyword'],
                "persona_definition": insight_data['persona_definition'],
                "insight_id": insight_data.get('insight_id', ''),
                "confirmed_at": insight_data.get('confirmed_at', datetime.utcnow().isoformat()),
                "original_text_id": insight_data.get('text_id', ''),
                "confidence_score": insight_data.get('confidence_score', 0.8)
            }
            
            # 创建文档
            document = Document(
                page_content=content,
                metadata=metadata
            )
            
            # 存储到向量数据库
            await self._vectorstore.aadd_documents([document])
            
            logger.info(f"成功存储人设洞察: {insight_data['persona_keyword']}")
            return True
            
        except Exception as e:
            logger.error(f"存储人设洞察失败: {str(e)}")
            return False
            
    async def search_similar_insights(self, 
                                    query: str,
                                    user_id: str,
                                    limit: int = 5,
                                    similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        搜索相似的人设洞察
        
        Args:
            query: 查询文本
            user_id: 用户ID
            limit: 返回结果数量限制
            similarity_threshold: 相似度阈值
            
        Returns:
            相似洞察列表
        """
        try:
            if not self._vectorstore:
                await self.initialize()
                
            # 构建过滤器（只搜索当前用户的洞察）
            filter_dict = {"user_id": user_id}
            
            # 执行相似度搜索
            results = await self._vectorstore.asimilarity_search_with_score(
                query=query,
                k=limit,
                filter=filter_dict
            )
            
            # 处理结果
            insights = []
            for doc, score in results:
                if score >= similarity_threshold:
                    insight = {
                        "content": doc.page_content,
                        "metadata": doc.metadata,
                        "similarity_score": score,
                        "persona_keyword": doc.metadata.get("persona_keyword", ""),
                        "persona_definition": doc.metadata.get("persona_definition", "")
                    }
                    insights.append(insight)
                    
            logger.info(f"找到 {len(insights)} 个相似洞察")
            return insights
            
        except Exception as e:
            logger.error(f"搜索相似洞察失败: {str(e)}")
            return []
            
    async def get_user_all_insights(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户的所有人设洞察
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户所有洞察列表
        """
        try:
            if not self._vectorstore:
                await self.initialize()
                
            # 使用向量数据库的过滤搜索
            # 这里使用一个通用查询，然后通过过滤器限制用户
            results = await self._vectorstore.asimilarity_search(
                query="人设特质",  # 通用查询
                k=100,  # 设置较大的k值
                filter={"user_id": user_id}
            )
            
            # 处理结果
            insights = []
            for doc in results:
                insight = {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "persona_keyword": doc.metadata.get("persona_keyword", ""),
                    "persona_definition": doc.metadata.get("persona_definition", ""),
                    "confirmed_at": doc.metadata.get("confirmed_at", ""),
                    "confidence_score": doc.metadata.get("confidence_score", 0.0)
                }
                insights.append(insight)
                
            # 按确认时间排序
            insights.sort(key=lambda x: x["confirmed_at"], reverse=True)
            
            logger.info(f"获取到用户 {user_id} 的 {len(insights)} 个洞察")
            return insights
            
        except Exception as e:
            logger.error(f"获取用户洞察失败: {str(e)}")
            return []
            
    async def delete_insight(self, insight_id: str, user_id: str) -> bool:
        """
        删除指定的人设洞察
        
        Args:
            insight_id: 洞察ID
            user_id: 用户ID
            
        Returns:
            删除是否成功
        """
        try:
            if not self._client:
                await self.initialize()
                
            # 使用Qdrant客户端直接删除
            # 注意：这需要知道向量的ID，实际实现中可能需要额外的映射
            # 这里提供一个基本框架
            
            logger.info(f"删除洞察 {insight_id} (用户: {user_id})")
            
            # TODO: 实现具体的删除逻辑
            # 可能需要先搜索找到对应的向量ID，然后删除
            
            return True
            
        except Exception as e:
            logger.error(f"删除洞察失败: {str(e)}")
            return False
            
    async def update_insight(self, 
                           insight_id: str,
                           updated_data: Dict[str, Any],
                           user_id: str) -> bool:
        """
        更新人设洞察
        
        Args:
            insight_id: 洞察ID
            updated_data: 更新的数据
            user_id: 用户ID
            
        Returns:
            更新是否成功
        """
        try:
            # 先删除旧的洞察，再添加新的
            # 这是一个简化的实现方式
            await self.delete_insight(insight_id, user_id)
            return await self.store_persona_insight(updated_data, user_id)
            
        except Exception as e:
            logger.error(f"更新洞察失败: {str(e)}")
            return False
            
    async def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Returns:
            集合统计信息
        """
        try:
            if not self._client:
                await self.initialize()
                
            info = self._client.get_collection(self.collection_name)
            
            return {
                "collection_name": self.collection_name,
                "vectors_count": info.vectors_count if info.vectors_count else 0,
                "indexed_vectors_count": info.indexed_vectors_count if info.indexed_vectors_count else 0,
                "status": "active"
            }
            
        except Exception as e:
            logger.error(f"获取集合统计失败: {str(e)}")
            return {"status": "error", "error": str(e)}

# 全局RAG系统实例
persona_rag = PersonaRAGSystem() 
"""
智能工作流编排系统

使用LangGraph构建复杂的业务流程，包括：
- 文本存储工作流
- 人设分析工作流
- 综合分析工作流

注意：情绪强度检测和安慰回应已在现有emotion_analysis系统中实现，
此工作流专注于数据存储和人设分析
"""
import logging
import json
from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime

# LangGraph核心组件
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain.schema import BaseMessage, HumanMessage, AIMessage

# 原子工具导入
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from tools.annotate_relationship_text import annotate_relationship_text
from tools.analyze_partner_persona import analyze_partner_persona
from tools.generate_narrative_advice import generate_narrative_advice
from tools.generate_comprehensive_persona_description import generate_comprehensive_persona_description

# 存储管理器
from .storage_managers import text_storage, emotion_storage, persona_storage
from .rag_system import persona_rag

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 定义工作流状态
class RelationshipAnalysisState(TypedDict):
    """关系分析工作流状态"""
    # 输入数据
    user_id: str
    original_text: str
    
    # 处理状态
    text_id: Optional[str]
    emotion_data: Optional[Dict[str, Any]]
    emotion_id: Optional[str]
    persona_insight: Optional[str]
    persona_insight_id: Optional[str]
    rewritten_text: Optional[str]
    
    # 输出结果
    results: Dict[str, Any]
    errors: List[str]
    completed_steps: List[str]

class ComprehensiveAnalysisState(TypedDict):
    """综合分析工作流状态"""
    user_id: str
    analysis_type: str
    time_period: Optional[str]
    
    # 数据收集
    confirmed_insights: List[Dict[str, Any]]
    emotion_history: List[Dict[str, Any]]
    text_history: List[Dict[str, Any]]
    
    # 分析结果
    persona_description: Optional[Dict[str, Any]]
    emotion_trends: Optional[Dict[str, Any]]
    relationship_patterns: Optional[Dict[str, Any]]
    
    # 输出
    final_report: Dict[str, Any]
    errors: List[str]

class IntelligentWorkflowManager:
    """智能工作流管理器"""
    
    def __init__(self):
        """初始化工作流管理器"""
        self._storage_workflow = None
        self._analysis_workflow = None
        
    async def initialize(self):
        """初始化工作流图"""
        try:
            # 初始化存储管理器
            await text_storage.initialize()
            await emotion_storage.initialize()
            await persona_storage.initialize()
            await persona_rag.initialize()
            
            # 构建工作流图
            self._build_storage_workflow()
            self._build_analysis_workflow()
            
            logger.info("工作流管理器初始化完成")
            
        except Exception as e:
            logger.error(f"工作流管理器初始化失败: {str(e)}")
            raise
            
    def _build_storage_workflow(self):
        """构建文本存储工作流（简化版，不包含安慰逻辑）"""
        # 创建状态图
        workflow = StateGraph(RelationshipAnalysisState)
        
        # 添加节点
        workflow.add_node("store_text", self._store_text_node)
        workflow.add_node("analyze_emotion", self._analyze_emotion_node)
        workflow.add_node("store_emotion", self._store_emotion_node)
        workflow.add_node("analyze_persona", self._analyze_persona_node)
        workflow.add_node("store_persona", self._store_persona_node)
        workflow.add_node("check_rewrite", self._check_rewrite_node)
        workflow.add_node("rewrite_text", self._rewrite_text_node)
        workflow.add_node("finalize", self._finalize_storage_node)
        
        # 设置入口点
        workflow.set_entry_point("store_text")
        
        # 添加边（简化的工作流路径）
        workflow.add_edge("store_text", "analyze_emotion")
        workflow.add_edge("analyze_emotion", "store_emotion")
        workflow.add_edge("store_emotion", "analyze_persona")
        workflow.add_edge("analyze_persona", "store_persona")
        workflow.add_edge("store_persona", "check_rewrite")
        
        # 条件分支：是否需要重写
        workflow.add_conditional_edges(
            "check_rewrite",
            self._should_rewrite,
            {
                "rewrite": "rewrite_text",
                "no_rewrite": "finalize"
            }
        )
        
        workflow.add_edge("rewrite_text", "finalize")
        workflow.add_edge("finalize", END)
        
        # 编译工作流
        self._storage_workflow = workflow.compile()
        
    def _build_analysis_workflow(self):
        """构建综合分析工作流"""
        # 创建状态图
        workflow = StateGraph(ComprehensiveAnalysisState)
        
        # 添加节点
        workflow.add_node("collect_insights", self._collect_insights_node)
        workflow.add_node("collect_emotions", self._collect_emotions_node)
        workflow.add_node("collect_texts", self._collect_texts_node)
        workflow.add_node("generate_persona_analysis", self._generate_persona_analysis_node)
        workflow.add_node("analyze_emotion_trends", self._analyze_emotion_trends_node)
        workflow.add_node("identify_patterns", self._identify_patterns_node)
        workflow.add_node("compile_report", self._compile_report_node)
        
        # 设置入口点
        workflow.set_entry_point("collect_insights")
        
        # 添加边（并行数据收集）
        workflow.add_edge("collect_insights", "collect_emotions")
        workflow.add_edge("collect_emotions", "collect_texts")
        
        # 分析阶段
        workflow.add_edge("collect_texts", "generate_persona_analysis")
        workflow.add_edge("generate_persona_analysis", "analyze_emotion_trends")
        workflow.add_edge("analyze_emotion_trends", "identify_patterns")
        workflow.add_edge("identify_patterns", "compile_report")
        workflow.add_edge("compile_report", END)
        
        # 编译工作流
        self._analysis_workflow = workflow.compile()
        
    # ========== 存储工作流节点 ==========
    
    async def _store_text_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """存储文本节点"""
        try:
            text_id = await text_storage.store_text(
                user_id=state["user_id"],
                original_text=state["original_text"]
            )
            
            state["text_id"] = text_id
            state["completed_steps"].append("store_text")
            logger.info(f"文本存储完成，ID: {text_id}")
            
        except Exception as e:
            error_msg = f"文本存储失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _analyze_emotion_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """情绪分析节点"""
        try:
            # 调用原子工具进行情绪分析
            emotion_annotations = await annotate_relationship_text(state["original_text"])
            
            # 取第一个分析结果（简化处理）
            if emotion_annotations:
                state["emotion_data"] = emotion_annotations[0]
                state["completed_steps"].append("analyze_emotion")
                logger.info("情绪分析完成")
            else:
                state["errors"].append("情绪分析返回空结果")
                
        except Exception as e:
            error_msg = f"情绪分析失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _store_emotion_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """存储情绪数据节点"""
        try:
            if state["emotion_data"] and state["text_id"]:
                emotion_id = await emotion_storage.store_emotion(
                    user_id=state["user_id"],
                    text_id=state["text_id"],
                    emotion_data=state["emotion_data"]
                )
                
                state["emotion_id"] = emotion_id
                state["completed_steps"].append("store_emotion")
                logger.info(f"情绪数据存储完成，ID: {emotion_id}")
            else:
                state["errors"].append("缺少情绪数据或文本ID")
                
        except Exception as e:
            error_msg = f"情绪存储失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _analyze_persona_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """人设分析节点"""
        try:
            # 调用原子工具进行人设分析
            persona_result = await analyze_partner_persona(state["original_text"])
            
            # 检查是否是有效的人设洞察（包含"—"分隔符）
            if "—" in persona_result:
                state["persona_insight"] = persona_result
                state["completed_steps"].append("analyze_persona")
                logger.info(f"人设分析完成: {persona_result}")
            else:
                # 如果是问题或无效结果，记录但不作为错误
                logger.info(f"人设分析结果: {persona_result}")
                
        except Exception as e:
            error_msg = f"人设分析失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _store_persona_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """存储人设洞察节点"""
        try:
            if state["persona_insight"] and state["text_id"]:
                # 解析人设洞察
                parts = state["persona_insight"].split("—", 1)
                if len(parts) == 2:
                    persona_keyword = parts[0].strip()
                    persona_definition = parts[1].strip()
                    
                    # 存储到PostgreSQL
                    insight_id = await persona_storage.store_persona_insight(
                        user_id=state["user_id"],
                        text_id=state["text_id"],
                        persona_keyword=persona_keyword,
                        persona_definition=persona_definition,
                        confidence_score=0.8
                    )
                    
                    state["persona_insight_id"] = insight_id
                    state["completed_steps"].append("store_persona")
                    logger.info(f"人设洞察存储完成，ID: {insight_id}")
                else:
                    logger.warning(f"人设洞察格式不正确: {state['persona_insight']}")
            
        except Exception as e:
            error_msg = f"人设存储失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _check_rewrite_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """检查是否需要重写节点"""
        # 这个节点主要用于条件判断，实际逻辑在 _should_rewrite 中
        state["completed_steps"].append("check_rewrite")
        return state
        
    async def _rewrite_text_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """重写文本节点"""
        try:
            # 调用原子工具进行文本重写
            rewritten_result = await generate_narrative_advice(
                user_text=state["original_text"],
                annotations=[state["emotion_data"]] if state["emotion_data"] else None
            )
            
            # 如果返回的是实际的重写内容（不是提示信息）
            if not rewritten_result.startswith("未检测到") and not rewritten_result.startswith("很抱歉"):
                state["rewritten_text"] = rewritten_result
                
                # 更新数据库中的文本记录
                if state["text_id"]:
                    await text_storage.update_text(
                        text_id=state["text_id"],
                        rewritten_text=rewritten_result
                    )
                    
                state["completed_steps"].append("rewrite_text")
                logger.info("文本重写完成")
                
        except Exception as e:
            error_msg = f"文本重写失败: {str(e)}"
            state["errors"].append(error_msg)
            logger.error(error_msg)
            
        return state
        
    async def _finalize_storage_node(self, state: RelationshipAnalysisState) -> RelationshipAnalysisState:
        """完成存储工作流节点"""
        # 整理最终结果
        state["results"] = {
            "text_id": state["text_id"],
            "emotion_id": state["emotion_id"],
            "persona_insight_id": state["persona_insight_id"],
            "emotion_data": state["emotion_data"],
            "persona_insight": state["persona_insight"],
            "rewritten_text": state["rewritten_text"],
            "completed_steps": state["completed_steps"],
            "errors": state["errors"]
        }
        
        state["completed_steps"].append("finalize")
        logger.info("存储工作流完成")
        return state
        
    # ========== 条件判断函数 ==========
    
    def _should_rewrite(self, state: RelationshipAnalysisState) -> str:
        """判断是否需要重写文本"""
        if not state["emotion_data"]:
            return "no_rewrite"
            
        emotions = state["emotion_data"].get("emotions", {})
        
        # 检查是否有明显的负面情绪需要重写
        negative_emotions = ["anger", "sadness", "fear", "disgust"]
        for emotion in negative_emotions:
            if emotions.get(emotion, {}).get("score", 0) > 0.3:
                return "rewrite"
                
        return "no_rewrite"
        
    # ========== 分析工作流节点 ==========
    
    async def _collect_insights_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """收集人设洞察数据节点"""
        try:
            insights = await persona_storage.get_confirmed_insights(state["user_id"])
            state["confirmed_insights"] = insights
            logger.info(f"收集到 {len(insights)} 个已确认洞察")
            
        except Exception as e:
            state["errors"].append(f"收集洞察失败: {str(e)}")
            
        return state
        
    async def _collect_emotions_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """收集情绪历史数据节点"""
        try:
            emotions = await emotion_storage.get_user_emotions(
                user_id=state["user_id"],
                limit=100
            )
            state["emotion_history"] = emotions
            logger.info(f"收集到 {len(emotions)} 条情绪记录")
            
        except Exception as e:
            state["errors"].append(f"收集情绪失败: {str(e)}")
            
        return state
        
    async def _collect_texts_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """收集文本历史数据节点"""
        try:
            texts = await text_storage.get_user_texts(
                user_id=state["user_id"],
                limit=50
            )
            state["text_history"] = texts
            logger.info(f"收集到 {len(texts)} 条文本记录")
            
        except Exception as e:
            state["errors"].append(f"收集文本失败: {str(e)}")
            
        return state
        
    async def _generate_persona_analysis_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """生成人设分析节点"""
        try:
            if state["confirmed_insights"]:
                # 调用原子工具生成综合人设描述
                persona_description = await generate_comprehensive_persona_description(
                    state["confirmed_insights"]
                )
                state["persona_description"] = persona_description
                logger.info("综合人设分析完成")
            else:
                state["persona_description"] = {"message": "暂无足够的人设数据进行分析"}
                
        except Exception as e:
            state["errors"].append(f"人设分析失败: {str(e)}")
            
        return state
        
    async def _analyze_emotion_trends_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """分析情绪趋势节点"""
        try:
            if state["emotion_history"]:
                # 简单的情绪趋势分析
                emotion_trends = self._calculate_emotion_trends(state["emotion_history"])
                state["emotion_trends"] = emotion_trends
                logger.info("情绪趋势分析完成")
            else:
                state["emotion_trends"] = {"message": "暂无情绪数据"}
                
        except Exception as e:
            state["errors"].append(f"情绪趋势分析失败: {str(e)}")
            
        return state
        
    async def _identify_patterns_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """识别关系模式节点"""
        try:
            # 基于文本和情绪数据识别关系模式
            patterns = self._identify_relationship_patterns(
                state["text_history"],
                state["emotion_history"]
            )
            state["relationship_patterns"] = patterns
            logger.info("关系模式识别完成")
            
        except Exception as e:
            state["errors"].append(f"模式识别失败: {str(e)}")
            
        return state
        
    async def _compile_report_node(self, state: ComprehensiveAnalysisState) -> ComprehensiveAnalysisState:
        """编译最终报告节点"""
        state["final_report"] = {
            "user_id": state["user_id"],
            "analysis_type": state["analysis_type"],
            "persona_analysis": state["persona_description"],
            "emotion_trends": state["emotion_trends"],
            "relationship_patterns": state["relationship_patterns"],
            "data_summary": {
                "insights_count": len(state["confirmed_insights"]),
                "emotion_records_count": len(state["emotion_history"]),
                "text_records_count": len(state["text_history"])
            },
            "generated_at": datetime.utcnow().isoformat(),
            "errors": state["errors"]
        }
        
        logger.info("综合分析报告编译完成")
        return state
        
    # ========== 辅助分析方法 ==========
    
    def _calculate_emotion_trends(self, emotions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算情绪趋势"""
        if not emotions:
            return {}
            
        # 按时间排序
        sorted_emotions = sorted(emotions, key=lambda x: x["created_at"])
        
        # 计算各情绪的平均值和趋势
        emotion_types = ["anger", "sadness", "fear", "joy", "disgust", "neutral"]
        trends = {}
        
        for emotion_type in emotion_types:
            scores = [e["emotions"][emotion_type]["score"] for e in sorted_emotions]
            trends[emotion_type] = {
                "average": sum(scores) / len(scores),
                "recent_average": sum(scores[-10:]) / min(10, len(scores)),
                "trend": "上升" if scores[-1] > scores[0] else "下降" if scores[-1] < scores[0] else "稳定"
            }
            
        return {
            "trends": trends,
            "total_records": len(emotions),
            "analysis_period": {
                "start": sorted_emotions[0]["created_at"],
                "end": sorted_emotions[-1]["created_at"]
            }
        }
        
    def _identify_relationship_patterns(self, 
                                      texts: List[Dict[str, Any]], 
                                      emotions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """识别关系模式"""
        patterns = {
            "communication_frequency": len(texts),
            "emotional_volatility": 0,
            "positive_ratio": 0,
            "common_themes": []
        }
        
        if emotions:
            # 计算情绪波动性
            joy_scores = [e["emotions"]["joy"]["score"] for e in emotions]
            anger_scores = [e["emotions"]["anger"]["score"] for e in emotions]
            
            if joy_scores:
                avg_joy = sum(joy_scores) / len(joy_scores)
                avg_anger = sum(anger_scores) / len(anger_scores)
                patterns["positive_ratio"] = avg_joy / (avg_joy + avg_anger) if (avg_joy + avg_anger) > 0 else 0.5
                
        return patterns
        
    # ========== 公共接口 ==========
    
    async def run_storage_workflow(self, user_id: str, text: str) -> Dict[str, Any]:
        """运行存储工作流"""
        try:
            initial_state = RelationshipAnalysisState(
                user_id=user_id,
                original_text=text,
                text_id=None,
                emotion_data=None,
                emotion_id=None,
                persona_insight=None,
                persona_insight_id=None,
                rewritten_text=None,
                results={},
                errors=[],
                completed_steps=[]
            )
            
            # 运行工作流
            final_state = await self._storage_workflow.ainvoke(initial_state)
            return final_state["results"]
            
        except Exception as e:
            logger.error(f"存储工作流运行失败: {str(e)}")
            return {"error": str(e)}
            
    async def run_analysis_workflow(self, 
                                  user_id: str, 
                                  analysis_type: str = "comprehensive",
                                  time_period: Optional[str] = None) -> Dict[str, Any]:
        """运行综合分析工作流"""
        try:
            initial_state = ComprehensiveAnalysisState(
                user_id=user_id,
                analysis_type=analysis_type,
                time_period=time_period,
                confirmed_insights=[],
                emotion_history=[],
                text_history=[],
                persona_description=None,
                emotion_trends=None,
                relationship_patterns=None,
                final_report={},
                errors=[]
            )
            
            # 运行工作流
            final_state = await self._analysis_workflow.ainvoke(initial_state)
            return final_state["final_report"]
            
        except Exception as e:
            logger.error(f"分析工作流运行失败: {str(e)}")
            return {"error": str(e)}

# 全局工作流管理器实例
workflow_manager = IntelligentWorkflowManager() 
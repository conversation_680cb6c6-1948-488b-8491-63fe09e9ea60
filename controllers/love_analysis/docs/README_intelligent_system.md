# 智能关系分析系统

## 📋 系统概述

智能关系分析系统是一个基于LangChain和LangGraph的高级分析平台，专注于关系数据处理、人设洞察和综合分析功能。

**重要说明**: 本系统与现有的情绪分析系统(`emotion_analysis.py`)配合工作，不重复实现情绪检测和安慰回应功能。

### 🔧 核心技术栈

- **WebSocket服务器**: 实时双向通信（端口8766）
- **工作流引擎**: LangGraph状态图编排
- **向量数据库**: Qdrant + BGE-large-zh-v1.5中文嵌入
- **关系数据库**: PostgreSQL存储结构化数据
- **原子工具**: 基于现有tools模块的功能组件

### 🏗️ 系统架构

```
完整服务架构（通过main.py统一启动）
├── MCP服务器 (8080端口)
├── 情绪分析WebSocket服务器 (8765端口)
│   ├── 情绪检测功能
│   ├── 安慰回应功能
│   └── 情绪强度监控
└── 智能分析WebSocket服务器 (8766端口)
    ├── LangGraph工作流编排
    ├── PostgreSQL数据存储
    │   ├── 关系文本表
    │   ├── 情绪记录表
    │   └── 人设洞察表
    ├── Qdrant向量数据库
    │   └── 人设洞察向量
    └── 原子工具集成
        ├── 人设分析工具
        ├── 文本重写工具
        └── 综合描述工具
```

## 🚀 启动系统

### 1. 环境准备

```bash
# 设置数据库环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=relationship_analysis
export DB_USER=postgres
export DB_PASSWORD=your_password

# 启动PostgreSQL和Qdrant
docker run -p 5432:5432 -e POSTGRES_PASSWORD=your_password postgres:13
docker run -p 6333:6333 qdrant/qdrant
```

### 2. 安装依赖

```bash
pip install langchain langgraph qdrant-client asyncpg websockets
pip install sentence-transformers  # 用于BGE嵌入模型
```

### 3. 启动完整服务

**⚠️ 重要: 请使用统一入口启动**

```bash
# 使用统一入口启动全部服务
python main.py
```

这将同时启动：
- MCP服务器 (端口8080)
- 情绪分析WebSocket服务器 (端口8765)
- 智能分析WebSocket服务器 (端口8766)

## 🔄 工作流说明

### 智能存储工作流（简化版）

```mermaid
graph TD
    A[存储文本] --> B[分析情绪]
    B --> C[存储情绪]
    C --> D[分析人设]
    D --> E[存储人设]
    E --> F{需要重写?}
    F -->|是| G[重写文本]
    F -->|否| H[完成]
    G --> H
    
    note1[情绪强度检测和安慰回应<br/>在emotion_analysis系统中处理]
    C -.-> note1
```

### 功能分工

**情绪分析服务器 (8765端口)**:
- 实时情绪检测
- 情绪强度评估
- 自动安慰回应
- 情绪异常监控

**智能分析服务器 (8766端口)**:
- 关系文本存储
- 人设洞察分析
- RAG知识库管理
- 综合分析报告
- 情绪数据归档

## 📡 WebSocket API 接口

### 连接信息

- **情绪分析**: `ws://localhost:8765`
- **智能分析**: `ws://localhost:8766`
- **协议**: WebSocket
- **数据格式**: JSON

### 支持的消息类型

#### 智能分析服务器 (8766端口)

注意: 情绪检测和安慰回应请使用情绪分析服务器 (8765端口)

#### 1. 存储关系文本

```javascript
// 发送到 ws://localhost:8766
{
    "type": "store_relationship_text",
    "user_id": "user123",
    "text": "今天他对我很冷淡，感觉很难受"
}

// 接收响应（不包含安慰回应，安慰回应在8765端口处理）
{
    "type": "emotion_detected",
    "emotion_data": {
        "emotions": {
            "anger": {"score": 0.2},
            "sadness": {"score": 0.8}
        },
        "behavior_tag": "情感忽视"
    }
}

{
    "type": "persona_insight_found",
    "insight": {
        "id": "insight-uuid",
        "persona_keyword": "情感疏离",
        "persona_definition": "在亲密关系中容易保持距离"
    },
    "requires_confirmation": true
}

{
    "type": "storage_complete",
    "text_id": "text-uuid",
    "completed_steps": ["store_text", "analyze_emotion", "store_emotion", "analyze_persona", "store_persona"]
}
```

#### 2. 确认人设洞察

```javascript
// 发送
{
    "type": "confirm_persona_insight",
    "insight_id": "insight-uuid",
    "confirmed": true,
    "user_id": "user123"
}

// 接收
{
    "type": "persona_insight_confirmed",
    "insight_id": "insight-uuid",
    "confirmed": true,
    "success": true,
    "message": "人设洞察已确认并存储到知识库"
}

// 可能的额外响应（如果达到综合分析条件）
{
    "type": "comprehensive_persona_generated",
    "persona_description": {
        "persona_summary": "具有回避型依恋特质的伴侣",
        "trait_integration": "...",
        "relationship_patterns": "...",
        "interaction_advice": "..."
    },
    "based_on_insights": 3
}
```

#### 3. 请求综合分析

```javascript
// 发送
{
    "type": "request_comprehensive_analysis",
    "user_id": "user123",
    "analysis_type": "comprehensive",
    "include_emotion_trends": true,
    "include_persona_analysis": true,
    "time_period": "last_3_months"
}

// 接收
{
    "type": "analysis_started",
    "message": "开始生成综合分析报告",
    "analysis_type": "comprehensive"
}

{
    "type": "comprehensive_analysis",
    "analysis_result": {
        "persona_analysis": {
            "persona_summary": "...",
            "trait_integration": "...",
            "relationship_patterns": "...",
            "interaction_advice": "..."
        },
        "emotion_trends": {
            "trends": {
                "anger": {"average": 0.3, "trend": "下降"},
                "joy": {"average": 0.4, "trend": "上升"}
            },
            "total_records": 50
        },
        "relationship_patterns": {
            "communication_frequency": 25,
            "positive_ratio": 0.6,
            "emotional_volatility": 0.3
        },
        "data_summary": {
            "insights_count": 5,
            "emotion_records_count": 50,
            "text_records_count": 25
        }
    }
}
```

#### 4. 获取情绪日记

```javascript
// 发送
{
    "type": "get_emotion_diary",
    "user_id": "user123",
    "date_range": ["2024-01-01", "2024-12-31"],
    "chart_type": "trend"  // 或 "heatmap", "distribution"
}

// 接收
{
    "type": "emotion_diary",
    "chart_data": [
        {
            "date": "2024-01-01",
            "emotions": {
                "anger": {"score": 0.2},
                "sadness": {"score": 0.3},
                // ...
            },
            "behavior_tag": "沟通困难"
        }
        // ... 更多数据点
    ],
    "insights": {
        "emotion_averages": {
            "anger": 0.25,
            "sadness": 0.35,
            // ...
        },
        "dominant_emotion": "sadness",
        "stability_score": 0.7,
        "analysis_summary": "主导情绪为sadness，情绪稳定性为0.70"
    },
    "chart_type": "trend",
    "total_records": 100
}
```

#### 5. 获取关系历史

```javascript
// 发送
{
    "type": "get_relationship_history",
    "user_id": "user123",
    "limit": 20,
    "offset": 0
}

// 接收
{
    "type": "relationship_history",
    "texts": [
        {
            "id": "text-uuid",
            "original_text": "原始文本",
            "rewritten_text": "重写文本",
            "tags": ["标签1", "标签2"],
            "created_at": "2024-01-01T00:00:00"
        }
        // ... 更多记录
    ],
    "total_count": 20,
    "limit": 20,
    "offset": 0
}
```

## 🔧 配置说明

### 环境变量

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=relationship_analysis
DB_USER=postgres
DB_PASSWORD=your_password

# Qdrant配置
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=persona_insights

# 嵌入模型配置
EMBEDDING_MODEL=BAAI/bge-large-zh-v1.5
EMBEDDING_DEVICE=cpu  # 或 cuda
```

### 性能优化建议

1. **数据库索引**: 确保在`user_id`和`created_at`字段上建立索引
2. **连接池**: 使用适当的数据库连接池大小
3. **GPU加速**: 如有GPU，设置`EMBEDDING_DEVICE=cuda`
4. **缓存策略**: 考虑对频繁查询的结果进行缓存

## 🚨 注意事项

1. **数据隐私**: 确保用户数据的安全性和隐私保护
2. **错误处理**: 系统具有完善的错误处理和恢复机制
3. **监控日志**: 定期检查系统日志和性能指标
4. **数据备份**: 定期备份PostgreSQL和Qdrant数据
5. **版本兼容**: 确保LangChain和LangGraph版本兼容性

## 📈 扩展功能

### 计划中的功能

1. **实时情绪监控**: 连续的情绪状态跟踪
2. **关系健康评分**: 基于多维度数据的关系质量评估
3. **个性化建议**: 基于用户历史的定制化建议
4. **多模态分析**: 支持语音和图像输入
5. **社交网络分析**: 扩展到多人关系分析

### 技术路线图

- [ ] 实现更复杂的LangGraph工作流
- [ ] 集成更多原子工具
- [ ] 优化向量检索性能
- [ ] 添加实时推荐系统
- [ ] 实现多租户支持 
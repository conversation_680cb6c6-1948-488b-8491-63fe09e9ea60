# 关系分析系统 - 模块化架构文档

## 📁 目录结构

```
controllers/love_analysis/
├── __init__.py                    # 主模块入口
├── emotion/                       # 情绪分析模块
│   ├── __init__.py               # 模块接口暴露
│   ├── analysis.py               # 主要分析服务
│   ├── websocket.py              # WebSocket服务器
│   ├── workflows.py              # LangGraph工作流
│   ├── handlers.py               # 消息处理器
│   ├── utils.py                  # 工具函数
│   └── server_start.py           # 独立启动脚本
├── intelligent/                   # 智能分析模块
│   ├── __init__.py               # 模块接口暴露
│   ├── server.py                 # WebSocket服务器
│   ├── workflows.py              # LangGraph工作流
│   ├── handlers.py               # 消息处理器
│   ├── server_start.py           # 独立启动脚本
│   ├── rag/                      # RAG检索子模块
│   │   ├── __init__.py          # RAG接口暴露
│   │   └── system.py            # RAG系统实现
│   └── storage/                  # 数据存储子模块
│       ├── __init__.py          # 存储接口暴露
│       └── managers.py          # 存储管理器
├── docs/                         # 文档目录
│   ├── MODULAR_ARCHITECTURE.md  # 模块化架构文档
│   ├── README_intelligent_system.md
│   ├── README_modular_architecture.md
│   └── USAGE.md
├── scripts/                      # 工具脚本
└── shared/                       # 共享组件（预留）
```

## 🏗️ 模块化设计原则

### 1. **单一职责原则**
- 每个模块只负责一个特定的功能领域
- `emotion/` - 专注情绪检测和支持
- `intelligent/` - 专注人设分析和数据存储

### 2. **接口隔离原则**
- 每个模块通过`__init__.py`暴露清晰的API接口
- 隐藏内部实现细节，只暴露必要的功能

### 3. **依赖倒置原则**
- 模块间通过抽象接口交互
- 高层模块不依赖底层模块的具体实现

### 4. **开闭原则**
- 对扩展开放：可以轻松添加新的子模块
- 对修改封闭：现有模块的修改不影响其他模块

## 📦 模块详细说明

### Emotion模块 (情绪分析)

**职责**：
- 实时情绪检测
- 智能情绪支持
- 情绪强度监控
- WebSocket通信管理

**主要组件**：
```python
# 导入示例
from controllers.love_analysis.emotion import (
    start_emotion_analysis_server,  # 服务器启动
    emotion_server,                 # 服务器实例
    EmotionAnalysisWorkflow,        # 情绪分析工作流
    EmotionalSupportWorkflow,       # 情绪支持工作流
    EmotionAnalysisUtils,           # 分析工具
    EmotionalSupportUtils           # 支持工具
)
```

**启动方式**：
```bash
# 独立启动
python controllers/love_analysis/emotion/server_start.py

# 模块导入启动
from controllers.love_analysis.emotion import start_emotion_analysis_server
await start_emotion_analysis_server(host="0.0.0.0", port=8765)
```

### Intelligent模块 (智能分析)

**职责**：
- 人设洞察分析
- RAG知识检索
- 综合分析报告
- 数据持久化存储

**主要组件**：
```python
# 导入示例
from controllers.love_analysis.intelligent import (
    intelligent_server,           # 服务器实例
    workflow_manager,             # 工作流管理器
    IntelligentMessageHandlers,   # 消息处理器
    persona_rag,                  # RAG系统
    text_storage,                 # 文本存储
    emotion_storage,              # 情绪存储
    persona_storage               # 人设存储
)
```

**子模块**：

#### RAG子模块
```python
from controllers.love_analysis.intelligent.rag import (
    PersonaRAGSystem,
    persona_rag
)
```

#### Storage子模块
```python
from controllers.love_analysis.intelligent.storage import (
    DatabaseConfig,
    TextStorageManager,
    EmotionStorageManager,
    PersonaStorageManager,
    text_storage,
    emotion_storage,
    persona_storage
)
```

## 🔄 模块间交互

### 数据流向
```mermaid
graph TD
    A[用户输入] --> B[Emotion模块]
    B --> C[情绪检测]
    C --> D[Intelligent模块]
    D --> E[数据存储]
    D --> F[人设分析]
    F --> G[RAG检索]
    G --> H[综合分析]
    
    I[Storage子模块] --> E
    J[RAG子模块] --> G
```

### API交互
```python
# 模块间数据共享示例
from controllers.love_analysis import emotion, intelligent

# 情绪数据传递给智能分析
emotion_data = await emotion.EmotionAnalysisUtils.analyze_text("...")
await intelligent.emotion_storage.store_emotion(user_id, text_id, emotion_data)

# 人设洞察存储到RAG系统
persona_insight = await intelligent.workflow_manager.analyze_persona("...")
await intelligent.persona_rag.store_persona_insight(persona_insight, user_id)
```

## 🚀 启动和部署

### 1. 模块化启动
```python
# 方式一：独立启动脚本
python controllers/love_analysis/emotion/server_start.py
python controllers/love_analysis/intelligent/server_start.py

# 方式二：模块导入
import asyncio
from controllers.love_analysis.emotion import start_emotion_analysis_server
from controllers.love_analysis.intelligent import intelligent_server

async def main():
    # 启动情绪分析服务器
    emotion_task = asyncio.create_task(
        start_emotion_analysis_server(host="0.0.0.0", port=8765)
    )
    
    # 启动智能分析服务器
    intelligent_task = asyncio.create_task(
        intelligent_server.start_server(host="0.0.0.0", port=8766)
    )
    
    await asyncio.gather(emotion_task, intelligent_task)

asyncio.run(main())
```

### 2. PM2管理
```bash
# 启动所有服务
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs emotion-analysis-server
pm2 logs intelligent-analysis-server
```

## 🔧 开发和扩展

### 添加新模块
```bash
# 1. 创建新模块目录
mkdir controllers/love_analysis/new_module

# 2. 创建模块文件
touch controllers/love_analysis/new_module/__init__.py
touch controllers/love_analysis/new_module/main.py
touch controllers/love_analysis/new_module/server_start.py

# 3. 在主模块中注册
# 编辑 controllers/love_analysis/__init__.py
```

### 添加子模块
```bash
# 在现有模块下创建子模块
mkdir controllers/love_analysis/intelligent/new_submodule
touch controllers/love_analysis/intelligent/new_submodule/__init__.py
```

### 模块测试
```python
# 单元测试示例
import unittest
from controllers.love_analysis.emotion import EmotionAnalysisUtils

class TestEmotionModule(unittest.TestCase):
    def test_emotion_analysis(self):
        utils = EmotionAnalysisUtils()
        result = utils.analyze_text("测试文本")
        self.assertIsNotNone(result)

if __name__ == '__main__':
    unittest.main()
```

## 📊 模块监控

### 性能监控
```python
# 模块性能监控
from controllers.love_analysis import get_system_info

system_info = get_system_info()
print(f"系统版本: {system_info['version']}")
print(f"架构类型: {system_info['architecture']}")
print(f"可用模块: {list(system_info['modules'].keys())}")
```

### 日志管理
```python
import logging

# 模块化日志配置
logger_emotion = logging.getLogger('emotion_module')
logger_intelligent = logging.getLogger('intelligent_module')

# 不同模块使用不同的日志级别
logger_emotion.setLevel(logging.INFO)
logger_intelligent.setLevel(logging.DEBUG)
```

## 🎯 最佳实践

### 1. 模块设计
- ✅ 每个模块职责单一明确
- ✅ 通过`__init__.py`暴露清晰API
- ✅ 避免循环依赖
- ✅ 使用相对导入

### 2. 代码组织
- ✅ 相关功能文件放在同一目录
- ✅ 子模块按功能进一步划分
- ✅ 公共工具放在shared目录
- ✅ 文档统一放在docs目录

### 3. 部署管理
- ✅ 每个模块可独立启动调试
- ✅ 使用PM2统一管理进程
- ✅ 配置文件集中管理
- ✅ 日志分模块记录

### 4. 扩展原则
- ✅ 新功能优先考虑作为子模块
- ✅ 保持模块间接口稳定
- ✅ 向后兼容性考虑
- ✅ 完善的文档和测试

## 🔗 相关文档

- [PM2架构管理](../../README_PM2_ARCHITECTURE.md)
- [智能分析系统](./README_intelligent_system.md)
- [使用指南](./USAGE.md)

---

通过模块化架构，系统实现了：
- ✅ 功能解耦和独立开发
- ✅ 清晰的代码组织结构
- ✅ 便于维护和扩展
- ✅ 支持独立部署和测试 
# 情绪分析服务使用说明

## 🚀 统一启动（推荐）

现在您可以通过项目根目录的 `main.py` 统一启动所有服务：

```bash
# 在项目根目录执行
python main.py
```

### 启动后的服务架构

```
🚀 完整服务架构已启动
├── 📡 MCP服务器    - http://0.0.0.0:8080/mcp
└── 🔌 WebSocket服务器 - ws://0.0.0.0:8765
```

### 服务说明

1. **MCP服务器 (端口8080)**
   - 提供标准MCP协议接口
   - 支持所有注册的工具和工作流
   - 包含 `emotion_analysis_workflow` 工作流

2. **WebSocket服务器 (端口8765)**
   - 提供实时情绪分析服务
   - 支持长连接和实时推送
   - 专为前端Vue应用设计

## 🔧 独立启动（可选）

如果只需要WebSocket服务器，也可以独立启动：

```bash
# 进入 controllers/love_analysis 目录
cd controllers/love_analysis

# 启动WebSocket服务器
python emotion_analysis.py
```

## 📋 使用方式

### 1. 作为工作流调用

```python
from controllers.love_analysis.emotion_analysis import emotion_analysis_workflow

# 调用情绪分析工作流
result = await emotion_analysis_workflow(
    user_text="我感觉很难过，我们的关系出现了问题",
    user_id="user123"
)

print(result["results"]["processed_data"]["summary"]["dominant_emotion"])
```

### 2. 通过WebSocket连接

```javascript
// 前端Vue代码示例
const socket = new WebSocket('ws://localhost:8765');

socket.onopen = function(event) {
    console.log('连接已建立');
};

socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'analysis_result') {
        console.log('分析结果:', data.data);
    }
};

// 发送分析请求
function analyzeText(text) {
    socket.send(JSON.stringify({
        type: 'analyze_text',
        text: text
    }));
}
```

## 🎯 功能特性

### 情绪分析工作流
- ✅ 符合项目controller注册规范
- ✅ 使用 `@service_manager.workflow` 装饰器
- ✅ 可被其他模块调用
- ✅ 返回标准化的结果格式

### WebSocket服务器
- ✅ 实时双向通信
- ✅ 自动连接管理
- ✅ 心跳检测机制
- ✅ 错误处理和恢复
- ✅ 支持多客户端并发连接

## 📊 数据格式

### 工作流返回格式
```json
{
    "success": true,
    "workflow_type": "emotion_analysis",
    "user_id": "user123",
    "user_text": "原始文本",
    "results": {
        "success": true,
        "annotation_data": [...],
        "processed_data": {
            "segments": [...],
            "summary": {
                "total_segments": 1,
                "dominant_emotion": "sadness",
                "average_emotions": {...},
                "confidence_level": 0.85
            }
        },
        "timestamp": 1672531200.123
    }
}
```

### WebSocket消息格式
```json
{
    "type": "analysis_result",
    "data": {
        "segments": [...],
        "summary": {...}
    },
    "original_text": "原始文本",
    "timestamp": 1672531200.123,
    "status": "success"
}
```

## 🔍 监控和日志

启动后可以观察到以下日志信息：
- ✅ MCP工具和工作流注册信息
- ✅ WebSocket连接建立和断开
- ✅ 情绪分析请求处理过程
- ✅ 错误和异常处理记录

## 🛠 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8080  # MCP服务器
   lsof -i :8765  # WebSocket服务器
   ```

2. **依赖缺失**
   ```bash
   # 确保安装了websockets
   pip install websockets
   ```

3. **工作流未注册**
   - 确保 `core.service_manager` 已正确初始化
   - 检查 `tools.annotate_relationship_text` 是否可导入

## 🎉 优势对比

| 启动方式 | MCP服务器 | WebSocket服务器 | 推荐度 |
|---------|----------|----------------|--------|
| **统一启动** | ✅ 自动启动 | ✅ 自动启动 | ⭐⭐⭐⭐⭐ |
| 独立启动 | ❌ 需手动启动 | ✅ 自动启动 | ⭐⭐⭐ |

**建议：始终使用 `python main.py` 启动完整服务架构** 
#!/usr/bin/env python3
"""
情绪分析统一服务器启动脚本

同时启动HTTP API服务器和WebSocket服务器
- HTTP API: 用户认证、服务状态等
- WebSocket: 实时情绪分析和支持
适用于pm2进程管理
"""
import asyncio
import logging
import signal
import sys
import os
import uvicorn
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.insert(0, project_root)

# 导入所需模块
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from controllers.love_analysis.emotion.websocket import start_emotion_analysis_server as start_ws_server
from controllers.love_analysis.emotion.api import emotion_api_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="情绪分析服务",
    description="提供情绪分析、用户认证、WebSocket实时服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(emotion_api_router)

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "emotion_analysis",
        "message": "情绪分析服务运行中",
        "services": {
            "http_api": "http://localhost:8000",
            "websocket": "ws://localhost:8765"
        },
        "docs": "/docs"
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "services": {
            "http_api": "running",
            "websocket": "running",
            "database": "connected"
        }
    }

def signal_handler(sig, frame):
    """处理中断信号"""
    logger.info("接收到停止信号，正在关闭情绪分析服务器...")
    sys.exit(0)

async def start_http_server():
    """启动HTTP API服务器"""
    try:
        logger.info("启动HTTP API服务器 - 端口: 8000")
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0", 
            port=8000,
            log_level="info",
            access_log=False  # 减少日志噪音
        )
        server = uvicorn.Server(config)
        await server.serve()
    except Exception as e:
        logger.error(f"HTTP服务器启动失败: {str(e)}")
        raise

async def start_websocket_server():
    """启动WebSocket服务器"""
    try:
        logger.info("启动WebSocket服务器 - 端口: 8765")
        await start_ws_server(host="0.0.0.0", port=8765)
    except Exception as e:
        logger.error(f"WebSocket服务器启动失败: {str(e)}")
        raise

async def main():
    """主函数 - 同时启动HTTP和WebSocket服务器"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 检查必要的依赖
        logger.info("检查依赖模块...")
        try:
            import uvicorn
            import fastapi
            import websockets
            logger.info("✓ 所有依赖模块可用")
        except ImportError as e:
            logger.error(f"✗ 缺少依赖模块: {str(e)}")
            logger.error("请安装所需依赖: pip install fastapi uvicorn websockets")
            sys.exit(1)
        
        # 初始化数据库
        logger.info("🔧 初始化数据库...")
        try:
            from controllers.love_analysis.emotion.database import initialize_database
            await initialize_database()
            logger.info("✅ 数据库初始化成功")
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {str(e)}")
            logger.error("数据库是必需的，服务无法启动")
            sys.exit(1)
        
        logger.info("=== 情绪分析统一服务器启动 ===")
        logger.info("HTTP API: http://0.0.0.0:8000")
        logger.info("  - 根路径: /")
        logger.info("  - 认证API: /emotion/auth/")
        logger.info("  - API文档: /docs")
        logger.info("  - 健康检查: /health")
        logger.info("WebSocket: ws://0.0.0.0:8765")
        logger.info("  - 需要用户认证")
        logger.info("  - 支持实时情绪分析")
        logger.info("功能: 用户认证、情绪检测、情绪支持、强度监控")
        logger.info("管理: 通过pm2进程管理")
        logger.info("架构: HTTP + WebSocket 双服务")
        logger.info("路径: %s", project_root)
        logger.info("=====================================")
        
        # 同时启动HTTP和WebSocket服务器
        tasks = await asyncio.gather(
            start_http_server(),
            start_websocket_server(),
            return_exceptions=True
        )
        
        # 检查是否有异常
        for i, task in enumerate(tasks):
            if isinstance(task, Exception):
                logger.error(f"服务器 {i} 启动异常: {str(task)}")
                raise task
        
    except KeyboardInterrupt:
        logger.info("收到键盘中断，停止服务器")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 
"""
WebSocket消息处理器模块

处理不同类型的WebSocket消息和业务逻辑
"""
import json
import logging
import time
from typing import Dict, List, Any
from websockets.server import WebSocketServerProtocol

# 导入工具函数
from .utils import (
    calculate_confidence_width,
    calculate_intensity_level,
    calculate_confidence_level,
    determine_overall_intensity_and_risk,
    is_emotion_outlier,
    generate_analysis_summary,
    DEFAULT_INTENSITY_THRESHOLDS,
    DEFAULT_CONFIDENCE_THRESHOLDS
)

# 配置日志
logger = logging.getLogger(__name__)

class MessageHandler:
    """
    WebSocket消息处理器
    
    负责处理各种类型的WebSocket消息
    """
    
    def __init__(self):
        """初始化消息处理器"""
        self.intensity_thresholds = DEFAULT_INTENSITY_THRESHOLDS
        self.confidence_thresholds = DEFAULT_CONFIDENCE_THRESHOLDS
    
    async def handle_analyze_text(
        self,
        data: Dict[str, Any],
        websocket: WebSocketServerProtocol,
        session_id: str,
        user_id: str,
        send_message_func
    ) -> None:
        """
        处理文本分析请求
        
        Args:
            data: 消息数据
            websocket: WebSocket连接对象
            session_id: WebSocket会话ID
            user_id: 用户ID（数据库中的用户ID）
            send_message_func: 发送消息的函数
        """
        text = data.get("text", "").strip()
        
        if not text:
            error_message = {
                "type": "validation_error",
                "error": "文本内容不能为空",
                "timestamp": time.time()
            }
            await send_message_func(websocket, error_message)
            return
        
        # 执行情绪分析
        await self._process_emotion_analysis(text, websocket, session_id, user_id, send_message_func)
    
    async def handle_generate_support(
        self,
        data: Dict[str, Any],
        websocket: WebSocketServerProtocol,
        session_id: str,
        user_id: str,
        send_message_func
    ) -> None:
        """
        处理情绪支持生成请求
        
        Args:
            data: 消息数据
            websocket: WebSocket连接对象
            session_id: WebSocket会话ID
            user_id: 用户ID（数据库中的用户ID）
            send_message_func: 发送消息的函数
        """
        context = data.get("context", "").strip()
        emotion_data = data.get("data", [])
        history_message = data.get("message", "").strip()
        
        # 验证必要参数
        if not context:
            error_message = {
                "type": "validation_error",
                "error": "context 参数不能为空",
                "timestamp": time.time()
            }
            await send_message_func(websocket, error_message)
            return
        
        if not emotion_data or not isinstance(emotion_data, list):
            error_message = {
                "type": "validation_error", 
                "error": "data 参数必须是非空的情绪数据列表",
                "timestamp": time.time()
            }
            await send_message_func(websocket, error_message)
            return
        
        # 执行情绪支持处理
        await self._process_emotional_support(
            context, emotion_data, history_message, websocket, session_id, user_id, send_message_func
        )
    
    async def handle_ping(
        self,
        websocket: WebSocketServerProtocol,
        send_message_func
    ) -> None:
        """
        处理心跳检测
        
        Args:
            websocket: WebSocket连接对象
            send_message_func: 发送消息的函数
        """
        pong_message = {
            "type": "pong",
            "timestamp": time.time()
        }
        await send_message_func(websocket, pong_message)
    
    async def handle_unknown_type(
        self,
        message_type: str,
        websocket: WebSocketServerProtocol,
        send_message_func
    ) -> None:
        """
        处理未知消息类型
        
        Args:
            message_type: 消息类型
            websocket: WebSocket连接对象
            send_message_func: 发送消息的函数
        """
        error_message = {
            "type": "unknown_message_type",
            "error": f"未知的消息类型: {message_type}",
            "supported_types": ["analyze_text", "generate_support", "ping"],
            "timestamp": time.time()
        }
        await send_message_func(websocket, error_message)
    
    async def _process_emotion_analysis(
        self,
        text: str,
        websocket: WebSocketServerProtocol,
        session_id: str,
        user_id: str,
        send_message_func
    ) -> None:
        """
        处理情绪分析请求的核心逻辑
        集成会话管理和数据库操作
        
        Args:
            text: 用户输入的文本
            websocket: WebSocket连接对象
            session_id: WebSocket会话ID
            user_id: 用户ID（数据库中的用户ID）
            send_message_func: 发送消息的函数
        """
        try:
            # 发送开始处理消息
            start_message = {
                "type": "analysis_started",
                "message": "开始处理情绪分析...",
                "timestamp": time.time()
            }
            await send_message_func(websocket, start_message)
            
            logger.info(f"连接 {user_id} 开始处理情绪分析请求，文本长度: {len(text)}")
            
            # 1. 会话管理 - 创建或获取用户会话
            try:
                from .data_operations import SessionManager
                session_id = await SessionManager.create_or_get_session(
                    user_id=user_id,
                    metadata={
                        "connection_type": "websocket",
                        "analysis_type": "emotion_analysis",
                        "text_length": len(text)
                    }
                )
                logger.info(f"获取会话ID: {session_id} for 连接: {user_id}")
            except Exception as e:
                logger.warning(f"会话管理失败，使用默认处理: {str(e)}")
                session_id = f"ws_{user_id}_{int(time.time())}"
            
            # 2. 导入并使用工作流
            from .workflows import emotion_analysis_workflow
            workflow_result = await emotion_analysis_workflow(
                user_text=text,
                user_id=user_id,
                session_id=session_id  # 传递会话ID
            )
            
            # 3. 检查工作流执行结果
            if workflow_result["success"]:
                analysis_data = workflow_result["results"]["processed_data"]
                
                # 计算详细的情绪强度分析
                intensity_analysis = self._calculate_intensity(analysis_data)
                
                # 4. 保存真正的情绪分析记录（业务层面）
                emotion_analysis_id = None
                mcp_call_id = workflow_result.get("database_records", {}).get("analysis_record_id")
                
                try:
                    from .data_operations import get_database
                    from database.models.emotion_analysis import EmotionAnalysis
                    from uuid import uuid4
                    
                    db = get_database()
                    if db:
                        async with db.get_session() as db_session:
                            # 创建真正的情绪分析记录
                            emotion_analysis = EmotionAnalysis(
                                user_id=user_id,
                                mcp_tool_call_id=str(mcp_call_id) if mcp_call_id else str(uuid4()),
                                user_text_input=text,
                                threshold_results=[intensity_analysis],  # 保存业务层面的强度分析结果
                                analysis_version="1.0"
                            )
                            await emotion_analysis.save(db_session)
                            emotion_analysis_id = emotion_analysis.id
                            logger.info(f"保存情绪分析记录: {emotion_analysis_id} for 用户: {user_id}")
                    else:
                        logger.warning("数据库未初始化，跳过情绪分析记录保存")
                        
                except Exception as e:
                    logger.error(f"保存情绪分析记录失败: {str(e)}")
                    # 不影响主流程，继续执行
                
                # 5. 发送分析结果
                result_message = {
                    "type": "analysis_result",
                    "data": {
                        "intensity": intensity_analysis
                    },
                    "session_info": {
                        "session_id": session_id,
                        "mcp_call_id": str(mcp_call_id) if mcp_call_id else None,  # 确保是字符串
                        "emotion_analysis_id": str(emotion_analysis_id) if emotion_analysis_id else None,  # 确保是字符串
                        "processing_time_ms": workflow_result["results"].get("processing_time_ms")
                    },
                    "original_text": text,
                    "timestamp": time.time(),
                    "status": "success"
                }

                await send_message_func(websocket, result_message)
                
                segments_count = len(analysis_data.get("segments", []))
                logger.info(f"连接 {user_id} 分析完成，返回 {segments_count} 个文本段落的分析结果")
            else:
                # 工作流执行失败
                raise Exception(workflow_result.get("error", "工作流执行失败"))
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"处理情绪分析时出错: {error_msg}")
            
            # 根据错误类型提供不同的处理方案
            if "MCP服务器未初始化" in error_msg:
                error_response = {
                    "type": "mcp_server_error",
                    "error": "MCP服务器未正确初始化",
                    "message": "系统正在重新初始化，请稍后重试",
                    "suggestion": "请等待几秒钟后重新发送分析请求",
                    "timestamp": time.time(),
                    "status": "error",
                    "retry_suggested": True
                }
            elif "找不到模块" in error_msg or "import" in error_msg.lower():
                error_response = {
                    "type": "module_error", 
                    "error": "系统模块加载失败",
                    "message": "部分分析模块暂时不可用",
                    "suggestion": "请联系系统管理员检查模块配置",
                    "timestamp": time.time(),
                    "status": "error",
                    "retry_suggested": False
                }
            else:
                error_response = {
                    "type": "analysis_error",
                    "error": error_msg,
                    "message": "分析过程中发生错误",
                    "suggestion": "请检查输入文本并重试",
                    "timestamp": time.time(),
                    "status": "error",
                    "retry_suggested": True
                }
            
            await send_message_func(websocket, error_response)
    
    async def _process_emotional_support(
        self,
        context: str,
        data: List[Dict[str, Any]],
        message: str,
        websocket: WebSocketServerProtocol,
        session_id: str,
        user_id: str,
        send_message_func
    ) -> None:
        """
        处理情绪支持请求的核心逻辑
        集成会话管理和分析记录关联
        
        Args:
            context: 当前监测的文本
            data: 情绪分析数据列表
            message: 历史输入
            websocket: WebSocket连接对象
            session_id: WebSocket会话ID
            user_id: 用户ID（数据库中的用户ID）
            send_message_func: 发送消息的函数
        """
        try:
            # 发送开始处理消息
            start_message = {
                "type": "support_started",
                "message": "开始生成情绪支持回复...",
                "timestamp": time.time()
            }
            await send_message_func(websocket, start_message)
            
            logger.info(f"连接 {user_id} 开始处理情绪支持请求，情绪数据数量: {len(data)}")
            
            # 1. 会话管理 - 创建或获取用户会话
            session_data_id = None
            analysis_record_id = None
            try:
                from .data_operations import SessionManager, AnalysisRecordManager
                
                session_data_id = await SessionManager.create_or_get_session(
                    user_id=user_id,  # 使用用户ID而不是连接ID
                    metadata={
                        "connection_type": "websocket",
                        "analysis_type": "emotional_support",
                        "context_length": len(context)
                    }
                )
                
                # 尝试获取最近的分析记录ID用于关联
                recent_records = await AnalysisRecordManager.get_recent_analysis_records(
                    session_id=session_data_id,
                    limit=1
                )
                
                if recent_records and len(recent_records) > 0:
                    analysis_record_id = recent_records[0].get("id")
                    logger.info(f"关联到分析记录ID: {analysis_record_id}")
                    
                logger.info(f"获取会话数据ID: {session_data_id} for 用户: {user_id}")
                
            except Exception as e:
                logger.warning(f"会话管理失败，使用默认处理: {str(e)}")
                session_data_id = f"ws_{user_id}_{int(time.time())}"
            
            # 2. 导入并使用工作流
            from .workflows import emotional_support_workflow
            workflow_result = await emotional_support_workflow(
                context=context,
                data=data,
                message=message,
                user_id=user_id,  # 使用用户ID而不是连接ID
                session_id=session_data_id,
                analysis_record_id=analysis_record_id  # 传递关联的分析记录ID
            )
            
            # 3. 检查工作流执行结果
            if workflow_result["success"]:
                support_data = workflow_result["results"]
                
                # 4. 保存真正的情绪支持记录（业务层面）
                emotion_support_id = None
                mcp_call_id = workflow_result.get("database_records", {}).get("response_record_id")
                
                try:
                    from .data_operations import get_database
                    from database.models.emotion_support import EmotionSupport
                    from uuid import uuid4
                    
                    db = get_database()
                    if db:
                        async with db.get_session() as db_session:
                            # 创建真正的情绪支持记录
                            emotion_support = EmotionSupport(
                                user_id=user_id,
                                mcp_tool_call_id=str(mcp_call_id) if mcp_call_id else str(uuid4()),
                                input_data={
                                    "context": context,
                                    "emotion_data": data,
                                    "history_message": message,
                                    "detected_emotion": support_data.get("detected_emotion")
                                },
                                response_data={
                                    "comfort_response": support_data["comfort_response"],
                                    "confidence": support_data.get("confidence", 0.0),
                                    "response_type": "comfort"
                                },
                                support_type="emotional_comfort",
                                satisfaction_score=None  # 可以后续由用户评分
                            )
                            await emotion_support.save(db_session)
                            emotion_support_id = emotion_support.id
                            logger.info(f"保存情绪支持记录: {emotion_support_id} for 用户: {user_id}")
                    else:
                        logger.warning("数据库未初始化，跳过情绪支持记录保存")
                        
                except Exception as e:
                    logger.error(f"保存情绪支持记录失败: {str(e)}")
                    # 不影响主流程，继续执行
                
                # 5. 发送支持回复结果
                result_message = {
                    "type": "support_result",
                    "data": {
                        "comfort_response": support_data["comfort_response"],
                        "detected_emotion": support_data.get("detected_emotion"),
                        "emotion_data_count": support_data.get("emotion_data_count", 0)
                    },
                    "session_info": {
                        "session_id": session_data_id,
                        "mcp_call_id": str(mcp_call_id) if mcp_call_id else None,  # 确保是字符串
                        "emotion_support_id": str(emotion_support_id) if emotion_support_id else None,  # 确保是字符串
                        "analysis_record_id": analysis_record_id,
                        "processing_time_ms": support_data.get("processing_time_ms")
                    },
                    "original_context": context,
                    "timestamp": time.time(),
                    "status": "success"
                }
                
                await send_message_func(websocket, result_message)
                
                logger.info(f"连接 {user_id} 情绪支持处理完成")
            else:
                # 工作流执行失败
                raise Exception(workflow_result.get("error", "工作流执行失败"))
            
        except Exception as e:
            logger.error(f"处理情绪支持请求时出错: {str(e)}")
            
            # 发送错误消息
            error_message = {
                "type": "support_error",
                "error": str(e),
                "message": "生成支持回复时出现错误",
                "timestamp": time.time(),
                "status": "error"
            }
            await send_message_func(websocket, error_message)
    
    def _calculate_intensity(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算情绪强度分析结果
        
        Args:
            analysis_data: 返回的分析数据
        
        Returns:
            包含情绪强度详细分析的字典
        """
        # 获取 analysis_data 中的segments数据
        segments = analysis_data.get("segments", [])
        
        if not segments:
            return {
                "overall_intensity": "minimal",
                "emotion_analysis": {},
                "outlier_emotions": [],
                "risk_level": "low",
                "summary": "无有效数据进行分析"
            }

        # 定义负面情绪列表 - 只有这些情绪的异常值才会被报告为预警
        negative_emotions = {"sadness", "disgust", "anger", "fear"}
        
        # 分析结果汇总
        emotion_results = {}
        outlier_emotions = []
        high_intensity_count = 0
        extreme_intensity_count = 0
        
        # 分析每个文本段落的情绪
        for segment_idx, segment in enumerate(segments):
            emotions = segment.get("emotions", {})
            
            # 分析每种情绪
            for emotion, data in emotions.items():
                score = data.get("score", 0.0)
                confidence_interval = data.get("confidence_interval", [0, 1])
                
                # 计算置信区间宽度
                confidence_width = calculate_confidence_width(confidence_interval)
                
                # 判断强度等级
                intensity_level = calculate_intensity_level(score, self.intensity_thresholds)
                
                # 判断置信度等级
                confidence_level = calculate_confidence_level(confidence_width, self.confidence_thresholds)
                
                # 统计强度计数（所有情绪都计算）
                if intensity_level == "high":
                    high_intensity_count += 1
                elif intensity_level == "extreme":
                    extreme_intensity_count += 1
                
                # 异常判断
                is_outlier_result, outlier_reason = is_emotion_outlier(intensity_level, confidence_level)
                
                # 只有负面情绪的异常值才加入outlier_emotions（用于预警）
                if is_outlier_result and emotion in negative_emotions:
                    outlier_emotions.append({
                        "emotion": emotion,
                        "score": score,
                        "intensity_level": intensity_level,
                        "confidence_level": confidence_level,
                        "segment_index": segment_idx,
                        "outlier_reason": outlier_reason,
                        "confidence_interval": confidence_interval
                    })
                
                # 保存情绪分析结果（所有情绪都保存）
                if emotion not in emotion_results:
                    emotion_results[emotion] = []
                
                emotion_results[emotion].append({
                    "segment_index": segment_idx,
                    "score": score,
                    "intensity_level": intensity_level,
                    "confidence_level": confidence_level,
                    "confidence_width": confidence_width,
                    "is_outlier": is_outlier_result
                })
        
        # 计算整体强度等级和风险等级
        overall_intensity, risk_level = determine_overall_intensity_and_risk(
            high_intensity_count, extreme_intensity_count
        )
        
        # 生成分析摘要（只基于负面情绪异常值的数量）
        summary = generate_analysis_summary(
            len(outlier_emotions), overall_intensity, risk_level
        )
        
        # 计算统计信息
        total_emotions = len([item for sublist in emotion_results.values() for item in sublist])
        
        return {
            "overall_intensity": overall_intensity,
            "emotion_analysis": emotion_results,
            "outlier_emotions": outlier_emotions,  # 只包含负面情绪的异常值
            "risk_level": risk_level,
            "statistics": {
                "total_emotions_analyzed": total_emotions,
                "high_intensity_count": high_intensity_count,
                "extreme_intensity_count": extreme_intensity_count,
                "outlier_count": len(outlier_emotions)  # 只统计负面情绪异常值
            },
            "summary": summary
        } 
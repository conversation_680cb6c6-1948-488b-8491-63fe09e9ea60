"""
情绪分析工作流模块

包含所有情绪相关的工作流定义
集成数据库操作，保存分析结果和回复记录
"""
import logging
import time
import asyncio
import json
from typing import Dict, List, Any
from uuid import UUID

# 配置日志
logger = logging.getLogger(__name__)

def ensure_json_serializable(data: Any) -> Any:
    """
    确保数据能够JSON序列化，特别处理UUID对象
    
    Args:
        data: 需要序列化的数据
        
    Returns:
        可JSON序列化的数据
    """
    if isinstance(data, UUID):
        return str(data)
    elif isinstance(data, dict):
        return {key: ensure_json_serializable(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [ensure_json_serializable(item) for item in data]
    elif isinstance(data, tuple):
        return tuple(ensure_json_serializable(item) for item in data)
    else:
        return data

async def emotion_analysis_workflow(
    user_text: str, 
    user_id: str = "default_user",
    session_id: str = None
) -> Dict[str, Any]:
    """
    执行情绪分析工作流，包括文本标注、情绪分析和结果处理
    集成数据库操作，保存分析结果到数据库
    
    Args:
        user_text: 用户输入的关系文本
        user_id: 用户ID
        session_id: 会话ID（可选，如果未提供则自动创建）
        
    Returns:
        包含完整情绪分析结果的字典
    """
    start_time = time.time()
    logger.info(f"开始执行情绪分析工作流，用户ID: {user_id}")
    
    workflow_result = {
        "success": True,
        "workflow_type": "emotion_analysis",
        "user_id": user_id,
        "session_id": session_id,
        "user_text": user_text,
        "results": {},
        "database_records": {}
    }
    
    try:
        # 1. 数据库准备 - 创建或获取会话
        try:
            from .data_operations import SessionManager, AnalysisRecordManager, WorkflowLogger
            
            if session_id is None:
                session_id = await SessionManager.create_or_get_session(
                    user_id=user_id,
                    metadata={"workflow_type": "emotion_analysis", "start_time": start_time}
                )
                workflow_result["session_id"] = session_id
                logger.info(f"创建/获取会话: {session_id}")
            
            # 记录工作流开始
            await WorkflowLogger.log_workflow_step(
                session_id=session_id,
                user_id=user_id,
                workflow_type="emotion_analysis",
                workflow_status="started",
                step_name="workflow_init",
                step_status="success",
                input_data={"user_text": user_text},
                metadata={"start_time": start_time}
            )
            
        except Exception as e:
            logger.warning(f"数据库操作失败，继续执行工作流: {str(e)}")
            # 如果数据库操作失败，使用临时会话ID
            if session_id is None:
                session_id = f"temp_{user_id}_{int(start_time)}"
                workflow_result["session_id"] = session_id
        
        # 2. MCP工具调用 - 情绪分析
        logger.info("正在调用annotate_relationship_text工具...")
        
        # 记录MCP调用开始
        mcp_start_time = time.time()
        
        # 动态导入MCP客户端函数
        from ..emotion.analysis import _call_mcp_tool
        
        # 调用情绪分析工具
        annotation_result = await _call_mcp_tool(
            tool_name="annotate_relationship_text",
            parameters={"user_text": user_text}
        )
        
        mcp_processing_time = int((time.time() - mcp_start_time) * 1000)
        
        if not annotation_result.get("success"):
            raise Exception(f"MCP工具调用失败: {annotation_result.get('error', '未知错误')}")
        
        # 3. 数据处理和分析
        tool_data = annotation_result.get("data", [])
        
        # 处理和汇总分析结果
        from .utils import process_emotion_analysis_results
        processed_results = process_emotion_analysis_results(tool_data)
        
        # 4. 构建最终结果
        total_processing_time = int((time.time() - start_time) * 1000)
        
        workflow_result["results"] = {
            "success": True,
            "annotation_data": tool_data,
            "processed_data": processed_results,
            "processing_time_ms": total_processing_time,
            "mcp_processing_time_ms": mcp_processing_time,
            "timestamp": time.time()
        }
        
        # 5. 保存到数据库
        try:
            # 保存分析记录
            analysis_record_id = await AnalysisRecordManager.save_analysis_record(
                session_id=session_id,
                user_id=user_id,
                user_text=user_text,
                workflow_type="emotion_analysis",
                analysis_results=workflow_result["results"],
                processing_time_ms=total_processing_time,
                mcp_tool_calls=[{
                    "tool_name": "annotate_relationship_text",
                    "parameters": {"user_text": user_text},
                    "processing_time_ms": mcp_processing_time,
                    "success": True
                }],
                success=True
            )
            
            workflow_result["database_records"]["analysis_record_id"] = analysis_record_id
            
            # 记录工作流成功完成
            await WorkflowLogger.log_workflow_step(
                session_id=session_id,
                user_id=user_id,
                workflow_type="emotion_analysis",
                workflow_status="completed",
                step_name="analysis_complete",
                step_status="success",
                processing_time_ms=total_processing_time,
                output_data=ensure_json_serializable({
                    "analysis_record_id": analysis_record_id,
                    "dominant_emotion": processed_results.get("summary", {}).get("dominant_emotion"),
                    "total_segments": processed_results.get("summary", {}).get("total_segments")
                })
            )
            
            logger.info(f"分析结果已保存到数据库，记录ID: {analysis_record_id}")
            
        except Exception as e:
            logger.error(f"保存分析结果到数据库失败: {str(e)}")
            # 即使数据库保存失败，也返回分析结果
            workflow_result["database_records"]["error"] = str(e)
        
        logger.info(f"情绪分析工作流执行成功，用户ID: {user_id}, 处理时间: {total_processing_time}ms")
        return workflow_result
        
    except Exception as e:
        total_processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"情绪分析工作流执行失败: {str(e)}")
        
        workflow_result["success"] = False
        workflow_result["error"] = str(e)
        workflow_result["results"] = {
            "success": False,
            "error": str(e),
            "processing_time_ms": total_processing_time,
            "timestamp": time.time()
        }
        
        # 记录失败到数据库
        try:
            from .data_operations import AnalysisRecordManager, WorkflowLogger
            
            if session_id:
                # 保存失败记录
                analysis_record_id = await AnalysisRecordManager.save_analysis_record(
                    session_id=session_id,
                    user_id=user_id,
                    user_text=user_text,
                    workflow_type="emotion_analysis",
                    analysis_results=workflow_result["results"],
                    processing_time_ms=total_processing_time,
                    success=False,
                    error_message=str(e)
                )
                
                workflow_result["database_records"]["analysis_record_id"] = analysis_record_id
                
                # 记录工作流失败
                await WorkflowLogger.log_workflow_step(
                    session_id=session_id,
                    user_id=user_id,
                    workflow_type="emotion_analysis",
                    workflow_status="failed",
                    step_name="workflow_error",
                    step_status="error",
                    processing_time_ms=total_processing_time,
                    error_message=str(e)
                )
                
        except Exception as db_error:
            logger.error(f"保存失败记录到数据库时出错: {str(db_error)}")
        
        return workflow_result

async def emotional_support_workflow(
    context: str, 
    data: List[Dict[str, Any]], 
    message: str, 
    user_id: str = "default_user",
    session_id: str = None,
    analysis_record_id: int = None
) -> Dict[str, Any]:
    """
    执行情绪支持回复工作流，调用generate_comforting_response生成安慰回复
    集成数据库操作，保存生成的回复到数据库
    
    Args:
        context: 当前监测的文本
        data: 情绪分析数据列表
        message: 历史输入（作为上下文的参考）
        user_id: 用户ID
        session_id: 会话ID
        analysis_record_id: 关联的分析记录ID
        
    Returns:
        包含安慰回复结果的字典
    """
    start_time = time.time()
    logger.info(f"开始执行情绪支持工作流，用户ID: {user_id}")
    
    workflow_result = {
        "success": True,
        "workflow_type": "emotional_support",
        "user_id": user_id,
        "session_id": session_id,
        "context": context,
        "message": message,
        "results": {},
        "database_records": {}
    }
    
    try:
        # 1. 数据库准备
        try:
            from .data_operations import SessionManager, ResponseRecordManager, WorkflowLogger
            
            if session_id is None:
                session_id = await SessionManager.create_or_get_session(
                    user_id=user_id,
                    metadata={"workflow_type": "emotional_support", "start_time": start_time}
                )
                workflow_result["session_id"] = session_id
                logger.info(f"创建/获取会话: {session_id}")
            
            # 记录工作流开始
            await WorkflowLogger.log_workflow_step(
                session_id=session_id,
                user_id=user_id,
                workflow_type="emotional_support",
                workflow_status="started",
                step_name="workflow_init",
                step_status="success",
                input_data={
                    "context": context,
                    "message": message,
                    "data_count": len(data)
                },
                metadata={"start_time": start_time}
            )
            
        except Exception as e:
            logger.warning(f"数据库操作失败，继续执行工作流: {str(e)}")
            if session_id is None:
                session_id = f"temp_{user_id}_{int(start_time)}"
                workflow_result["session_id"] = session_id
        
        # 2. 构建输入文本
        if message.strip():
            # 如果有历史消息，将其作为背景信息
            input_text = f"背景：{message}\n\n当前情况：{context}"
        else:
            input_text = context
        
        # 3. 提取主要情绪并组合成描述性字符串
        detected_emotion = "未检测到明显情绪"
        if data and len(data) > 0:
            # 提取所有情绪和强度信息
            emotion_descriptions = []
            
            for emotion_item in data[:3]:  # 最多取前3个情绪数据
                emotion_name = emotion_item.get("emotion", "")
                if emotion_name:
                    # 获取情绪强度信息
                    score = emotion_item.get("score", 0.0)
                    intensity_level = emotion_item.get("intensity_level", "")
                    
                    if intensity_level:
                        emotion_descriptions.append(f"{emotion_name}({intensity_level})")
                    elif score > 0:
                        # 根据分数判断强度
                        if score >= 0.8:
                            emotion_descriptions.append(f"{emotion_name}(极强)")
                        elif score >= 0.6:
                            emotion_descriptions.append(f"{emotion_name}(强烈)")
                        elif score >= 0.4:
                            emotion_descriptions.append(f"{emotion_name}(中等)")
                        else:
                            emotion_descriptions.append(f"{emotion_name}(轻微)")
                    else:
                        emotion_descriptions.append(emotion_name)
            
            # 组合成一句话
            if emotion_descriptions:
                detected_emotion = "主要情绪: " + "、".join(emotion_descriptions)
            else:
                # 如果没有有效的情绪名称，尝试从第一个数据中获取基本信息
                first_item = data[0]
                if isinstance(first_item, dict):
                    # 检查是否有emotion字段
                    if "emotion" in first_item and first_item["emotion"]:
                        detected_emotion = f"检测到情绪: {first_item['emotion']}"
                    else:
                        detected_emotion = "检测到情绪波动"
        
        # 4. MCP工具调用 - 生成安慰回复
        logger.info("正在调用generate_comforting_response工具...")
        
        mcp_start_time = time.time()
        
        # 确保所有参数都是字符串类型
        input_text_str = str(input_text) if input_text is not None else ""
        detected_emotion_str = str(detected_emotion) if detected_emotion is not None else ""
        
        logger.info(f"准备调用工具参数: user_text='{input_text_str[:50]}...', detected_emotion='{detected_emotion_str}'")
        
        # 动态导入MCP客户端函数
        from ..emotion.analysis import _call_mcp_tool
        
        # 调用情绪支持工具
        comfort_result = await _call_mcp_tool(
            tool_name="generate_comforting_response",
            parameters={
                "user_text": input_text_str,
                "detected_emotion": detected_emotion_str
            }
        )
        
        mcp_processing_time = int((time.time() - mcp_start_time) * 1000)
        
        if not comfort_result.get("success"):
            raise Exception(f"MCP工具调用失败: {comfort_result.get('error', '未知错误')}")
        
        # 5. 获取生成的回复
        comfort_response = comfort_result.get("data", "")
        
        total_processing_time = int((time.time() - start_time) * 1000)
        
        workflow_result["results"] = {
            "success": True,
            "comfort_response": comfort_response,
            "input_text": input_text,
            "detected_emotion": detected_emotion,
            "emotion_data_count": len(data),
            "processing_time_ms": total_processing_time,
            "mcp_processing_time_ms": mcp_processing_time,
            "timestamp": time.time()
        }

        # 打印workflow_result
        logger.info(f"workflow_result: {workflow_result}")
        
        # 6. 保存到数据库
        try:
            # 保存回复记录
            response_record_id = await ResponseRecordManager.save_response_record(
                analysis_record_id=analysis_record_id or -1,  # 如果没有分析记录ID，使用-1
                session_id=session_id,
                user_id=user_id,
                response_type="emotional_support",
                generated_response=comfort_response,
                input_context=input_text,
                detected_emotion=detected_emotion,
                generation_params={
                    "emotion_data_count": len(data),
                    "has_background_message": bool(message.strip())
                },
                processing_time_ms=total_processing_time,
                mcp_tool_calls=[{
                    "tool_name": "generate_comforting_response",
                    "parameters": {
                        "user_text": input_text_str,
                        "detected_emotion": detected_emotion_str
                    },
                    "processing_time_ms": mcp_processing_time,
                    "success": True
                }],
                success=True
            )
            
            workflow_result["database_records"]["response_record_id"] = response_record_id
            
            # 记录工作流成功完成
            await WorkflowLogger.log_workflow_step(
                session_id=session_id,
                user_id=user_id,
                workflow_type="emotional_support",
                workflow_status="completed",
                step_name="response_generated",
                step_status="success",
                processing_time_ms=total_processing_time,
                output_data=ensure_json_serializable({
                    "response_record_id": response_record_id,
                    "detected_emotion": detected_emotion,
                    "response_length": len(comfort_response)
                })
            )
            
            logger.info(f"回复记录已保存到数据库，记录ID: {response_record_id}")
            
        except Exception as e:
            logger.error(f"保存回复记录到数据库失败: {str(e)}")
            workflow_result["database_records"]["error"] = str(e)
        
        logger.info(f"情绪支持工作流执行成功，用户ID: {user_id}, 处理时间: {total_processing_time}ms")
        return workflow_result
        
    except Exception as e:
        total_processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"情绪支持工作流执行失败: {str(e)}")
        
        workflow_result["success"] = False
        workflow_result["error"] = str(e)
        workflow_result["results"] = {
            "success": False,
            "error": str(e),
            "processing_time_ms": total_processing_time,
            "timestamp": time.time()
        }
        
        # 记录失败到数据库
        try:
            from .data_operations import ResponseRecordManager, WorkflowLogger
            
            if session_id:
                # 保存失败记录
                response_record_id = await ResponseRecordManager.save_response_record(
                    analysis_record_id=analysis_record_id or -1,
                    session_id=session_id,
                    user_id=user_id,
                    response_type="emotional_support",
                    generated_response="",
                    input_context=context,
                    detected_emotion=detected_emotion,
                    processing_time_ms=total_processing_time,
                    success=False,
                    error_message=str(e)
                )
                
                workflow_result["database_records"]["response_record_id"] = response_record_id
                
                # 记录工作流失败
                await WorkflowLogger.log_workflow_step(
                    session_id=session_id,
                    user_id=user_id,
                    workflow_type="emotional_support",
                    workflow_status="failed",
                    step_name="workflow_error",
                    step_status="error",
                    processing_time_ms=total_processing_time,
                    error_message=str(e)
                )
                
        except Exception as db_error:
            logger.error(f"保存失败记录到数据库时出错: {str(db_error)}")
        
        return workflow_result 
"""
情绪分析控制器 - 主入口模块

提供基于WebSocket的实时情绪分析和情绪支持服务
使用模块化架构，易于维护和扩展
"""
import asyncio
import json
import logging
from typing import Optional
from datetime import timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MCP服务器配置 - 使用官方mcp库
MCP_SERVER_URL = "http://localhost:8080/mcp"

# 全局MCP客户端
_mcp_client = None

async def _call_mcp_tool(tool_name: str, parameters: dict) -> dict:
    """
    调用MCP工具 - 使用官方mcp库的简化方式
    
    Args:
        tool_name: 工具名称
        parameters: 工具参数
        
    Returns:
        工具执行结果
    """
    try:
        # 使用官方mcp库进行每次调用
        from mcp import ClientSession
        from mcp.client.streamable_http import streamablehttp_client
        
        logger.info(f"调用MCP工具: {tool_name}, 参数: {parameters}")
        
        # 为每次调用创建独立的连接避免资源管理冲突
        async with streamablehttp_client(
            url=MCP_SERVER_URL,
            headers=None,
            timeout=timedelta(seconds=30),
            sse_read_timeout=timedelta(seconds=300)
        ) as (read_stream, write_stream, get_session_id):
            
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                
                # 调用工具
                result = await session.call_tool(tool_name, parameters)
                
                # 提取结果内容
                if hasattr(result, 'content'):
                    if isinstance(result.content, list) and len(result.content) > 0:
                        content = result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
                    else:
                        content = str(result.content)
                else:
                    content = str(result)
                
                logger.info(f"MCP工具调用成功: {tool_name}")
                logger.debug(f"MCP工具原始返回内容: {content[:200]}...")
                
                # 尝试解析JSON内容
                try:
                    # 如果内容是JSON字符串，解析为Python对象
                    parsed_data = json.loads(content)
                    logger.info(f"成功解析JSON数据，类型: {type(parsed_data)}")
                    return {
                        "success": True,
                        "data": parsed_data
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON，返回原始字符串
                    logger.debug(f"MCP工具返回非JSON内容: {content[:100]}...")
                    return {
                        "success": True,
                        "data": content
                    }
        
    except Exception as e:
        logger.error(f"调用MCP工具失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def _check_mcp_server_health() -> bool:
    """检查MCP服务器健康状态"""
    try:
        # 简单的健康检查 - 尝试列出工具
        from mcp import ClientSession
        from mcp.client.streamable_http import streamablehttp_client
        
        async with streamablehttp_client(
            url=MCP_SERVER_URL,
            headers=None,
            timeout=timedelta(seconds=5),
            sse_read_timeout=timedelta(seconds=10)
        ) as (read_stream, write_stream, get_session_id):
            
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                response = await session.list_tools()
                return len(response.tools) > 0
                
    except Exception as e:
        logger.debug(f"MCP服务器健康检查失败: {str(e)}")
        return False

async def _ensure_mcp_server_ready():
    """确保MCP服务器已启动并可用"""
    try:
        # 检查MCP服务器是否可用
        if not await _check_mcp_server_health():
            logger.info("🔧 MCP服务器不可用，正在等待...")
            # 等待MCP服务器启动
            max_retries = 30
            retry_count = 0
            
            while not await _check_mcp_server_health() and retry_count < max_retries:
                await asyncio.sleep(1)
                retry_count += 1
                if retry_count % 5 == 0:
                    logger.info(f"⏳ 等待MCP服务器可用... ({retry_count}/{max_retries})")
            
            if not await _check_mcp_server_health():
                logger.warning("⚠️ MCP服务器未在预期时间内可用，将以降级模式启动")
                return False
        
        logger.info("✅ MCP服务器已可用")
        logger.info(f"📡 连接到MCP服务器: {MCP_SERVER_URL}")
        
        # 列出可用工具
        try:
            from mcp import ClientSession
            from mcp.client.streamable_http import streamablehttp_client
            
            async with streamablehttp_client(
                url=MCP_SERVER_URL,
                headers=None,
                timeout=timedelta(seconds=10),
                sse_read_timeout=timedelta(seconds=30)
            ) as (read_stream, write_stream, get_session_id):
                
                async with ClientSession(read_stream, write_stream) as session:
                    await session.initialize()
                    response = await session.list_tools()
                    tools = response.tools
                    tool_names = [tool.name for tool in tools]
                    logger.info(f"📊 可用MCP工具: {tool_names}")
                    
        except Exception as e:
            logger.warning(f"获取工具列表失败: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MCP服务器检查失败: {str(e)}")
        logger.warning("⚠️ 将以降级模式启动（某些功能可能不可用）")
        return False

# 懒加载工作流模块
_workflows_loaded = False

def _ensure_workflows_loaded():
    """确保工作流模块已加载"""
    global _workflows_loaded
    if not _workflows_loaded:
        try:
            from . import workflows
            _workflows_loaded = True
            logger.info("✅ 工作流模块已懒加载")
        except Exception as e:
            logger.warning(f"⚠️ 工作流加载失败，将使用基本功能: {str(e)}")

# 工具模块懒加载
_tools_loaded = False

def _ensure_tools_loaded():
    """确保工具模块已加载"""
    global _tools_loaded
    if not _tools_loaded:
        try:
            # 现在使用MCP客户端模式，不需要导入旧的工具模块
            # 工具通过MCP协议调用，而不是直接导入
            logger.info("✅ MCP客户端模式：工具通过MCP协议调用")
            _tools_loaded = True
        except Exception as e:
            logger.warning(f"⚠️ 工具加载失败: {str(e)}")

# 对外暴露的服务接口
__all__ = [
    '_call_mcp_tool',
    '_check_mcp_server_health'
]

def get_module_info() -> dict:
    """获取模块信息"""
    return {
        "name": "情绪分析控制器",
        "version": "3.2",
        "architecture": "官方MCP客户端(简化版)",
        "components": {
            "workflows": "工作流定义模块（懒加载）",
            "websocket": "WebSocket服务器模块", 
            "handlers": "消息处理器模块",
            "utils": "工具函数模块",
            "mcp_client": "官方MCP客户端模块（简化版）"
        },
        "services": [
            "emotion_analysis_workflow",
            "emotional_support_workflow"
        ],
        "websocket_types": [
            "analyze_text",
            "generate_support", 
            "ping"
        ],
        "mcp_integration": {
            "mode": "official_mcp_client_simplified",
            "server_url": MCP_SERVER_URL,
            "transport": "streamable-http",
            "library": "mcp",
            "connection_strategy": "per_call",
            "supported_tools": ["annotate_relationship_text", "generate_comforting_response"]
        }
    }

# 保留独立运行的功能，但建议使用server_start.py
if __name__ == "__main__":
    print("🚀 建议使用 'python controllers/love_analysis/emotion/server_start.py' 来启动完整的服务架构")
    print("📝 如果只需要WebSocket服务器，可以继续...")
    print("🔧 当前使用模块化架构v2.0")
    
    import time
    time.sleep(2)  # 给用户时间看到提示
    
    # 显示模块信息
    info = get_module_info()
    print(f"\n📦 {info['name']} v{info['version']}")
    print("🏗️  架构组件:")
    for component, desc in info['components'].items():
        print(f"   • {component}: {desc}")
    
    # 告诉用户使用正确的启动脚本
    print("\n⚠️ 请使用以下命令启动服务器：")
    print("python controllers/love_analysis/emotion/server_start.py")

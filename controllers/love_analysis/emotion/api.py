"""
情绪分析服务API模块

提供用户认证、验证、登录等功能
支持访客用户和微信用户两种模式
集成到emotion服务中
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field, validator

from .database import get_database
from database.models.user import User
from database.models.user_record import UserRecord
from database.models.user_record_has_analysis import UserRecordHasAnalysis

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器 - 统一在emotion服务下
emotion_api_router = APIRouter(prefix="/emotion/auth", tags=["情绪分析-认证"])

class GuestRegisterRequest(BaseModel):
    """访客注册请求模型"""
    user_id: str = Field(..., min_length=1, max_length=255, description="前端生成的用户ID")
    nickname: str = Field("访客用户", max_length=100, description="用户昵称")
    
    @validator('user_id')
    def validate_user_id(cls, v):
        """验证用户ID格式"""
        if not v or not v.strip():
            raise ValueError('用户ID不能为空')
        return v.strip()

class UserResponse(BaseModel):
    """用户响应模型"""
    user_id: str
    nickname: str
    user_type: str
    status: str
    created_at: str
    last_login_at: Optional[str] = None

class UserValidateRequest(BaseModel):
    """用户验证请求模型"""
    user_id: str = Field(..., min_length=1, max_length=255, description="用户ID")

class UserStoreInputRequest(BaseModel):
    """用户输入存储请求模型"""
    user_id: str = Field(..., min_length=1, max_length=255, description="用户ID")
    user_text: str = Field(..., min_length=1, description="用户输入的文本内容")
    mcp_call_ids: List[str] = Field(default=[], description="MCP工具调用ID列表")
    emotion_analysis_ids: List[str] = Field(default=[], description="情绪分析ID列表")
    tags: Optional[List[str]] = Field(default=None, description="可选的标签列表")
    
    @validator('user_id', 'user_text')
    def validate_required_fields(cls, v):
        """验证必填字段"""
        if not v or not str(v).strip():
            raise ValueError('该字段不能为空')
        return str(v).strip()
    
    @validator('mcp_call_ids', 'emotion_analysis_ids')
    def validate_id_arrays(cls, v):
        """验证ID数组"""
        if v is None:
            return []
        # 过滤空字符串和None值
        return [str(item).strip() for item in v if item and str(item).strip()]
    
    def validate(self):
        """自定义验证：这里可以添加其他业务逻辑验证，但不强制要求ID数组"""
        # 移除强制校验，允许存储纯文本内容
        pass

class UserStoreInputResponse(BaseModel):
    """用户输入存储响应模型"""
    user_record_id: str
    user_id: str
    content: str
    content_type: str
    tags: Optional[List[str]]
    associations: List[Dict[str, Any]]
    created_at: str

class UserRecordQueryRequest(BaseModel):
    """用户记录查询请求模型"""
    user_id: Optional[str] = Field(default=None, description="用户ID（可选，不传则查询所有用户）")
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=10, ge=1, le=100, description="每页大小，1-100")
    include_associations: bool = Field(default=True, description="是否包含关联信息")
    content_type: Optional[str] = Field(default=None, description="内容类型过滤")
    
class UserRecordItem(BaseModel):
    """用户记录项响应模型"""
    user_record_id: str
    user_id: str
    content: str
    content_type: str
    tags: Optional[List[str]]
    associations: Optional[List[Dict[str, Any]]] = None
    created_at: str
    updated_at: str

class UserRecordQueryResponse(BaseModel):
    """用户记录查询响应模型"""
    records: List[UserRecordItem]
    pagination: Dict[str, Any]
    total_count: int

@emotion_api_router.post("/register/guest", response_model=UserResponse, summary="注册访客用户")
async def register_guest_user(request: GuestRegisterRequest):
    """
    注册访客用户
    
    前端生成用户ID并传递给后端，后端将其保存到数据库
    
    Args:
        request: 注册请求数据
        
    Returns:
        UserResponse: 用户信息
        
    Raises:
        HTTPException: 用户ID已存在或其他错误
    """
    try:
        db = get_database()
        
        async with db.get_session() as session:
            # 检查用户ID是否已存在
            existing_user = await User.find_by_id(session, request.user_id)
            if existing_user:
                raise HTTPException(
                    status_code=400,
                    detail=f"用户ID '{request.user_id}' 已存在"
                )
            
            # 创建访客用户
            new_user = await User.create_guest_user(
                session=session,
                user_id=request.user_id,
                nickname=request.nickname
            )
            
            logger.info(f"成功注册访客用户: {request.user_id}")
            
            return UserResponse(
                user_id=new_user.id,
                nickname=new_user.nickname,
                user_type=new_user.user_type.value,
                status=new_user.status.value,
                created_at=new_user.created_at.isoformat(),
                last_login_at=new_user.last_login_at.isoformat() if new_user.last_login_at else None
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册访客用户失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"注册失败: {str(e)}"
        )

@emotion_api_router.post("/validate", response_model=UserResponse, summary="验证用户")
async def validate_user(request: UserValidateRequest):
    """
    验证用户是否存在
    
    WebSocket连接前调用此接口验证用户ID的有效性
    
    Args:
        request: 验证请求数据
        
    Returns:
        UserResponse: 用户信息
        
    Raises:
        HTTPException: 用户不存在或其他错误
    """
    try:
        db = get_database()
        
        async with db.get_session() as session:
            # 查找用户
            user = await User.find_by_id(session, request.user_id)
            
            if not user:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户ID '{request.user_id}' 不存在"
                )
            
            if not user.is_active():
                raise HTTPException(
                    status_code=403,
                    detail=f"用户 '{request.user_id}' 状态异常"
                )
            
            # 更新最后登录时间
            user.last_login_at = datetime.utcnow()
            await user.save(session)
            
            logger.info(f"用户验证成功: {request.user_id}")
            
            return UserResponse(
                user_id=user.id,
                nickname=user.nickname,
                user_type=user.user_type.value,
                status=user.status.value,
                created_at=user.created_at.isoformat(),
                last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户验证失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"验证失败: {str(e)}"
        )

@emotion_api_router.get("/user/{user_id}", response_model=UserResponse, summary="获取用户信息")
async def get_user_info(user_id: str):
    """
    获取用户信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        UserResponse: 用户信息
        
    Raises:
        HTTPException: 用户不存在或其他错误
    """
    try:
        db = get_database()
        
        async with db.get_session() as session:
            user = await User.find_by_id(session, user_id)
            
            if not user:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户ID '{user_id}' 不存在"
                )
            
            return UserResponse(
                user_id=user.id,
                nickname=user.nickname,
                user_type=user.user_type.value,
                status=user.status.value,
                created_at=user.created_at.isoformat(),
                last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取失败: {str(e)}"
        )

# 其他emotion相关的API可以在这里添加

@emotion_api_router.get("/status", summary="情绪服务状态")
async def get_emotion_service_status():
    """
    获取情绪分析服务状态
    
    Returns:
        服务状态信息
    """
    # 这里可以检查WebSocket服务器状态、数据库连接等
    return {
        "service": "emotion_analysis",
        "status": "running",
        "websocket_server": "active",
        "database": "connected",
        "supported_features": [
            "user_authentication",
            "emotion_analysis",
            "emotional_support",
            "realtime_websocket"
        ]
    }

@emotion_api_router.get("/ws-info", summary="WebSocket连接信息")
async def get_websocket_info():
    """
    获取WebSocket连接信息
    
    Returns:
        WebSocket服务器连接信息
    """
    return {
        "websocket_url": "ws://localhost:8765",
        "protocol": "json",
        "auth_required": True,
        "auth_message_format": {
            "type": "auth",
            "user_id": "your_user_id_here"
        },
        "supported_message_types": [
            "analyze_text",
            "generate_support", 
            "ping"
        ]
    } 

@emotion_api_router.post("/store-input", response_model=UserStoreInputResponse, summary="存储用户输入并创建关联")
async def store_user_input(request: UserStoreInputRequest):
    """
    存储用户输入信息并可选择性创建与分析结果的关联
    
    将用户的文本输入存储到 user_records 表中，
    可选择性地与多个 mcp_tool_calls 和 emotion_analysis 记录建立关联
    即使没有提供任何关联ID，也可以存储纯文本内容
    
    Args:
        request: 存储请求数据
        
    Returns:
        UserStoreInputResponse: 存储结果信息
        
    Raises:
        HTTPException: 用户不存在或其他错误
    """
    try:
        # 1. 不再进行强制校验，允许存储纯文本
        # request.validate()  # 移除这个强制校验
        
        db = get_database()
        
        async with db.get_session() as session:
            # 2. 验证用户是否存在
            user = await User.find_by_id(session, request.user_id)
            if not user:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户ID '{request.user_id}' 不存在"
                )
            
            if not user.is_active():
                raise HTTPException(
                    status_code=403,
                    detail=f"用户 '{request.user_id}' 状态异常"
                )
            
            # 3. 创建用户记录
            user_record = UserRecord(
                user_id=request.user_id,
                content=request.user_text,
                content_type="text",
                tags=request.tags or []
            )
            
            # 验证和保存用户记录
            user_record.validate()
            await user_record.save(session)
            
            logger.info(f"创建用户记录: {user_record.id} for 用户: {request.user_id}")
            
            # 4. 批量创建关联记录
            associations = []
            mcp_success_count = 0
            emotion_success_count = 0
            
            # 4.1 批量创建MCP关联
            if request.mcp_call_ids:
                for mcp_call_id in request.mcp_call_ids:
                    try:
                        mcp_association = await UserRecordHasAnalysis.create_mcp_association(
                            db_session=session,
                            user_record_id=user_record.id,
                            mcp_tool_call_id=mcp_call_id,
                            analysis_type="mcp_call"
                        )
                        associations.append({
                            "id": str(mcp_association.id),  # 转换UUID为字符串
                            "type": mcp_association.type,
                            "mcp_tool_call_id": mcp_call_id,
                            "emotion_analysis_id": None,
                            "created_at": mcp_association.created_at.isoformat() if mcp_association.created_at else None
                        })
                        mcp_success_count += 1
                        logger.info(f"创建MCP关联: {mcp_association.id} -> {mcp_call_id}")
                        
                    except Exception as e:
                        logger.warning(f"创建MCP关联失败 (ID: {mcp_call_id}): {str(e)}")
                        continue
            
            # 4.2 批量创建情绪分析关联
            if request.emotion_analysis_ids:
                for emotion_analysis_id in request.emotion_analysis_ids:
                    try:
                        emotion_association = await UserRecordHasAnalysis.create_emotion_association(
                            db_session=session,
                            user_record_id=user_record.id,
                            emotion_analysis_id=emotion_analysis_id,
                            analysis_type="emotion_analysis"
                        )
                        associations.append({
                            "id": str(emotion_association.id),  # 转换UUID为字符串
                            "type": emotion_association.type,
                            "mcp_tool_call_id": None,
                            "emotion_analysis_id": emotion_analysis_id,
                            "created_at": emotion_association.created_at.isoformat() if emotion_association.created_at else None
                        })
                        emotion_success_count += 1
                        logger.info(f"创建情绪分析关联: {emotion_association.id} -> {emotion_analysis_id}")
                        
                    except Exception as e:
                        logger.warning(f"创建情绪分析关联失败 (ID: {emotion_analysis_id}): {str(e)}")
                        continue
            
            # 5. 检查是否至少有一个关联成功（现在不强制要求关联）
            # 即使没有关联也允许保存用户记录
            if not associations:
                logger.info("用户记录已保存，但没有创建任何关联")
            
            # 6. 记录成功统计
            total_requested = len(request.mcp_call_ids) + len(request.emotion_analysis_ids)
            total_success = len(associations)
            
            logger.info(f"用户输入存储完成，共创建 {total_success}/{total_requested} 个关联 (MCP: {mcp_success_count}/{len(request.mcp_call_ids)}, 情绪分析: {emotion_success_count}/{len(request.emotion_analysis_ids)})")
            
            return UserStoreInputResponse(
                user_record_id=str(user_record.id),  # 转换UUID为字符串
                user_id=user_record.user_id,
                content=user_record.content,
                content_type=user_record.content_type,
                tags=user_record.tags,
                associations=associations,
                created_at=user_record.created_at.isoformat() if user_record.created_at else datetime.utcnow().isoformat()
            )
            
    except HTTPException:
        raise
    except ValueError as e:
        # 处理请求验证错误
        raise HTTPException(
            status_code=400,
            detail=f"请求参数错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"存储用户输入失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"存储失败: {str(e)}"
        )

@emotion_api_router.get("/user-records", response_model=UserRecordQueryResponse, summary="分页查询用户记录")
async def query_user_records(
    user_id: Optional[str] = None,
    page: int = 1,
    page_size: int = 10,
    include_associations: bool = True,
    content_type: Optional[str] = None
):
    """
    分页查询用户记录，按时间线排序
    
    支持多种过滤条件和分页功能
    
    Args:
        user_id: 用户ID（可选，不传则查询所有用户记录）
        page: 页码，从1开始
        page_size: 每页大小，1-100
        include_associations: 是否包含关联信息
        content_type: 内容类型过滤（可选）
        
    Returns:
        UserRecordQueryResponse: 分页查询结果
        
    Raises:
        HTTPException: 查询失败或参数错误
    """
    try:
        # 参数验证
        if page < 1:
            raise HTTPException(status_code=400, detail="页码必须大于0")
        if page_size < 1 or page_size > 100:
            raise HTTPException(status_code=400, detail="每页大小必须在1-100之间")
        
        db = get_database()
        
        async with db.get_session() as session:
            # 如果指定了用户ID，先验证用户是否存在
            if user_id:
                user = await User.find_by_id(session, user_id)
                if not user:
                    raise HTTPException(
                        status_code=404,
                        detail=f"用户ID '{user_id}' 不存在"
                    )
            
            # 分页查询用户记录
            records, total_count = await UserRecord.find_with_pagination(
                session=session,
                user_id=user_id,
                content_type=content_type,
                page=page,
                page_size=page_size
            )
            
            logger.info(f"查询到 {len(records)} 条记录，总数: {total_count}")
            
            # 构建响应数据
            record_items = []
            
            # 如果需要包含关联信息，批量查询关联数据
            associations_map = {}
            if include_associations and records:
                record_ids = [record.id for record in records]
                all_associations = await UserRecordHasAnalysis.find_by_user_record_ids(
                    session, record_ids
                )
                
                # 按用户记录ID分组关联信息
                for assoc in all_associations:
                    user_record_id_str = str(assoc.user_record_id)  # 转换UUID为字符串作为键
                    if user_record_id_str not in associations_map:
                        associations_map[user_record_id_str] = []
                    associations_map[user_record_id_str].append({
                        "id": str(assoc.id),  # 转换UUID为字符串
                        "type": assoc.type,
                        "mcp_tool_call_id": assoc.mcp_tool_call_id,
                        "emotion_analysis_id": assoc.mcp_tool_call_id if assoc.type == "emotion_analysis" else None,
                        "created_at": assoc.created_at.isoformat() if assoc.created_at else None
                    })
            
            # 构建记录项
            for record in records:
                record_id_str = str(record.id)  # 转换UUID为字符串作为键
                associations = associations_map.get(record_id_str, []) if include_associations else None
                
                record_items.append(UserRecordItem(
                    user_record_id=record_id_str,
                    user_id=record.user_id,
                    content=record.content,
                    content_type=record.content_type,
                    tags=record.tags,
                    associations=associations,
                    created_at=record.created_at.isoformat() if record.created_at else "",
                    updated_at=record.updated_at.isoformat() if record.updated_at else ""
                ))
            
            # 计算分页信息
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            
            pagination = {
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev,
                "next_page": page + 1 if has_next else None,
                "prev_page": page - 1 if has_prev else None
            }
            
            return UserRecordQueryResponse(
                records=record_items,
                pagination=pagination,
                total_count=total_count
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询用户记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询失败: {str(e)}"
        )

@emotion_api_router.get("/user/record", summary="获取用户记录的内容")
async def get_user_record(user_id: str):
    """
    获取用户记录的内容
    
    Args:
        user_id: 用户ID
        
    Returns:
        UserRecord: 用户记录内容
    """
    try:
        db = get_database()
        
        async with db.get_session() as session:
            # 查找用户
            user = await User.find_by_id(session, user_id)
            
            if not user:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户ID '{user_id}' 不存在"
                )
            
            # 获取用户记录
            user_record = await UserRecord.find_by_user_id(session, user_id)
            
            if not user_record:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户ID '{user_id}' 没有记录"
                )
            
            return user_record
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户记录失败: {str(e)}")
        
# 调用重述tools ，重述用户记录
@emotion_api_router.post("/user/record/rephrase", summary="重述用户记录")
async def rephrase_user_record(user_id: str, record_id: str):
    """
    重述用户记录
    """
    try:
        db = get_database()
        async with db.get_session() as session:
            user_record = await UserRecord.find_by_id(session, record_id)
            if not user_record:
                raise HTTPException(
                    status_code=404,
                    detail=f"用户记录ID '{record_id}' 不存在"
                )
            # 调用重述tools
            rephrase_result = await rephrase_user_record(user_record)
            return rephrase_result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重述用户记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"重述失败: {str(e)}"
        )

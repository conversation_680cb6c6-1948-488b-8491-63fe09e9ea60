"""
WebSocket服务器模块

专门处理WebSocket连接管理和消息路由
支持用户ID验证和会话管理
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional
import websockets
from websockets.server import WebSocketServerProtocol
from websockets.exceptions import ConnectionClosed, WebSocketException

# 导入消息处理器和数据库
from .handlers import MessageHandler
from .database import get_database
from database.models.user import User

# 配置日志
logger = logging.getLogger(__name__)

class EmotionAnalysisWebSocketServer:
    """
    情绪分析WebSocket服务器
    
    功能：
    1. 管理WebSocket连接
    2. 验证用户ID有效性
    3. 路由消息到对应的处理器
    4. 提供统一的消息发送接口
    """
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        """
        初始化WebSocket服务器
        
        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        self.host = host
        self.port = port
        # 存储格式：{session_id: {"websocket": websocket, "user_id": user_id, "user_info": user_info}}
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        self.connection_count = 0
        
        # 初始化消息处理器
        self.message_handler = MessageHandler()
        
    def generate_session_id(self) -> str:
        """
        生成唯一的WebSocket会话ID
        
        Returns:
            唯一的WebSocket会话标识符
        """
        self.connection_count += 1
        return f"conn_{int(time.time())}_{self.connection_count}"
    
    async def validate_user_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        验证用户ID是否存在且有效
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict]: 用户信息，验证失败返回None
        """
        try:
            db = get_database()
            async with db.get_session() as session:
                user = await User.find_by_id(session, user_id)
                
                if not user:
                    logger.warning(f"用户ID不存在: {user_id}")
                    return None
                
                if not user.is_active():
                    logger.warning(f"用户状态异常: {user_id}, 状态: {user.status}")
                    return None
                
                return {
                    "user_id": user.id,
                    "nickname": user.nickname,
                    "user_type": user.user_type.value,
                    "status": user.status.value
                }
                
        except Exception as e:
            logger.error(f"验证用户ID失败: {str(e)}")
            return None
    
    async def register_connection(self, websocket: WebSocketServerProtocol, user_id: str) -> Optional[str]:
        """
        注册新的WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            user_id: 用户ID
            
        Returns:
            Optional[str]: 会话ID，验证失败返回None
        """
        # 验证用户ID
        user_info = await self.validate_user_id(user_id)
        if not user_info:
            return None
        
        session_id = self.generate_session_id()
        self.active_connections[session_id] = {
            "websocket": websocket,
            "user_id": user_id,
            "user_info": user_info
        }
        
        logger.info(f"新连接注册: 会话ID={session_id}, 用户ID={user_id}, 当前活跃连接数: {len(self.active_connections)}")
        
        # 发送连接确认消息
        welcome_message = {
            "type": "connection_established",
            "session_id": session_id,
            "user_id": user_id,
            "user_info": user_info,
            "message": "情绪分析与支持服务已就绪",
            "services": ["emotion_analysis", "emotional_support"],
            "timestamp": time.time()
        }
        await self.send_message(websocket, welcome_message)
        return session_id
    
    async def unregister_connection(self, session_id: str):
        """
        注销WebSocket连接
        
        Args:
            session_id: 会话ID
        """
        if session_id in self.active_connections:
            connection_info = self.active_connections[session_id]
            user_id = connection_info.get("user_id", "unknown")
            del self.active_connections[session_id]
            logger.info(f"连接注销: 会话ID={session_id}, 用户ID={user_id}, 当前活跃连接数: {len(self.active_connections)}")
    
    async def send_message(self, websocket: WebSocketServerProtocol, message: Dict[str, Any]):
        """
        向客户端发送消息
        
        Args:
            websocket: WebSocket连接对象
            message: 要发送的消息字典
        """
        try:
            await websocket.send(json.dumps(message, ensure_ascii=False))
        except ConnectionClosed:
            logger.warning("尝试向已关闭的连接发送消息")
        except Exception as e:
            logger.error(f"发送消息时出错: {str(e)}")

    async def handle_message(self, websocket: WebSocketServerProtocol, message: str, session_id: str):
        """
        处理收到的WebSocket消息，分发到对应的处理器
        
        Args:
            websocket: WebSocket连接对象
            message: 收到的消息字符串
            session_id: 会话ID
        """
        try:
            # 解析JSON消息
            data = json.loads(message)
            message_type = data.get("type", "unknown")
            
            # 获取连接信息
            connection_info = self.active_connections.get(session_id)
            if not connection_info:
                error_message = {
                    "type": "connection_error",
                    "error": "会话信息不存在",
                    "timestamp": time.time()
                }
                await self.send_message(websocket, error_message)
                return
            
            user_id = connection_info["user_id"]
            
            # 根据消息类型分发到对应的处理器
            if message_type == "analyze_text":
                await self.message_handler.handle_analyze_text(
                    data, websocket, session_id, user_id, self.send_message
                )
                
            elif message_type == "generate_support":
                await self.message_handler.handle_generate_support(
                    data, websocket, session_id, user_id, self.send_message
                )
                
            elif message_type == "ping":
                await self.message_handler.handle_ping(
                    websocket, self.send_message
                )
                
            else:
                await self.message_handler.handle_unknown_type(
                    message_type, websocket, self.send_message
                )
                
        except json.JSONDecodeError:
            error_message = {
                "type": "json_parse_error",
                "error": "无法解析JSON消息",
                "timestamp": time.time()
            }
            await self.send_message(websocket, error_message)
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            error_message = {
                "type": "message_handling_error",
                "error": str(e),
                "timestamp": time.time()
            }
            await self.send_message(websocket, error_message)
    
    async def handle_connection(self, websocket: WebSocketServerProtocol):
        """
        处理WebSocket连接的生命周期
        
        Args:
            websocket: WebSocket连接对象
        """
        session_id = None
        client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
        
        try:
            logger.info(f"新的WebSocket连接来自: {client_ip}")
            
            # 等待客户端发送认证消息
            logger.debug(f"等待客户端 {client_ip} 发送认证消息...")
            auth_message = await websocket.recv()
            logger.debug(f"收到认证消息: {auth_message}")
            
            auth_data = json.loads(auth_message)
            
            if auth_data.get("type") != "auth":
                error_message = {
                    "type": "auth_error",
                    "error": "首条消息必须是认证消息",
                    "timestamp": time.time()
                }
                logger.warning(f"客户端 {client_ip} 首条消息不是认证消息: {auth_data}")
                await websocket.send(json.dumps(error_message, ensure_ascii=False))
                return
            
            user_id = auth_data.get("user_id")
            if not user_id:
                error_message = {
                    "type": "auth_error",
                    "error": "缺少用户ID",
                    "timestamp": time.time()
                }
                logger.warning(f"客户端 {client_ip} 认证消息缺少用户ID")
                await websocket.send(json.dumps(error_message, ensure_ascii=False))
                return
            
            logger.info(f"客户端 {client_ip} 尝试使用用户ID认证: {user_id}")
            
            # 注册连接（包含用户ID验证）
            session_id = await self.register_connection(websocket, user_id)
            if not session_id:
                error_message = {
                    "type": "auth_error",
                    "error": f"用户ID验证失败: {user_id}",
                    "timestamp": time.time()
                }
                logger.error(f"客户端 {client_ip} 用户ID验证失败: {user_id}")
                await websocket.send(json.dumps(error_message, ensure_ascii=False))
                return
            
            logger.info(f"客户端 {client_ip} 认证成功，会话ID: {session_id}")
            
            # 处理后续消息
            async for message in websocket:
                await self.handle_message(websocket, message, session_id)
                
        except ConnectionClosed:
            logger.info(f"连接 {session_id or client_ip} 已关闭")
        except WebSocketException as e:
            logger.error(f"WebSocket异常 {session_id or client_ip}: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"客户端 {client_ip} 认证消息JSON解析失败: {str(e)}")
        except asyncio.TimeoutError:
            logger.warning(f"客户端 {client_ip} 认证超时，未发送认证消息")
        except Exception as e:
            logger.error(f"处理连接时出错 {session_id or client_ip}: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            if session_id:
                await self.unregister_connection(session_id)
    
    async def start_server(self):
        """
        启动WebSocket服务器
        
        Returns:
            WebSocket服务器对象
        """
        logger.info(f"启动情绪分析WebSocket服务器: {self.host}:{self.port}")
        
        # 使用正确的websockets 15.x API
        server = await websockets.serve(
            self.handle_connection,  # 直接传递方法
            self.host,
            self.port,
            ping_interval=30,  # 30秒心跳间隔
            ping_timeout=10,   # 10秒心跳超时
        )
        
        logger.info(f"情绪分析服务器已启动，监听 ws://{self.host}:{self.port}")
        return server
    
    def get_server_stats(self) -> Dict[str, Any]:
        """
        获取服务器统计信息
        
        Returns:
            服务器统计信息字典
        """
        return {
            "active_connections": len(self.active_connections),
            "total_connections_created": self.connection_count,
            "host": self.host,
            "port": self.port,
            "supported_message_types": ["analyze_text", "generate_support", "ping"]
        }

# 创建全局服务器实例
emotion_server = EmotionAnalysisWebSocketServer()

async def start_emotion_analysis_server(host: str = "localhost", port: int = 8765):
    """
    启动情绪分析服务器的便捷函数
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
    """
    emotion_server.host = host
    emotion_server.port = port
    
    # 确保在启动服务器之前导入所有必要的模块
    try:
        # 工作流模块采用懒加载，不在启动时强制导入
        # from . import workflows  # 工作流将在需要时导入
        
        # 工具模块也采用懒加载，不在启动时强制导入
        # import tools.annotate_relationship_text
        # import tools.generate_comforting_response
        logger.info("成功初始化服务器，模块将在需要时懒加载")
    except ImportError as e:
        logger.error(f"导入模块失败: {str(e)}")
        raise
    
    server = await emotion_server.start_server()
    
    try:
        await server.wait_closed()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        server.close()
        await server.wait_closed()
        logger.info("服务器已关闭") 
"""
情绪分析数据操作模块

使用新的数据模型提供专门的数据库CRUD操作接口
包含会话管理、分析记录保存、回复记录保存等功能
"""
import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from uuid import uuid4

from .database import get_database, get_models

# 配置日志
logger = logging.getLogger(__name__)

# 获取数据模型
models = get_models()
User = models['User']
McpToolCall = models['McpToolCall']
CallLog = models['CallLog']
EmotionAnalysis = models['EmotionAnalysis']
EmotionSupport = models['EmotionSupport']
UserRecord = models['UserRecord']

class SessionManager:
    """
    用户会话管理器
    
    功能：
    - 创建和管理用户会话（映射到User模型）
    - 会话状态跟踪
    - 会话元数据管理
    """
    
    @staticmethod
    async def create_or_get_session(user_id: str, metadata: Dict[str, Any] = None) -> str:
        """
        创建或获取用户会话
        
        Args:
            user_id: 用户ID（来自数据库users表的真实用户ID）
            metadata: 会话元数据（仅用于日志记录，不保存到数据库）
            
        Returns:
            session_id: 会话ID（直接返回用户ID，因为用户ID就是会话的标识）
        """
        db = get_database()
        
        try:
            async with db.get_session() as session:
                # 1. 验证用户是否存在
                existing_user = await User.find_by_id(session, user_id)
                
                if not existing_user:
                    logger.warning(f"用户ID不存在: {user_id}")
                    # 如果用户不存在，仍然返回用户ID，让调用方处理
                    return user_id
                
                # 2. 更新用户最后登录时间
                        existing_user.last_login_at = datetime.utcnow()
                        await existing_user.save(session)
                logger.info(f"更新用户最后登录时间: {user_id}")
                
                # 3. 记录会话元数据到日志（不保存到UserRecord）
                if metadata:
                    logger.debug(f"会话元数据: {user_id} - {metadata}")
                
                logger.info(f"会话管理成功: 用户ID={user_id}")
                return user_id  # 直接返回用户ID作为会话标识
                
        except Exception as e:
            logger.error(f"会话创建/获取失败: {str(e)}")
            # 发生错误时仍然返回用户ID，让上层处理
            return user_id
    
    @staticmethod
    async def update_session_metadata(session_id: str, metadata: Dict[str, Any]):
        """
        更新会话元数据
        
        Args:
            session_id: 会话ID（应该是UUID格式的用户ID）
            metadata: 新的元数据（仅记录到日志，不保存到数据库）
        """
        try:
            # 仅记录到日志，不保存到数据库
            logger.debug(f"会话元数据更新: {session_id} - {metadata}")
                
        except Exception as e:
            logger.error(f"会话元数据更新失败: {str(e)}")
    
    @staticmethod
    async def close_session(session_id: str):
        """
        关闭会话
        
        Args:
            session_id: 会话ID（应该是UUID格式的用户ID）
        """
        try:
            # 仅记录到日志，不在数据库中保存会话关闭事件
                logger.info(f"会话已关闭: {session_id}")
                
        except Exception as e:
            logger.error(f"会话关闭失败: {str(e)}")

class AnalysisRecordManager:
    """
    情绪分析记录管理器
    
    功能：
    - 保存情绪分析结果（使用EmotionAnalysis模型）
    - 记录MCP工具调用信息
    - 处理时间统计
    """
    
    @staticmethod
    async def save_analysis_record(
        session_id: str,
        user_id: str, 
        user_text: str,
        workflow_type: str,
        analysis_results: Dict[str, Any],
        processing_time_ms: int,
        mcp_tool_calls: List[Dict[str, Any]] = None,
        success: bool = True,
        error_message: str = None
    ) -> Optional[str]:
        """
        保存MCP工具调用记录
        
        注意：此函数只负责保存MCP工具调用的技术记录
        真正的业务层面情绪分析记录应该在handlers.py中保存
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            user_text: 用户输入文本
            workflow_type: 工作流类型
            analysis_results: MCP工具返回的原始结果
            processing_time_ms: 处理时间（毫秒）
            mcp_tool_calls: MCP工具调用详情列表
            success: 是否成功
            error_message: 错误消息
            
        Returns:
            Optional[str]: MCP工具调用记录的UUID，失败时返回None
        """
        db = get_database()
        
        if not db:
            logger.error("数据库未初始化")
            return None
        
        try:
            async with db.get_session() as db_session:
                # 使用session_id作为实际用户ID（已经是UUID格式）
                actual_user_id = session_id
                
                # 只创建MCP工具调用记录
                mcp_call_id = None
                if mcp_tool_calls:
                    mcp_call = McpToolCall(
                        tool_name=workflow_type,
                        user_id=actual_user_id,  # 使用UUID格式的用户ID
                        input_data={'user_text': user_text},
                        output_data=analysis_results,
                        status='success' if success else 'failed',
                        error_message=error_message,
                        execution_time_ms=processing_time_ms
                    )
                    await mcp_call.save(db_session)
                    mcp_call_id = mcp_call.id
                
                # 如果有MCP工具调用，也记录到调用日志
                if mcp_tool_calls and mcp_call_id:
                    for call_info in mcp_tool_calls:
                        call_log = CallLog(
                            mcp_tool_call_id=mcp_call_id,
                            log_level='info' if success else 'error',
                            message=call_info.get('message', f'工具调用: {workflow_type}'),
                            metadata=call_info
                        )
                        await call_log.save(db_session)
                
                # 返回真正的MCP工具调用记录UUID
                if mcp_call_id:
                    logger.info(f"保存MCP工具调用记录: {mcp_call_id}")
                    return mcp_call_id  # 返回UUID字符串用于业务记录关联
                else:
                    logger.warning("没有MCP工具调用需要保存")
                    return None
                
        except Exception as e:
            logger.error(f"保存MCP工具调用记录失败: {str(e)}")
            return None  # 返回None表示失败
    
    @staticmethod
    async def get_recent_analysis_records(
        session_id: str, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取最近的分析记录
        
        Args:
            session_id: 会话ID
            limit: 返回记录数量限制
            
        Returns:
            分析记录列表
        """
        db = get_database()
        
        try:
            async with db.get_session() as db_session:
                # 查询用户的情绪分析记录
                query = """
                    SELECT id, user_text_input, threshold_results, 
                           created_at, analysis_version
                    FROM emotion_analysis 
                    WHERE user_id = $1 
                    ORDER BY created_at DESC 
                    LIMIT $2
                """
                
                records = await db_session.fetch(query, session_id, limit)
                
                result = []
                for record in records:
                    result.append({
                        'id': hash(record['id']) % (10**9),  # 兼容整型ID
                        'user_text': record['user_text_input'],
                        'workflow_type': 'emotion_analysis',
                        'analysis_results': record['threshold_results'][0] if record['threshold_results'] else {},
                        'processing_time_ms': None,  # 这个信息在MCP调用记录中
                        'success': True,
                        'created_at': record['created_at']
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"获取分析记录失败: {str(e)}")
            return []

class ResponseRecordManager:
    """
    回复记录管理器
    
    功能：
    - 保存生成的回复（使用EmotionSupport模型）
    - 关联分析记录
    - 回复质量跟踪
    """
    
    @staticmethod
    async def save_response_record(
        analysis_record_id: int,
        session_id: str,
        user_id: str,
        response_type: str,
        generated_response: str,
        input_context: str = None,
        detected_emotion: str = None,
        generation_params: Dict[str, Any] = None,
        processing_time_ms: int = None,
        mcp_tool_calls: List[Dict[str, Any]] = None,
        success: bool = True,
        error_message: str = None
    ) -> Optional[str]:
        """
        保存MCP工具调用记录（回复生成相关）
        
        注意：此函数只负责保存MCP工具调用的技术记录
        真正的业务层面情绪支持记录应该在handlers.py中保存
        
        Args:
            analysis_record_id: 关联的分析记录ID
            session_id: 会话ID
            user_id: 用户ID
            response_type: 回复类型
            generated_response: 生成的回复
            input_context: 输入上下文
            detected_emotion: 检测到的情绪
            generation_params: 生成参数
            processing_time_ms: 处理时间（毫秒）
            mcp_tool_calls: MCP工具调用详情列表
            success: 是否成功
            error_message: 错误信息
            
        Returns:
            Optional[str]: MCP工具调用记录的UUID，失败时返回None
        """
        db = get_database()
        
        if not db:
            logger.error("数据库未初始化")
            return None
        
        try:
            async with db.get_session() as db_session:
                # 使用session_id作为实际用户ID（已经是UUID格式）
                actual_user_id = session_id
                
                # 只创建MCP工具调用记录
                mcp_call_id = None
                if mcp_tool_calls:
                    mcp_call = McpToolCall(
                        tool_name=f"response_generation_{response_type}",
                        user_id=actual_user_id,  # 使用UUID格式的用户ID
                        input_data={
                            'context': input_context,
                            'emotion': detected_emotion,
                            'params': generation_params or {},
                            'analysis_record_id': analysis_record_id
                        },
                        output_data={'response': generated_response},
                        status='success' if success else 'failed',
                        error_message=error_message,
                        execution_time_ms=processing_time_ms
                    )
                    await mcp_call.save(db_session)
                    mcp_call_id = mcp_call.id
                
                # 记录调用日志
                if mcp_tool_calls and mcp_call_id:
                    for call_info in mcp_tool_calls:
                        call_log = CallLog(
                            mcp_tool_call_id=mcp_call_id,
                            log_level='info' if success else 'error',
                            message=call_info.get('message', f'回复生成: {response_type}'),
                            metadata=call_info
                        )
                        await call_log.save(db_session)
                
                # 返回MCP工具调用记录UUID
                if mcp_call_id:
                    logger.info(f"保存MCP工具调用记录(回复生成): {mcp_call_id}")
                    return mcp_call_id
                else:
                    logger.warning("没有MCP工具调用需要保存")
                    return None
                
        except Exception as e:
            logger.error(f"保存MCP工具调用记录(回复生成)失败: {str(e)}")
            return None  # 返回None表示失败

class WorkflowLogger:
    """
    工作流日志管理器
    
    功能：
    - 记录工作流执行步骤（使用CallLog模型）
    - 性能监控
    - 错误追踪
    """
    
    @staticmethod
    async def log_workflow_step(
        session_id: str,
        user_id: str,
        workflow_type: str,
        workflow_status: str,
        step_name: str = None,
        step_status: str = None,
        processing_time_ms: int = None,
        input_data: Dict[str, Any] = None,
        output_data: Dict[str, Any] = None,
        error_message: str = None,
        metadata: Dict[str, Any] = None
    ) -> int:
        """
        记录工作流执行步骤
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            workflow_type: 工作流类型
            workflow_status: 工作流状态
            step_name: 步骤名称
            step_status: 步骤状态
            processing_time_ms: 处理时间(毫秒)
            input_data: 输入数据
            output_data: 输出数据
            error_message: 错误信息
            metadata: 元数据
            
        Returns:
            log_id: 日志ID
        """
        db = get_database()
        
        try:
            async with db.get_session() as db_session:
                # 创建MCP工具调用记录（用于追踪工作流）
                mcp_call = McpToolCall(
                    tool_name=f"workflow_{workflow_type}",
                    user_id=user_id,
                    input_data=input_data or {},
                    output_data=output_data or {},
                    status='success' if workflow_status == 'completed' else 'pending',
                    error_message=error_message,
                    execution_time_ms=processing_time_ms
                )
                await mcp_call.save(db_session)
                
                # 创建详细的调用日志
                log_level = 'error' if error_message else ('info' if step_status == 'completed' else 'debug')
                
                call_log = CallLog(
                    mcp_tool_call_id=mcp_call.id,
                    log_level=log_level,
                    message=f"工作流步骤: {step_name or workflow_type} - 状态: {step_status or workflow_status}",
                    metadata={
                        'workflow_type': workflow_type,
                        'workflow_status': workflow_status,
                        'step_name': step_name,
                        'step_status': step_status,
                        'session_id': session_id,
                        **(metadata or {})
                    }
                )
                await call_log.save(db_session)
                
                # 返回日志ID的哈希值
                log_hash = hash(call_log.id) % (10**9)
                
                logger.debug(f"工作流日志记录: {call_log.id}, 类型: {workflow_type}")
                return log_hash
                
        except Exception as e:
            logger.error(f"工作流日志记录失败: {str(e)}")
            return -1

# 对外暴露的接口
__all__ = [
    'SessionManager',
    'AnalysisRecordManager', 
    'ResponseRecordManager',
    'WorkflowLogger'
] 
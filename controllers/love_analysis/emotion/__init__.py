"""
情绪分析模块

提供完整的情绪检测、分析和支持功能
包括WebSocket服务器、工作流管理和消息处理
"""

# 导入主要组件
from .analysis import get_module_info
from .websocket import emotion_server
# 工作流和工具类采用懒加载，避免启动时的MCP服务器依赖
# from .workflows import EmotionAnalysisWorkflow, EmotionalSupportWorkflow
# from .utils import EmotionAnalysisUtils, EmotionalSupportUtils

# 模块信息
__version__ = "2.0.0"
__author__ = "Relationship Analysis System"

# 对外暴露的主要接口
__all__ = [
    # 服务器启动
    'emotion_server',
    
    # 工作流（懒加载）
    # 'EmotionAnalysisWorkflow',
    # 'EmotionalSupportWorkflow',
    
    # 工具类（懒加载）
    # 'EmotionAnalysisUtils', 
    # 'EmotionalSupportUtils',
    
    # 模块信息
    'get_module_info'
]

def get_emotion_module_info():
    """获取情绪分析模块信息"""
    return {
        "name": "emotion_analysis_module",
        "version": __version__,
        "description": "情绪检测、分析和支持的完整解决方案",
        "components": {
            "analysis": "主要分析服务和入口点",
            "websocket": "WebSocket服务器实现",
            "workflows": "LangGraph工作流定义（懒加载）",
            "handlers": "消息处理器",
            "utils": "工具函数和辅助类（懒加载）",
            "server_start": "独立服务器启动脚本"
        },
        "features": [
            "实时情绪检测",
            "智能情绪支持",
            "情绪强度监控",
            "WebSocket实时通信",
            "LangGraph工作流编排"
        ]
    } 
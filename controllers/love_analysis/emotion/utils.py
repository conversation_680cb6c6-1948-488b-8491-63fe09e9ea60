"""
情绪分析工具函数模块

包含情绪分析相关的工具函数和计算逻辑
"""
import logging
from typing import Dict, List, Any

# 配置日志
logger = logging.getLogger(__name__)

def process_emotion_analysis_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    处理和汇总情绪分析结果
    
    Args:
        results: annotate_relationship_text工具返回的原始结果
        
    Returns:
        处理后的结构化结果
    """
    if not results:
        return {
            "segments": [],
            "summary": {
                "total_segments": 0,
                "dominant_emotion": "neutral",
                "average_emotions": {}
            }
        }
    
    # 计算所有情绪的平均分数
    emotion_totals = {}
    emotion_counts = {}
    
    for result in results:
        emotions = result.get("emotions", {})
        for emotion, data in emotions.items():
            score = data.get("score", 0.0)
            if emotion not in emotion_totals:
                emotion_totals[emotion] = 0.0
                emotion_counts[emotion] = 0
            emotion_totals[emotion] += score
            emotion_counts[emotion] += 1
    
    # 计算平均情绪分数
    average_emotions = {}
    for emotion in emotion_totals:
        if emotion_counts[emotion] > 0:
            average_emotions[emotion] = round(emotion_totals[emotion] / emotion_counts[emotion], 3)
    
    # 找出主导情绪
    dominant_emotion = "neutral"
    if average_emotions:
        dominant_emotion = max(average_emotions.keys(), key=lambda k: average_emotions[k])
    
    # 计算整体置信度
    confidence_level = calculate_overall_confidence(results)
    
    return {
        "segments": results,
        "summary": {
            "total_segments": len(results),
            "dominant_emotion": dominant_emotion,
            "average_emotions": average_emotions,
            "confidence_level": confidence_level
        }
    }

def calculate_overall_confidence(results: List[Dict[str, Any]]) -> float:
    """
    计算整体分析的置信度
    
    Args:
        results: 分析结果列表
        
    Returns:
        整体置信度 (0-1)
    """
    if not results:
        return 0.0
    
    total_confidence = 0.0
    count = 0
    
    for result in results:
        emotions = result.get("emotions", {})
        for emotion, data in emotions.items():
            confidence_interval = data.get("confidence_interval", [0, 0])
            if len(confidence_interval) == 2:
                # 置信区间越窄，置信度越高
                interval_width = confidence_interval[1] - confidence_interval[0]
                confidence = 1.0 - min(interval_width, 1.0)
                total_confidence += confidence
                count += 1
    
    return round(total_confidence / max(count, 1), 3)

def calculate_confidence_width(confidence_interval: List[float]) -> float:
    """
    计算置信区间宽度
    
    Args:
        confidence_interval: 置信区间 [下限, 上限]
        
    Returns:
        置信区间宽度
    """
    if len(confidence_interval) != 2:
        return 1.0  # 默认最大宽度
    return abs(confidence_interval[1] - confidence_interval[0])

def calculate_intensity_level(score: float, intensity_thresholds: Dict[str, float]) -> str:
    """
    根据得分计算强度等级
    
    Args:
        score: 情绪得分
        intensity_thresholds: 强度阈值配置
        
    Returns:
        强度等级字符串
    """
    if score >= intensity_thresholds.get("extreme", 0.9):
        return "extreme"
    elif score >= intensity_thresholds.get("high", 0.8):
        return "high"
    elif score >= intensity_thresholds.get("moderate", 0.6):
        return "moderate"
    elif score >= intensity_thresholds.get("low", 0.3):
        return "low"
    else:
        return "minimal"

def calculate_confidence_level(confidence_width: float, confidence_thresholds: Dict[str, float]) -> str:
    """
    根据置信区间宽度计算置信度等级
    
    Args:
        confidence_width: 置信区间宽度
        confidence_thresholds: 置信度阈值配置
        
    Returns:
        置信度等级字符串
    """
    if confidence_width <= confidence_thresholds.get("low_confidence", 0.3):
        return "high"
    elif confidence_width <= confidence_thresholds.get("moderate_confidence", 0.6):
        return "moderate"
    else:
        return "low"

def determine_overall_intensity_and_risk(
    high_intensity_count: int,
    extreme_intensity_count: int
) -> tuple[str, str]:
    """
    确定整体强度等级和风险等级
    
    Args:
        high_intensity_count: 高强度情绪数量
        extreme_intensity_count: 极端强度情绪数量
        
    Returns:
        (整体强度等级, 风险等级)
    """
    if extreme_intensity_count >= 2:
        overall_intensity = "extreme"
        risk_level = "high"
    elif high_intensity_count >= 3 or extreme_intensity_count >= 1:
        overall_intensity = "high"
        risk_level = "moderate"
    elif high_intensity_count >= 1:
        overall_intensity = "moderate"
        risk_level = "low"
    else:
        overall_intensity = "low"
        risk_level = "minimal"
    
    return overall_intensity, risk_level

def is_emotion_outlier(
    intensity_level: str,
    confidence_level: str,
    outlier_criteria: Dict[str, Any] = None
) -> tuple[bool, str]:
    """
    判断情绪是否为异常值
    
    Args:
        intensity_level: 强度等级
        confidence_level: 置信度等级
        outlier_criteria: 异常判断标准（可选）
        
    Returns:
        (是否异常, 异常原因)
    """
    # 默认异常判断标准
    if outlier_criteria is None:
        outlier_criteria = {
            "min_intensity": ["high", "extreme"],
            "min_confidence": "high"
        }
    
    is_outlier = (
        intensity_level in outlier_criteria.get("min_intensity", []) and
        confidence_level == outlier_criteria.get("min_confidence", "high")
    )
    
    if is_outlier:
        reason = f"情绪达到{intensity_level}强度且置信度很高"
        return True, reason
    
    return False, ""

def generate_analysis_summary(
    outlier_count: int,
    overall_intensity: str,
    risk_level: str
) -> str:
    """
    生成分析摘要
    
    Args:
        outlier_count: 异常情绪数量
        overall_intensity: 整体强度等级
        risk_level: 风险等级
        
    Returns:
        分析摘要字符串
    """
    summary_parts = []
    
    if outlier_count > 0:
        summary_parts.append(f"检测到{outlier_count}个异常情绪")
    
    summary_parts.append(f"整体强度等级: {overall_intensity}")
    summary_parts.append(f"风险等级: {risk_level}")
    
    return " | ".join(summary_parts)

# 预定义的阈值配置
DEFAULT_INTENSITY_THRESHOLDS = {
    "low": 0.3,      # 低强度阈值
    "moderate": 0.6, # 中等强度阈值  
    "high": 0.8,     # 高强度阈值
    "extreme": 0.9   # 极端强度阈值
}

DEFAULT_CONFIDENCE_THRESHOLDS = {
    "low_confidence": 0.3,     # 低置信度
    "moderate_confidence": 0.6, # 中等置信度
    "high_confidence": 0.8     # 高置信度
} 
"""
情绪分析数据库模块

使用新的数据库管理系统，提供向后兼容的接口
支持异步操作和统一的数据模型管理
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

# 导入新的数据库管理模块
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from database import DatabaseManager
from database.models import User, McpToolCall, CallLog, EmotionAnalysis, EmotionSupport, UserRecord
from database.utils.exceptions import DatabaseError

# 配置日志
logger = logging.getLogger(__name__)

class EmotionAnalysisDatabase:
    """
    情绪分析数据库管理器
    
    基于新的数据库管理系统，提供向后兼容的接口
    功能：
    - 统一的数据库连接管理
    - 新数据模型支持
    - 向后兼容的API
    """
    
    def __init__(self, database_url: str = None, min_connections: int = 5, max_connections: int = 20):
        """
        初始化数据库管理器
        
        Args:
            database_url: PostgreSQL连接URL（已废弃，使用环境变量配置）
            min_connections: 最小连接数（已废弃，使用配置文件）
            max_connections: 最大连接数（已废弃，使用配置文件）
        """
        # 使用新的数据库管理器
        self.db_manager = DatabaseManager()
        self._initialized = False
        
        if database_url:
            logger.warning("database_url参数已废弃，请在.env文件中配置数据库连接信息")
        
        logger.info("初始化情绪分析数据库管理器（基于新架构）")
    
    async def initialize(self):
        """初始化数据库连接池和表结构"""
        try:
            if self._initialized:
                logger.info("数据库已初始化，跳过重复初始化")
                return
            
            # 初始化新的数据库管理器
            await self.db_manager.initialize()
            
            # 执行数据库迁移（确保表结构是最新的）
            # migration_count = await self.db_manager.migrate()
            # if migration_count > 0:
            #     logger.info(f"执行了 {migration_count} 个数据库迁移")
            
            self._initialized = True
            logger.info("✅ 数据库初始化完成（新架构）")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {str(e)}")
            raise DatabaseError("数据库初始化失败", e)
    
    async def close(self):
        """关闭数据库连接池"""
        if self.db_manager and self._initialized:
            await self.db_manager.close()
            self._initialized = False
            logger.info("✅ 数据库连接池已关闭")
    
    @asynccontextmanager
    async def get_connection(self):
        """
        获取数据库连接的异步上下文管理器
        
        注意：这是向后兼容的接口，实际使用新的会话管理器
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化，请先调用 initialize()")
        
        # 使用新的会话管理器
        async with self.db_manager.session_manager.get_session() as session:
            # 包装session以提供向后兼容的接口
            class CompatibilityWrapper:
                def __init__(self, session):
                    self.session = session
                
                async def execute(self, query, *args):
                    return await self.session.execute(query, *args)
                
                async def fetch(self, query, *args):
                    return await self.session.fetch(query, *args)
                
                async def fetchrow(self, query, *args):
                    return await self.session.fetchrow(query, *args)
                
                async def fetchval(self, query, *args):
                    return await self.session.fetchval(query, *args)
            
            yield CompatibilityWrapper(session)
    
    @asynccontextmanager
    async def get_session(self):
        """
        获取数据库会话（新接口）
        
        返回新的数据库会话对象，支持所有新功能
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化，请先调用 initialize()")
        
        async with self.db_manager.session_manager.get_session() as session:
            yield session
    
    @asynccontextmanager
    async def transaction(self):
        """
        获取事务会话
        
        自动管理事务的开始、提交和回滚
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化，请先调用 initialize()")
        
        async with self.db_manager.session_manager.transaction() as session:
            yield session

# 全局数据库实例
_db_instance: Optional[EmotionAnalysisDatabase] = None

def get_database() -> EmotionAnalysisDatabase:
    """获取数据库实例（单例模式）"""
    global _db_instance
    if _db_instance is None:
        raise RuntimeError("数据库未初始化，请先调用 initialize_database()")
    return _db_instance

async def initialize_database(database_url: str = None, min_connections: int = 5, max_connections: int = 20):
    """
    初始化全局数据库实例
    
    使用新的统一数据库管理模块，提供向后兼容的接口
    
    Args:
        database_url: PostgreSQL连接URL（已废弃，使用环境变量配置）
        min_connections: 最小连接数（已废弃，使用配置文件）
        max_connections: 最大连接数（已废弃，使用配置文件）
    """
    global _db_instance
    
    if _db_instance is not None:
        logger.warning("数据库已经初始化，跳过重复初始化")
        return _db_instance
    
    try:
        logger.info("🔧 初始化emotion模块数据库...")
        
        # 创建并初始化emotion数据库实例（内部会使用新的数据库管理器）
        _db_instance = EmotionAnalysisDatabase()
        await _db_instance.initialize()
        
        logger.info("✅ emotion模块数据库初始化完成")
        return _db_instance
        
    except Exception as e:
        logger.error(f"❌ emotion模块数据库初始化失败: {str(e)}")
        raise e

async def close_database():
    """关闭全局数据库实例"""
    global _db_instance
    
    if _db_instance:
        await _db_instance.close()
        _db_instance = None
        logger.info("✅ 全局数据库实例已关闭")

# 提供数据模型的便捷访问
def get_models():
    """
    获取所有可用的数据模型
    
    Returns:
        dict: 包含所有数据模型的字典
    """
    return {
        'User': User,
        'McpToolCall': McpToolCall,
        'CallLog': CallLog,
        'EmotionAnalysis': EmotionAnalysis,
        'EmotionSupport': EmotionSupport,
        'UserRecord': UserRecord
    }

# 对外暴露的接口
__all__ = [
    'EmotionAnalysisDatabase',
    'get_database',
    'initialize_database',
    'close_database',
    'get_models'
] 
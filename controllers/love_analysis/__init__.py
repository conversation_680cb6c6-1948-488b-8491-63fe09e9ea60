"""
关系分析系统 - 模块化架构

包含情绪分析和智能分析两大核心模块
采用微服务架构设计，支持独立部署和扩展
"""

# 导入子模块
from . import emotion
from . import intelligent

# 模块信息
__version__ = "3.0.0"
__author__ = "Relationship Analysis System"
__architecture__ = "microservices"

# 对外暴露的主要接口
__all__ = [
    'emotion',      # 情绪分析模块
    'intelligent'   # 智能分析模块
]

def get_system_info():
    """获取整个系统的信息"""
    return {
        "name": "relationship_analysis_system",
        "version": __version__,
        "architecture": __architecture__,
        "description": "基于微服务架构的关系分析系统",
        "modules": {
            "emotion": emotion.get_emotion_module_info(),
            "intelligent": intelligent.get_intelligent_module_info()
        },
        "deployment": {
            "process_manager": "PM2",
            "services": [
                {
                    "name": "emotion-analysis-server",
                    "port": 8765,
                    "module": "emotion",
                    "features": ["情绪检测", "安慰回应", "强度监控"]
                },
                {
                    "name": "intelligent-analysis-server", 
                    "port": 8766,
                    "module": "intelligent",
                    "features": ["人设分析", "RAG检索", "综合分析", "数据存储"]
                }
            ]
        },
        "directory_structure": {
            "emotion/": "情绪分析模块",
            "intelligent/": "智能分析模块",
            "intelligent/rag/": "RAG检索子模块",
            "intelligent/storage/": "数据存储子模块",
            "docs/": "文档目录",
            "scripts/": "工具脚本",
            "shared/": "共享组件"
        }
    }

def list_available_services():
    """列出所有可用的服务"""
    return [
        {
            "service": "emotion-analysis",
            "entry_point": "emotion.server_start",
            "description": "情绪分析WebSocket服务器"
        },
        {
            "service": "intelligent-analysis",
            "entry_point": "intelligent.server_start", 
            "description": "智能分析WebSocket服务器"
        }
    ] 
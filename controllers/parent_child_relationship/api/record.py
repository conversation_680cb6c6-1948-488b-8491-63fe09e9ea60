"""
用户输入存储与查询，更新 删除 等操作
亲子关系分析模块的用户记录管理API
提供完整的CRUD操作，支持分页查询和数据验证
"""

from typing import List, Optional, Dict, Any
from fastapi import HTTPException, status
from uuid import uuid4
import logging
import json
from datetime import datetime

# 导入数据库相关模块
from database import DatabaseManager, UserRecord, User, DatabaseError, ValidationError
from database.connection.session_manager import DatabaseSession
from database.models.insight import Insight

# 配置日志
logger = logging.getLogger(__name__)

class UserRecordService:
    """
    用户记录服务类
    
    提供用户记录的完整CRUD操作，包括：
    - 创建记录
    - 查询记录（支持分页）
    - 更新记录
    - 删除记录（软删除）
    - 恢复删除的记录
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化用户记录服务
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
    
    async def _associate_files_with_record(self, session, record_id: str, file_ids: List[str]):
        """
        将文件与记录关联
        
        Args:
            session: 数据库会话
            record_id: 记录ID
            file_ids: 文件ID列表
        """
        if not file_ids:
            return
            
        # 使用原生SQL插入关联记录
        insert_query = """
            INSERT INTO record_files (record_id, file_id)
            SELECT $1, unnest($2::uuid[]) 
            ON CONFLICT (record_id, file_id) DO NOTHING
        """
        await session.execute(insert_query, record_id, file_ids)
    
    async def _update_record_files(self, session, record_id: str, file_ids: List[str]):
        """
        更新记录的文件关联
        
        Args:
            session: 数据库会话
            record_id: 记录ID
            file_ids: 新的文件ID列表
        """
        # 先删除现有的关联
        delete_query = "DELETE FROM record_files WHERE record_id = $1"
        await session.execute(delete_query, record_id)
        
        # 添加新的关联
        if file_ids:
            await self._associate_files_with_record(session, record_id, file_ids)
    
    async def _get_record_files(self, session, record_id: str) -> List[str]:
        """
        获取记录关联的文件ID列表
        
        Args:
            session: 数据库会话
            record_id: 记录ID
            
        Returns:
            List[str]: 文件ID列表
        """
        query = """
            SELECT file_id::text 
            FROM record_files 
            WHERE record_id = $1 
            ORDER BY created_at ASC
        """
        rows = await session.fetch(query, record_id)
        return [row[0] for row in rows]
    
    def _parse_json_field(self, json_field):
        """
        解析JSON字段，处理字符串格式的JSON数据
        
        Args:
            json_field: JSON字段值，可能是字符串或已解析的对象
            
        Returns:
            解析后的Python对象（通常是列表或字典）
        """
        if json_field is None:
            return []
        
        if isinstance(json_field, str):
            try:
                return json.loads(json_field)
            except (json.JSONDecodeError, TypeError):
                logger.warning(f"无法解析JSON字段: {json_field}")
                return []
        
        # 如果已经是Python对象，直接返回
        return json_field
    
    async def create_record(
        self, 
        user_id: str, 
        content: str, 
        content_type: str = "text",
        file_ids: Optional[List[str]] = None
    ) -> UserRecord:
        """
        创建用户记录
        
        Args:
            user_id: 用户ID
            content: 记录内容
            content_type: 内容类型，默认为"text"
            file_ids: 关联的COS文件ID列表，可选
            
        Returns:
            UserRecord: 创建的用户记录
            
        Raises:
            HTTPException: 当用户不存在或创建失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 验证用户是否存在
                user = await User.find_by_id(session, user_id)
                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"用户 {user_id} 不存在"
                    )
                
                # 创建用户记录
                record = UserRecord(
                    id=str(uuid4()),
                    user_id=user_id,
                    content=content,
                    content_type=content_type
                )
                
                # 保存到数据库
                saved_record = await record.save(session)
                
                # 处理文件关联
                if file_ids:
                    await self._associate_files_with_record(session, saved_record.id, file_ids)

                # 获取关联的文件列表
                saved_record.file_storage = await self._get_record_files(session, saved_record.id)

                # 获取包含文件ID的记录
                saved_record.file_ids = file_ids or []
                logger.info(f"创建用户记录成功: {saved_record.id}, 关联文件数量: {len(file_ids or [])}")
                return saved_record
                
        except ValidationError as e:
            logger.error(f"数据验证失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"数据验证失败: {e}"
            )
        except DatabaseError as e:
            logger.error(f"数据库操作失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建记录失败"
            )
    
    async def get_record_by_id(self, record_id: str) -> Dict[str, Any]:
        """
        根据ID获取用户记录（包含关联的洞察信息）
        
        Args:
            record_id: 记录ID
            
        Returns:
            Dict[str, Any]: 用户记录详细信息，包含关联的洞察数据
            
        Raises:
            HTTPException: 当记录不存在时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 关联查询用户记录和洞察信息
                query = """
                    SELECT 
                        ur.id,
                        ur.user_id,
                        ur.content,
                        ur.content_type,
                        ur.is_deleted,
                        ur.created_at,
                        ur.updated_at,
                        -- 关联洞察信息（只查询 status=1 的洞察）
                        COALESCE(
                            json_agg(
                                json_build_object(
                                    'insight_id', i.id,
                                    'content', i.content,
                                    'status', i.status,
                                    'created_at', i.created_at
                                )
                                ORDER BY i.created_at DESC
                            ) FILTER (WHERE i.id IS NOT NULL),
                            '[]'::json
                        ) as insights
                    FROM user_records ur
                    LEFT JOIN insights i ON ur.id::text = i.record_id AND i.status = 1
                    WHERE ur.id = $1
                    GROUP BY ur.id, ur.user_id, ur.content, ur.content_type, 
                             ur.is_deleted, ur.created_at, ur.updated_at
                """
                
                row = await session.fetchrow(query, record_id)
                
                if not row or row["is_deleted"]:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"记录 {record_id} 不存在或已被删除"
                    )
                
                # 获取关联的文件ID
                file_ids = await self._get_record_files(session, record_id)
                
                # 构建返回数据
                record_data = {
                    "id": str(row["id"]),
                    "user_id": str(row["user_id"]),
                    "content": row["content"],
                    "content_type": row["content_type"],
                    "file_ids": file_ids,
                    "is_deleted": row["is_deleted"],
                    "created_at": row["created_at"].isoformat() if row["created_at"] else "",
                    "updated_at": row["updated_at"].isoformat() if row["updated_at"] else "",
                    "insights": self._parse_json_field(row["insights"]) if row["insights"] else []
                }
                
                logger.info(f"获取用户记录成功: {record_id}, 关联洞察数量: {len(record_data['insights'])}")
                return record_data
                
        except DatabaseError as e:
            logger.error(f"查询记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="查询记录失败"
            )
    
    async def get_records_by_user_id(
        self, 
        user_id: str, 
        limit: int = 10, 
        offset: int = 0
    ) -> List[UserRecord]:
        """
        根据用户ID获取用户记录列表
        
        Args:
            user_id: 用户ID
            limit: 限制数量，默认10条
            offset: 偏移量，默认0
            
        Returns:
            List[UserRecord]: 用户记录列表
        """
        try:
            async with self.db_manager.get_session() as session:
                records = await UserRecord.find_all_by_user_id(
                    session, user_id, limit, offset
                )
                return records
                
        except DatabaseError as e:
            logger.error(f"查询用户记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="查询用户记录失败"
            )
    
    async def get_records_with_pagination(
        self,
        user_id: Optional[str] = None,
        content_type: Optional[str] = None,
        file_ids: Optional[List[str]] = None,
        date: Optional[str] = None,
        page: int = 1,
        page_size: int = 10
    ) -> Dict[str, Any]:
        """
        分页查询用户记录，关联查询MCP工具调用结果和COS文件存储信息
        
        Args:
            user_id: 用户ID，可选
            content_type: 内容类型，可选
            date: 日期，可选
            file_ids: 关联文件ID列表，可选
            page: 页码，从1开始
            page_size: 每页大小，默认10条
            
        Returns:
            Dict: 包含记录列表、分页信息和统计信息的字典
            - records: 记录列表，每条记录包含：
              - 基本字段：id, user_id, content, content_type, file_ids, created_at, updated_at
              - analysis_results: 关联的MCP工具调用分析结果
              - file_storage: 关联的COS文件存储信息（包含file_url、file_size等）
            - pagination: 分页信息
            - statistics: 统计信息（文件数量、总大小等）
        """
        try:
            async with self.db_manager.get_session() as session:
                # 构建查询条件
                where_conditions = ["ur.is_deleted = false"]
                params = []
                param_index = 1
                
                logger.info(f"content_type: {content_type}")

                logger.info(f"date: {date}")
                
                # 过滤无效的参数值（前端可能传递 'null', 'undefined' 等字符串）
                def is_valid_param(value):
                    """检查参数是否有效（不是None、空字符串或字符串'null'等）"""
                    if value is None or value == "":
                        return False
                    if isinstance(value, str) and value.lower() in ['null', 'undefined', 'none']:
                        return False
                    return True
                
                if is_valid_param(user_id):
                    where_conditions.append(f"ur.user_id = ${param_index}")
                    params.append(user_id)
                    param_index += 1
                    
                if is_valid_param(content_type):
                    where_conditions.append(f"ur.content_type = ${param_index}")
                    params.append(content_type)
                    param_index += 1
                    
                if is_valid_param(file_ids) and file_ids:
                    where_conditions.append(f"EXISTS (SELECT 1 FROM record_files rf WHERE rf.record_id = ur.id AND rf.file_id = ANY(${param_index}::uuid[]))")
                    params.append(file_ids)
                    param_index += 1

                # 数据库中存储的是datetime类型，传入的date是date字符串，需要转换为datetime对象进行比较
                if is_valid_param(date):
                    try:
                        # 🎯 修复：将字符串转换为datetime对象
                        start_datetime = datetime.fromisoformat(date + " 00:00:00")
                        end_datetime = datetime.fromisoformat(date + " 23:59:59")
                        
                        where_conditions.append(f"ur.created_at >= ${param_index}")
                        params.append(start_datetime)
                        param_index += 1

                        where_conditions.append(f"ur.created_at <= ${param_index}")
                        params.append(end_datetime)
                        param_index += 1
                        
                        logger.info(f"日期过滤范围: {start_datetime} 到 {end_datetime}")
                    except ValueError as e:
                        logger.warning(f"日期格式错误: {date}, 错误: {e}")
                        # 如果日期格式错误，跳过日期过滤条件

                where_clause = " AND ".join(where_conditions)

                logger.info(f"查询条件: {where_clause}")
                logger.info(f"参数值: {params}")
                logger.info(f"参数替换后的逻辑SQL示例: {where_clause.replace('$1', repr(params[0]) if len(params) > 0 else '$1').replace('$2', repr(params[1]) if len(params) > 1 else '$2').replace('$3', repr(params[2]) if len(params) > 2 else '$3')}")
                
                # 查询总数（不重复统计）
                count_query = f"""
                    SELECT COUNT(DISTINCT ur.id) 
                    FROM user_records ur
                    WHERE {where_clause}
                """
                total_count = await session.fetchval(count_query, *params)
                
                # 计算偏移量
                offset = (page - 1) * page_size
                
                # 关联查询用户记录、MCP工具调用结果、COS文件存储信息和洞察信息
                query = f"""
                    SELECT 
                        ur.id,
                        ur.user_id,
                        ur.content,
                        ur.content_type,
                        ur.created_at,
                        ur.updated_at,
                        -- COS文件存储信息（多个文件）
                        COALESCE(
                            json_agg(
                                json_build_object(
                                    'file_id', cf.id,
                                    'file_key', cf.file_key,
                                    'file_info', cf.file_info,
                                    'bucket', cf.bucket,
                                    'region', cf.region,
                                    'file_url', 'https://' || cf.bucket || '.cos.' || cf.region || '.myqcloud.com/' || cf.file_key,
                                    'file_size', COALESCE((cf.file_info->>'size')::int, 0),
                                    'content_type', COALESCE(cf.file_info->>'content_type', 'unknown'),
                                    'file_created_at', cf.created_at
                                )
                                ORDER BY cf.created_at ASC
                            ) FILTER (WHERE cf.id IS NOT NULL),
                            '[]'::json
                        ) as file_storage,
                        COALESCE(
                            json_agg(
                                json_build_object(
                                    'tool_call_id', mt.id,
                                    'tool_name', mt.tool_name,
                                    'output_data', mt.output_data,
                                    'status', mt.status,
                                    'created_at', mt.created_at
                                )
                                ORDER BY mt.created_at DESC
                            ) FILTER (WHERE mt.id IS NOT NULL),
                            '[]'::json
                        ) as analysis_results,
                        -- 洞察信息（只查询 status=1 的洞察）
                        COALESCE(
                            json_agg(
                                json_build_object(
                                    'insight_id', i.id,
                                    'content', i.content,
                                    'status', i.status,
                                    'created_at', i.created_at
                                )
                                ORDER BY i.created_at DESC
                            ) FILTER (WHERE i.id IS NOT NULL),
                            '[]'::json
                        ) as insights
                    FROM user_records ur
                    LEFT JOIN user_record_has_analysis urha ON ur.id = urha.user_record_id
                    LEFT JOIN mcp_tool_calls mt ON urha.mcp_tool_call_id = mt.id
                    LEFT JOIN record_files rf ON ur.id = rf.record_id
                    LEFT JOIN cos_files cf ON rf.file_id = cf.id
                    LEFT JOIN insights i ON ur.id::text = i.record_id AND i.status = 1
                    WHERE {where_clause}
                    GROUP BY ur.id, ur.user_id, ur.content, ur.content_type, ur.created_at, ur.updated_at
                    ORDER BY ur.created_at DESC
                    LIMIT ${param_index} OFFSET ${param_index + 1}
                """
                params.extend([page_size, offset])
                
                rows = await session.fetch(query, *params)
                
                # 转换结果为字典格式
                records = []
                for row in rows:
                    # 处理文件存储信息（现在是数组格式）
                    file_storage = self._parse_json_field(row["file_storage"]) if row["file_storage"] else []
                    
                    # 获取文件ID列表
                    file_ids = [f["file_id"] for f in file_storage] if file_storage else []
                    
                    record_dict = {
                        "id": str(row["id"]),  # 确保 UUID 对象转换为字符串
                        "user_id": str(row["user_id"]),
                        "content": row["content"],
                        "content_type": row["content_type"],
                        "file_ids": file_ids,  # 改为文件ID列表
                        "file_storage": file_storage,  # 现在包含多个文件的完整信息
                        "is_deleted": False,  # 查询条件已过滤删除的记录，所以是False
                        "created_at": row["created_at"].isoformat() if row["created_at"] else "",
                        "updated_at": row["updated_at"].isoformat() if row["updated_at"] else "",
                        "analysis_results": self._parse_json_field(row["analysis_results"]) if row["analysis_results"] else [],
                        "insights": self._parse_json_field(row["insights"]) if row["insights"] else []  # 新增：关联的洞察信息
                    }
                    records.append(record_dict)
                
                # 计算分页信息
                total_pages = (total_count + page_size - 1) // page_size
                has_next = page < total_pages
                has_prev = page > 1
                
                # 统计文件存储信息（处理多个文件的新格式）
                records_with_files = sum(1 for record in records if record.get("file_storage") and len(record["file_storage"]) > 0)
                total_file_size = sum(
                    file["file_size"] 
                    for record in records 
                    if record.get("file_storage")
                    for file in record["file_storage"]
                    if isinstance(file.get("file_size"), (int, float))
                )
                
                logger.info(f"分页查询用户记录成功: 页码={page}, 记录数={len(records)}, 总数={total_count}, 包含文件={records_with_files}, 文件总大小={total_file_size}字节")
                
                return {
                    "records": records,
                    "pagination": {
                        "current_page": page,
                        "page_size": page_size,
                        "total_count": total_count,
                        "total_pages": total_pages,
                        "has_next": has_next,
                        "has_prev": has_prev
                    },
                    "statistics": {
                        "records_with_files": records_with_files,
                        "total_file_size_bytes": total_file_size,
                        "total_file_size_mb": round(total_file_size / (1024 * 1024), 2) if total_file_size > 0 else 0
                    }
                }
                
        except DatabaseError as e:
            logger.error(f"分页查询记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="查询记录失败"
            )
    
    async def update_record(
        self, 
        record_id: str, 
        content: Optional[str] = None,
        content_type: Optional[str] = None,
        file_ids: Optional[List[str]] = None
    ) -> UserRecord:
        """
        更新用户记录
        
        Args:
            record_id: 记录ID
            content: 新的内容，可选
            content_type: 新的内容类型，可选
            file_ids: 新的关联文件ID列表，可选
            
        Returns:
            UserRecord: 更新后的用户记录
            
        Raises:
            HTTPException: 当记录不存在或更新失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 查找记录
                record = await UserRecord.find_by_id(session, record_id)
                if not record or record.is_deleted:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"记录 {record_id} 不存在或已被删除"
                    )
                
                # 更新字段
                if content is not None:
                    record.content = content
                if content_type is not None:
                    record.content_type = content_type
                
                # 处理文件关联更新
                if file_ids is not None:
                    await self._update_record_files(session, record_id, file_ids)
                
                # 更新时间戳
                from database.utils.helpers import get_current_timestamp
                record.updated_at = get_current_timestamp()
                
                # 保存更新
                updated_record = await record.save(session)
                
                # 获取关联的文件ID
                if file_ids is None:
                    file_ids = await self._get_record_files(session, record_id)
                
                updated_record.file_ids = file_ids or []
                logger.info(f"更新用户记录成功: {record_id}, 关联文件数量: {len(file_ids or [])}")
                return updated_record
                
        except ValidationError as e:
            logger.error(f"数据验证失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"数据验证失败: {e}"
            )
        except DatabaseError as e:
            logger.error(f"更新记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新记录失败"
            )
    
    async def delete_record(self, record_id: str) -> Dict[str, str]:
        """
        软删除用户记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            Dict: 删除结果消息
            
        Raises:
            HTTPException: 当记录不存在或删除失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 查找记录
                record = await UserRecord.find_by_id(session, record_id)
                if not record:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"记录 {record_id} 不存在"
                    )
                
                if record.is_deleted:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"记录 {record_id} 已被删除"
                    )
                
                # 执行软删除
                record.soft_delete()
                await record.save(session)
                
                logger.info(f"删除用户记录成功: {record_id}")
                return {"message": f"记录 {record_id} 已成功删除"}
                
        except DatabaseError as e:
            logger.error(f"删除记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除记录失败"
            )
    
    async def restore_record(self, record_id: str) -> UserRecord:
        """
        恢复已删除的用户记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            UserRecord: 恢复的用户记录
            
        Raises:
            HTTPException: 当记录不存在或恢复失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 查找记录（包括已删除的）
                record = await UserRecord.find_by_id(session, record_id)
                if not record:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"记录 {record_id} 不存在"
                    )
                
                if not record.is_deleted:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"记录 {record_id} 未被删除，无需恢复"
                    )
                
                # 恢复记录
                record.restore()
                restored_record = await record.save(session)
                
                logger.info(f"恢复用户记录成功: {record_id}")
                return restored_record
                
        except DatabaseError as e:
            logger.error(f"恢复记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="恢复记录失败"
            )
    
    async def count_user_records(self, user_id: str) -> int:
        """
        统计用户的记录总数
        
        Args:
            user_id: 用户ID
            
        Returns:
            int: 记录总数
        """
        try:
            async with self.db_manager.get_session() as session:
                count = await UserRecord.count_by_user_id(session, user_id)
                return count
                
        except DatabaseError as e:
            logger.error(f"统计用户记录失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="统计记录失败"
            )

# 全局变量，用于存储服务实例
_record_service: Optional[UserRecordService] = None

def get_record_service() -> UserRecordService:
    """
    获取用户记录服务实例
    
    Returns:
        UserRecordService: 用户记录服务实例
        
    Raises:
        RuntimeError: 当服务未初始化时抛出
    """
    global _record_service
    if _record_service is None:
        raise RuntimeError("用户记录服务未初始化")
    return _record_service

def initialize_record_service(db_manager: DatabaseManager):
    """
    初始化用户记录服务
    
    Args:
        db_manager: 数据库管理器实例
    """
    global _record_service
    _record_service = UserRecordService(db_manager)
    logger.info("用户记录服务初始化完成")
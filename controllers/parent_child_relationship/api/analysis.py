"""
调用tools分析，并且存储结果
"""
import logging
import json
from database import DatabaseManager
from database.models.user_record import UserRecord
from fastapi import HTTPException
from tools.parenting.analyze_parenting_journal import analyze_parenting_journal
from tools.parenting.follow_up import analyze_follow_up
from database.models.mcp_tool_call import McpToolCall
from database.models.user_record_has_analysis import UserRecordHasAnalysis
from tools.parenting.insight import generate_parenting_insight
from database.models.insight import Insight

# 配置日志
logger = logging.getLogger(__name__)

class AnalysisService:
    """
    分析服务类 - 负责调用工具进行分析并存储结果
    
    该类封装了分析相关的业务逻辑：
    1. 从数据库获取用户记录
    2. 调用相应的分析工具 
    3. 将分析结果存储到数据库
    4. 建立记录与分析结果的关联关系
    """
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化分析服务
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager

    async def analyze_record(self, record_id: str):
        """
        分析用户记录
        
        根据记录ID获取用户记录内容，调用育儿日记分析工具进行分析，
        并将结果存储到数据库中建立关联关系
        
        Args:
            record_id: 用户记录ID
            
        Returns:
            分析结果字典，与列表接口相同的数据结构
            
        Raises:
            HTTPException: 当记录不存在或分析失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                # 1. 根据ID获取用户记录
                record = await UserRecord.find_by_id(session, record_id)
                if not record:
                    logger.warning(f"用户记录不存在: {record_id}")
                    raise HTTPException(status_code=404, detail="Record not found")
                
                logger.info(f"开始分析用户记录: {record_id}")
                
                # 2. 调用育儿日记分析工具
                result = await analyze_parenting_journal(record.content)
                
                # 3. 将工具调用结果存储到数据库
                from database.models.mcp_tool_call import CallStatus
                from uuid import uuid4
                from database.utils.helpers import get_current_timestamp
                
                mcp_tool_call = McpToolCall(
                    id=str(uuid4()),  # 生成UUID
                    tool_name="analyze_parenting_journal",
                    user_id=record.user_id,
                    input_data={"content": record.content},  # 使用字典格式
                    output_data=result,
                    status=CallStatus.SUCCESS,  # 使用枚举值
                    created_at=get_current_timestamp(),
                    updated_at=get_current_timestamp()
                )
                await mcp_tool_call.save(session)
                
                # 4. 创建用户记录与分析结果的关联关系
                user_record_has_analysis = UserRecordHasAnalysis(
                    id=str(uuid4()),  # 生成UUID
                    user_record_id=record.id,
                    mcp_tool_call_id=mcp_tool_call.id,
                    type="parenting_journal_analysis",  # 设置关联类型
                    created_at=get_current_timestamp(),
                    updated_at=get_current_timestamp()
                )
                await user_record_has_analysis.save(session)
                
                logger.info(f"用户记录分析完成: {record_id}")

                # 构建与列表接口相同格式的返回数据（包含分析结果和文件存储信息）
                
                # 查询COS文件存储信息（如果有关联文件）
                file_storage = None
                if record.file_id:
                    from database.models.cos_file import CosFile
                    cos_file = await CosFile.find_by_id(session, record.file_id)
                    if cos_file:
                        # 处理file_info字段：从JSON字符串转换为Python字典
                        file_info_dict = {}
                        if cos_file.file_info:
                            if isinstance(cos_file.file_info, str):
                                # 如果是JSON字符串，需要反序列化
                                try:
                                    file_info_dict = json.loads(cos_file.file_info)
                                except (json.JSONDecodeError, TypeError) as e:
                                    logger.warning(f"解析file_info JSON失败: {e}, 原始值: {cos_file.file_info}")
                                    file_info_dict = {}
                            elif isinstance(cos_file.file_info, dict):
                                # 如果已经是字典，直接使用
                                file_info_dict = cos_file.file_info
                        
                        # 构建文件访问URL
                        file_url = f"https://{cos_file.bucket}.cos.{cos_file.region}.myqcloud.com/{cos_file.file_key}" if cos_file.bucket and cos_file.region else None
                        
                        file_storage = {
                            "file_id": str(cos_file.id),
                            "file_key": cos_file.file_key,
                            "file_info": file_info_dict,
                            "bucket": cos_file.bucket,
                            "region": cos_file.region,
                            "file_url": file_url,
                            "file_size": file_info_dict.get("size", 0) if file_info_dict else 0,
                            "content_type": file_info_dict.get("content_type", "unknown") if file_info_dict else "unknown",
                            "file_created_at": cos_file.created_at.isoformat() if cos_file.created_at else ""
                        }

                # 构建分析结果
                analysis_results = [
                    {
                        "tool_call_id": str(mcp_tool_call.id),
                        "tool_name": mcp_tool_call.tool_name,
                        "output_data": mcp_tool_call.output_data,
                        "status": mcp_tool_call.status.value if hasattr(mcp_tool_call.status, 'value') else str(mcp_tool_call.status),
                        "created_at": mcp_tool_call.created_at.isoformat() if mcp_tool_call.created_at else ""
                    }
                ]

                # 返回与列表接口相同格式的数据
                record_data = {
                    "id": str(record.id),
                    "user_id": str(record.user_id),
                    "content": record.content,
                    "content_type": record.content_type,
                    "file_id": str(record.file_id) if record.file_id else None,
                    "file_storage": file_storage,  # 完整的文件存储信息
                    "is_deleted": record.is_deleted,
                    "created_at": record.created_at.isoformat() if record.created_at else "",
                    "updated_at": record.updated_at.isoformat() if record.updated_at else "",
                    "analysis_results": analysis_results  # 分析结果数组
                }
                
                return record_data
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"分析用户记录失败 {record_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

    async def analyze_follow_up_content(self, user_id: str, content: str):
        """
        分析用户内容并判断是否需要追问
        
        基于S-A-R-F深度洞察模型分析用户提供的内容，
        智能判断信息完整性并决定是否需要进行针对性追问
        
        Args:
            user_id: 用户ID
            content: 用户输入的内容文本
            
        Returns:
            追问分析结果字典，包含追问建议和元数据
            
        Raises:
            HTTPException: 当分析失败时抛出
        """
        try:
            async with self.db_manager.get_session() as session:
                logger.info(f"开始分析用户内容进行追问判断: user_id={user_id}")
                
                # 调用追问分析工具
                result = await analyze_follow_up(content)
                
                # 将工具调用结果存储到数据库
                from database.models.mcp_tool_call import CallStatus
                from uuid import uuid4
                from database.utils.helpers import get_current_timestamp
                
                mcp_tool_call = McpToolCall(
                    id=str(uuid4()),  # 生成UUID
                    tool_name="analyze_follow_up",
                    user_id=user_id,
                    input_data={"content": content},  # 使用字典格式
                    output_data=result,
                    status=CallStatus.SUCCESS,  # 使用枚举值
                    created_at=get_current_timestamp(),
                    updated_at=get_current_timestamp()
                )
                await mcp_tool_call.save(session)
                
                # 解析追问分析结果
                import json
                try:
                    parsed_result = json.loads(result)
                    
                    # 如果解析结果包含错误信息，也添加到返回数据中
                    if "error" in parsed_result:
                        parsed_result["error"] = parsed_result["error"]
                        parsed_result["error_details"] = parsed_result.get("error_details", "")
                    
                    logger.info(f"追问分析完成: user_id={user_id}, type={parsed_result.get('type')}")
                    return parsed_result
                    
                except json.JSONDecodeError as e:
                    logger.error(f"解析追问结果JSON失败: {str(e)}")
                    # 即使JSON解析失败，也返回基本信息
                    follow_up_data = {
                        "tool_call_id": str(mcp_tool_call.id),
                        "tool_name": mcp_tool_call.tool_name,
                        "user_id": str(user_id),
                        "input_content": content,
                        "content_length": len(content),
                        "follow_up_result": {
                            "type": "question",
                            "content": "可以再详细描述一下您的情况吗？",
                            "reasoning": "追问结果解析失败，使用默认追问",
                            "need_follow_up": True,
                            "error": "JSON解析失败",
                            "raw_result": result[:200]  # 截取前200字符
                        },
                        "status": mcp_tool_call.status.value if hasattr(mcp_tool_call.status, 'value') else str(mcp_tool_call.status),
                        "created_at": mcp_tool_call.created_at.isoformat() if mcp_tool_call.created_at else ""
                    }
                    return follow_up_data
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"追问分析失败 user_id={user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"追问分析失败: {str(e)}")
    
    async def generate_parenting_insight(self, user_id: str, story_text: str):
        """
        调用育儿洞察工具，根据整理后的完整叙事文本生成洞察卡片。

        Args:
            user_id: 用户ID，用于记录调用日志
            story_text: 整理后的完整育儿叙事文本

        Returns:
            dict: 洞察卡片结果 JSON 解析后的字典
        """
        try:
            async with self.db_manager.get_session() as session:
                logger.info(f"开始生成育儿洞察: user_id={user_id}")

                # 调用洞察生成工具
                result = await generate_parenting_insight(story_text)

                # 记录工具调用
                from database.models.mcp_tool_call import CallStatus
                from uuid import uuid4
                from database.utils.helpers import get_current_timestamp

                mcp_tool_call = McpToolCall(
                    id=str(uuid4()),
                    tool_name="generate_parenting_insight",
                    user_id=user_id,
                    input_data={"story_text": story_text},
                    output_data=result,
                    status=CallStatus.SUCCESS,
                    created_at=get_current_timestamp(),
                    updated_at=get_current_timestamp()
                )
                await mcp_tool_call.save(session)

                # 尝试解析 JSON
                import json
                try:
                    parsed_result = json.loads(result)
                    # 如果包含错误字段，直接返回
                    logger.info("育儿洞察生成完成")
                    return parsed_result
                except json.JSONDecodeError as e:
                    logger.error(f"解析育儿洞察结果 JSON 失败: {e}")
                    return {
                        "tool_call_id": str(mcp_tool_call.id),
                        "tool_name": mcp_tool_call.tool_name,
                        "user_id": str(user_id),
                        "input_length": len(story_text),
                        "error": "JSON解析失败",
                        "raw_result": result[:200]
                    }
        except Exception as e:
            logger.error(f"育儿洞察生成失败 user_id={user_id}: {e}")
            raise HTTPException(status_code=500, detail=f"育儿洞察生成失败: {e}")
    
    def _evaluate_sarf_completeness(self, content: str) -> dict:
        """
        评估内容的S-A-R-F完整性
        
        Args:
            content: 用户输入的内容
            
        Returns:
            包含完整性评估的字典
        """
        completeness = {
            "scene": False,      # 情境
            "action": False,     # 行为
            "result": False,     # 结果
            "feeling": False,    # 感受
            "overall_score": 0   # 总体完整性评分(0-100)
        }
        
        score = 0
        
        # 检测情境关键词
        scene_keywords = ["今天", "昨天", "早上", "晚上", "在家", "学校", "公园", "商场", "时候"]
        if any(keyword in content for keyword in scene_keywords):
            completeness["scene"] = True
            score += 25
        
        # 检测行为关键词
        action_keywords = ["做", "说", "哭", "笑", "跑", "玩", "拒绝", "要求", "打", "抱", "叫"]
        if any(keyword in content for keyword in action_keywords):
            completeness["action"] = True
            score += 25
        
        # 检测结果关键词
        result_keywords = ["结果", "后来", "最后", "然后", "接着", "于是", "所以", "终于"]
        if any(keyword in content for keyword in result_keywords):
            completeness["result"] = True
            score += 25
        
        # 检测感受关键词
        feeling_keywords = ["开心", "生气", "难过", "委屈", "兴奋", "害怕", "焦虑", "无奈", "感动"]
        if any(keyword in content for keyword in feeling_keywords):
            completeness["feeling"] = True
            score += 25
        
        completeness["overall_score"] = score
        return completeness
    
    def _extract_content_keywords(self, content: str) -> list:
        """
        从内容中提取关键词
        
        Args:
            content: 用户输入的内容
            
        Returns:
            关键词列表
        """
        keywords = []
        
        # 育儿相关关键词
        parenting_keywords = [
            "孩子", "宝宝", "儿子", "女儿", "小朋友", "宝贝",
            "不听话", "发脾气", "哭闹", "不吃饭", "不睡觉",
            "作业", "学习", "玩耍", "游戏", "玩具"
        ]
        
        for keyword in parenting_keywords:
            if keyword in content:
                keywords.append(keyword)
        
        return keywords[:5]  # 最多返回5个关键词
    
    def _categorize_content(self, content: str) -> str:
        """
        对内容进行分类
        
        Args:
            content: 用户输入的内容
            
        Returns:
            内容分类字符串
        """
        # 简单的内容分类逻辑
        if any(word in content for word in ["孩子", "宝宝", "儿子", "女儿"]):
            if any(word in content for word in ["学习", "作业", "考试"]):
                return "学习教育"
            elif any(word in content for word in ["吃饭", "睡觉", "洗澡"]):
                return "生活习惯"
            elif any(word in content for word in ["玩", "游戏", "玩具"]):
                return "游戏娱乐"
            elif any(word in content for word in ["哭", "闹", "生气", "发脾气"]):
                return "情绪行为"
            else:
                return "亲子互动"
        else:
            return "其他内容"

    async def save_insight_card(self, user_id: str, record_id: str, content: dict):
        """
        保存洞察卡片并设置为收藏状态
        
        Args:
            user_id: 用户ID
            record_id: 关联的记录ID
            content: 洞察卡片内容
            
        Returns:
            保存的洞察卡片数据
        """
        try:
            async with self.db_manager.get_session() as session:
                from uuid import uuid4
                from database.utils.helpers import get_current_timestamp
                
                # 创建洞察卡片
                insight = Insight(
                    id=str(uuid4()),
                    user_id=user_id,
                    record_id=record_id,
                    content=content,
                    status=1,  # 直接设置为收藏状态
                    created_at=get_current_timestamp()
                )
                
                await insight.save(session)
                logger.info(f"洞察卡片已保存并收藏: {insight.id}")
                
                return insight.to_dict()
                
        except Exception as e:
            logger.error(f"保存洞察卡片失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"保存洞察卡片失败: {str(e)}")
    
    async def get_insight_cards(self, user_id: str):
        """
        获取用户的洞察卡片列表(仅收藏状态)
        
        Args:
            user_id: 用户ID
            
        Returns:
            洞察卡片列表，关联用户记录信息
        """
        try:
            async with self.db_manager.get_session() as session:
                # 查询用户收藏的洞察卡片
                insights = await Insight.find_by_conditions(
                    session, 
                    conditions={'user_id': user_id, 'status': 1}
                )
                
                result = []
                for insight in insights:
                    insight_data = insight.to_dict()
                    
                    # 如果有关联记录，获取记录信息
                    if insight.record_id:
                        # 🎯 修复：增加UUID格式验证，避免无效的record_id导致查询失败
                        is_valid_uuid = self._is_valid_uuid(insight.record_id)
                        
                        if is_valid_uuid:
                            try:
                                record = await UserRecord.find_by_id(session, insight.record_id)
                                if record:
                                    insight_data['record_info'] = {
                                        'id': record.id,
                                        'content': record.content,
                                        'content_type': record.content_type,
                                        'created_at': record.created_at.isoformat() if record.created_at else ""
                                    }
                                else:
                                    logger.warning(f"洞察卡片 {insight.id} 关联的记录 {insight.record_id} 不存在")
                                    insight_data['record_info'] = None
                            except Exception as e:
                                logger.error(f"查询关联记录失败: {insight.record_id}, 错误: {e}")
                                insight_data['record_info'] = None
                        else:
                            logger.warning(f"洞察卡片 {insight.id} 的record_id格式无效: {insight.record_id}")
                            insight_data['record_info'] = None
                    else:
                        insight_data['record_info'] = None
                    
                    result.append(insight_data)
                
                logger.info(f"获取用户洞察卡片列表: {user_id}, 数量: {len(result)}")
                return result
                
        except Exception as e:
            logger.error(f"获取洞察卡片列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取洞察卡片列表失败: {str(e)}")
    
    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """
        检查字符串是否为有效的UUID格式
        
        Args:
            uuid_string: 要检查的字符串
            
        Returns:
            bool: 是否为有效的UUID格式
        """
        try:
            from uuid import UUID
            UUID(uuid_string)
            return True
        except (ValueError, TypeError):
            return False
    
    async def delete_insight_card(self, user_id: str, insight_id: str):
        """
        删除收藏的洞察卡片
        
        Args:
            user_id: 用户ID
            insight_id: 洞察卡片ID
            
        Returns:
            操作结果
        """
        try:
            async with self.db_manager.get_session() as session:
                # 查找洞察卡片
                insight = await Insight.find_by_id(session, insight_id)
                if not insight:
                    raise HTTPException(status_code=404, detail="洞察卡片不存在")
                
                # 验证是否为当前用户的卡片
                if insight.user_id != user_id:
                    raise HTTPException(status_code=403, detail="无权限删除此洞察卡片")
                
                # 更新状态为删除(2)
                insight.status = 2
                await insight.save(session)
                
                logger.info(f"洞察卡片已删除: {insight_id}")
                return {"success": True, "message": "洞察卡片已删除"}
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除洞察卡片失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除洞察卡片失败: {str(e)}")
    
    async def regenerate_insight(self, user_id: str, record_id: str, additional_content: str):
        """
        重新生成洞察(在当前记录基础上追加新内容)
        
        Args:
            user_id: 用户ID
            record_id: 记录ID
            additional_content: 追加的新内容
            
        Returns:
            新生成的洞察卡片
        """
        try:
            async with self.db_manager.get_session() as session:
                # 获取原始记录
                record = await UserRecord.find_by_id(session, record_id)
                if not record:
                    raise HTTPException(status_code=404, detail="记录不存在")
                
                if record.user_id != user_id:
                    raise HTTPException(status_code=403, detail="无权限访问此记录")
                
                # 合并原内容和新内容
                combined_content = f"{record.content}\n\n{additional_content}"
                
                # 调用洞察生成工具
                result = await generate_parenting_insight(combined_content)
                
                # 解析结果
                import json
                try:
                    parsed_result = json.loads(result)
                    
                    # 保存新的洞察卡片
                    insight_data = await self.save_insight_card(user_id, record_id, parsed_result)
                    
                    logger.info(f"重新生成洞察完成: {record_id}")
                    return insight_data
                    
                except json.JSONDecodeError:
                    logger.error("洞察结果JSON解析失败")
                    raise HTTPException(status_code=500, detail="洞察生成结果格式错误")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"重新生成洞察失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"重新生成洞察失败: {str(e)}")

    async def generate_more_insight(self, user_id: str, record_id: str, user_question: str):
        """
        生成更多洞察(深度洞察模式)
        
        基于原始记录内容和首次洞察结果，结合用户追问生成深度洞察
        
        Args:
            user_id: 用户ID
            record_id: 记录ID，用于获取原始故事
            user_question: 用户追问内容
            
        Returns:
            深度洞察卡片结果
        """
        try:
            async with self.db_manager.get_session() as session:
                # 1. 获取原始用户记录
                record = await UserRecord.find_by_id(session, record_id)
                if not record:
                    raise HTTPException(status_code=404, detail="记录不存在")
                
                if record.user_id != user_id:
                    raise HTTPException(status_code=403, detail="无权限访问此记录")
                
                original_story = record.content
                
                # 2. 查询首次洞察结果(通过关联表查询status=1的insights)
                insight_query = """
                    SELECT content 
                    FROM insights 
                    WHERE record_id = $1 AND user_id = $2 AND status = 1 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """
                
                insight_row = await session.fetchrow(insight_query, record_id, user_id)
                
                if not insight_row:
                    # 创建空的json数组
                    first_insight = []
                
                # 解析首次洞察结果
                first_insight_content = insight_row["content"] if insight_row else None
                if isinstance(first_insight_content, str):
                    import json
                    try:
                        first_insight = json.loads(first_insight_content)
                    except json.JSONDecodeError:
                        first_insight = first_insight_content
                else:
                    first_insight = first_insight_content
                
                # 转换为JSON字符串格式供deep模式使用
                first_insight_json = json.dumps(first_insight, ensure_ascii=False, indent=2)
                
                logger.info(f"开始生成深度洞察: user_id={user_id}, record_id={record_id}")
                
                # 3. 调用深度洞察工具(mode="deep")
                result = await generate_parenting_insight(
                    full_story=original_story,  # 使用原始故事作为full_story
                    mode="deep",
                    original_story=original_story,
                    first_insight=first_insight_json,
                    user_question=user_question,
                    user_id=user_id
                )
                
                # 4. 记录工具调用
                from database.models.mcp_tool_call import CallStatus
                from uuid import uuid4
                from database.utils.helpers import get_current_timestamp
                
                mcp_tool_call = McpToolCall(
                    id=str(uuid4()),
                    tool_name="generate_parenting_insight",
                    user_id=user_id,
                    input_data={
                        "mode": "deep",
                        "original_story": original_story,
                        "first_insight": first_insight_json,
                        "user_question": user_question,
                        "record_id": record_id
                    },
                    output_data=result,
                    status=CallStatus.SUCCESS,
                    created_at=get_current_timestamp(),
                    updated_at=get_current_timestamp()
                )
                await mcp_tool_call.save(session)
                
                # 5. 解析结果
                import json
                try:
                    parsed_result = json.loads(result)
                    
                    # 检查是否有错误
                    if "error" in parsed_result:
                        logger.warning(f"深度洞察生成返回错误: {parsed_result.get('error')}")
                        return parsed_result
                    
                    # 如果生成成功，可选择保存为新的洞察卡片
                    # 注意：这里不自动保存，让前端决定是否收藏
                    
                    logger.info(f"深度洞察生成成功: record_id={record_id}")
                    return parsed_result
                    
                except json.JSONDecodeError as e:
                    logger.error(f"深度洞察结果JSON解析失败: {e}")
                    return {
                        "error": "深度洞察生成结果格式错误",
                        "tool_call_id": str(mcp_tool_call.id),
                        "raw_result": result[:200]  # 截取前200字符用于调试
                    }
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"生成深度洞察失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"生成深度洞察失败: {str(e)}")

# 全局分析服务实例
_analysis_service: AnalysisService = None

def initialize_analysis_service(db_manager: DatabaseManager):
    """
    初始化分析服务
    
    在应用启动时调用，创建全局分析服务实例
    
    Args:
        db_manager: 数据库管理器实例
    """
    global _analysis_service
    _analysis_service = AnalysisService(db_manager)
    logger.info("分析服务已初始化")

def get_analysis_service() -> AnalysisService:
    """
    获取分析服务实例
    
    用于FastAPI依赖注入，提供分析服务实例
    
    Returns:
        AnalysisService: 分析服务实例
        
    Raises:
        RuntimeError: 当服务未初始化时抛出
    """
    if _analysis_service is None:
        logger.error("分析服务未初始化")
        raise RuntimeError("分析服务未初始化，请先调用 initialize_analysis_service()")
    
    return _analysis_service 
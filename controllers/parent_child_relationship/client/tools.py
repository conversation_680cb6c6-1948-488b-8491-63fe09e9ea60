"""
MCP tools 调用的client端
亲子关系分析模块的MCP工具客户端
基于官方mcp库，提供analyze_parenting_journal工具的调用功能
"""

import asyncio
import json
import logging
from typing import Optional, Dict, Any, List
from datetime import timedelta

from database.utils import get_current_time_iso

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MCP服务器配置 - 使用官方mcp库
MCP_SERVER_URL = "http://localhost:8080/mcp"

class ParentingJournalAnalyzer:
    """
    亲子关系日记分析器
    
    使用MCP工具 analyze_parenting_journal 对用户的亲子关系记录进行分析
    提供结构化的分析结果和建议
    """
    
    def __init__(self, server_url: str = MCP_SERVER_URL):
        """
        初始化分析器
        
        Args:
            server_url: MCP服务器地址
        """
        self.server_url = server_url
        self._is_server_ready = None  # 缓存服务器状态
    
    async def _call_mcp_tool(self, tool_name: str, parameters: dict) -> dict:
        """
        调用MCP工具 - 参考analysis.py的实现方式
        
        Args:
            tool_name: 工具名称 
            parameters: 工具参数
            
        Returns:
            工具执行结果
        """
        try:
            # 使用官方mcp库进行每次调用
            from mcp import ClientSession
            from mcp.client.streamable_http import streamablehttp_client
            
            logger.info(f"调用MCP工具: {tool_name}")
            logger.debug(f"工具参数: {parameters}")
            
            # 为每次调用创建独立的连接避免资源管理冲突
            async with streamablehttp_client(
                url=self.server_url,
                headers=None,
                timeout=timedelta(seconds=30),
                sse_read_timeout=timedelta(seconds=300)
            ) as (read_stream, write_stream, get_session_id):
                
                async with ClientSession(read_stream, write_stream) as session:
                    await session.initialize()
                    
                    # 调用工具
                    result = await session.call_tool(tool_name, parameters)
                    
                    # 提取结果内容
                    if hasattr(result, 'content'):
                        if isinstance(result.content, list) and len(result.content) > 0:
                            content = result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
                        else:
                            content = str(result.content)
                    else:
                        content = str(result)
                    
                    logger.info(f"MCP工具调用成功: {tool_name}")
                    logger.debug(f"MCP工具原始返回内容: {content[:200]}...")
                    
                    # 尝试解析JSON内容
                    try:
                        # 如果内容是JSON字符串，解析为Python对象
                        parsed_data = json.loads(content)
                        logger.info(f"成功解析JSON数据，类型: {type(parsed_data)}")
                        logger.info(f"解析后的数据: {parsed_data}")
                        return {
                            "success": True,
                            "data": parsed_data
                        }
                    except json.JSONDecodeError:
                        # 如果不是JSON，返回原始字符串
                        logger.debug(f"MCP工具返回非JSON内容: {content[:100]}...")
                        return {
                            "success": True,
                            "data": content
                        }
            
        except Exception as e:
            logger.error(f"调用MCP工具失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _check_mcp_server_health(self) -> bool:
        """
        检查MCP服务器健康状态
        
        Returns:
            bool: 服务器是否健康
        """
        try:
            # 简单的健康检查 - 尝试列出工具
            from mcp import ClientSession
            from mcp.client.streamable_http import streamablehttp_client
            
            async with streamablehttp_client(
                url=self.server_url,
                headers=None,
                timeout=timedelta(seconds=5),
                sse_read_timeout=timedelta(seconds=10)
            ) as (read_stream, write_stream, get_session_id):
                
                async with ClientSession(read_stream, write_stream) as session:
                    await session.initialize()
                    response = await session.list_tools()
                    
                    # 检查是否包含我们需要的工具
                    tool_names = [tool.name for tool in response.tools]
                    has_required_tool = "analyze_parenting_journal" in tool_names
                    
                    if has_required_tool:
                        logger.info(f"✅ MCP服务器健康，包含所需工具: {tool_names}")
                    else:
                        logger.warning(f"⚠️ MCP服务器缺少analyze_parenting_journal工具。可用工具: {tool_names}")
                    
                    return has_required_tool
                    
        except Exception as e:
            logger.debug(f"MCP服务器健康检查失败: {str(e)}")
            return False
    
    async def _ensure_mcp_server_ready(self) -> bool:
        """
        确保MCP服务器已启动并可用
        
        Returns:
            bool: 服务器是否准备就绪
        """
        # 如果已经检查过状态，直接返回缓存结果
        if self._is_server_ready is not None:
            return self._is_server_ready
        
        try:
            # 检查MCP服务器是否可用
            if not await self._check_mcp_server_health():
                logger.info("🔧 MCP服务器不可用，正在等待...")
                # 等待MCP服务器启动
                max_retries = 30
                retry_count = 0
                
                while not await self._check_mcp_server_health() and retry_count < max_retries:
                    await asyncio.sleep(1)
                    retry_count += 1
                    if retry_count % 5 == 0:
                        logger.info(f"⏳ 等待MCP服务器可用... ({retry_count}/{max_retries})")
                
                if not await self._check_mcp_server_health():
                    logger.warning("⚠️ MCP服务器未在预期时间内可用")
                    self._is_server_ready = False
                    return False
            
            logger.info("✅ MCP服务器已可用")
            logger.info(f"📡 连接到MCP服务器: {self.server_url}")
            self._is_server_ready = True
            return True
            
        except Exception as e:
            logger.error(f"❌ MCP服务器检查失败: {str(e)}")
            self._is_server_ready = False
            return False
    
    async def analyze_journal(
        self, 
        journal_text: str, 
        child_age: Optional[str] = None,
        relationship_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分析亲子关系日记
        
        调用MCP工具analyze_parenting_journal对用户的亲子关系记录进行深度分析
        
        Args:
            journal_text: 亲子关系日记文本内容
            child_age: 孩子年龄（可选）
            relationship_context: 关系背景信息（可选）
            
        Returns:
            Dict[str, Any]: 分析结果，包含情感分析、建议等
            
        Raises:
            RuntimeError: 当MCP服务器不可用时抛出
        """
        # 确保MCP服务器可用
        if not await self._ensure_mcp_server_ready():
            raise RuntimeError("MCP服务器不可用，无法进行分析")
        
        # 准备工具调用参数 - 注意：工具只接受journal_entry参数
        parameters = {
            "journal_entry": journal_text
        }
        
        # 注意：analyze_parenting_journal工具目前只接受journal_entry参数
        # child_age和relationship_context可以包含在journal_text中描述
        if child_age or relationship_context:
            # 将额外信息添加到日记文本中
            enhanced_text = journal_text
            if child_age:
                enhanced_text = f"[孩子年龄: {child_age}] {enhanced_text}"
            if relationship_context:
                enhanced_text = f"[背景: {relationship_context}] {enhanced_text}"
            parameters["journal_entry"] = enhanced_text
        
        actual_text = parameters["journal_entry"]
        logger.info(f"开始分析亲子关系日记，实际文本长度: {len(actual_text)} 字符")
        
        # 调用MCP工具
        result = await self._call_mcp_tool("analyze_parenting_journal", parameters)
        
        if not result["success"]:
            logger.error(f"亲子关系分析失败: {result.get('error', '未知错误')}")
            raise RuntimeError(f"分析失败: {result.get('error', '未知错误')}")
        
        # 处理分析结果
        analysis_data = result["data"]
        
        # 如果返回的是字符串，尝试解析为JSON
        if isinstance(analysis_data, str):
            try:
                analysis_data = json.loads(analysis_data)
            except json.JSONDecodeError:
                # 如果解析失败，包装为结构化格式
                analysis_data = {
                    "analysis_text": analysis_data,
                    "structured": False
                }
        
        logger.info("✅ 亲子关系分析完成")
        return {
            "success": True,
            "analysis": analysis_data,
            "metadata": {
                "tool_used": "analyze_parenting_journal",
                "text_length": len(journal_text),
                "has_child_age": bool(child_age),
                "has_context": bool(relationship_context),
                "analysis_timestamp": get_current_time_iso()
            }
        }
    
    async def batch_analyze_journals(
        self, 
        journal_entries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量分析多个亲子关系日记
        
        Args:
            journal_entries: 日记条目列表，每个条目应包含:
                - text: 日记文本
                - child_age: 孩子年龄（可选）
                - context: 关系背景（可选）
                - id: 记录ID（可选）
                
        Returns:
            List[Dict[str, Any]]: 批量分析结果列表
        """
        if not await self._ensure_mcp_server_ready():
            raise RuntimeError("MCP服务器不可用，无法进行批量分析")
        
        logger.info(f"开始批量分析 {len(journal_entries)} 个亲子关系日记")
        
        results = []
        
        for i, entry in enumerate(journal_entries):
            try:
                logger.info(f"分析第 {i+1}/{len(journal_entries)} 个日记")
                
                result = await self.analyze_journal(
                    journal_text=entry.get("text", ""),
                    child_age=entry.get("child_age"),
                    relationship_context=entry.get("context")
                )
                
                # 添加条目ID到结果中
                if "id" in entry:
                    result["entry_id"] = entry["id"]
                result["batch_index"] = i
                
                results.append(result)
                
                # 在批量处理中添加短暂延迟，避免过快请求
                if i < len(journal_entries) - 1:
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"分析第 {i+1} 个日记失败: {str(e)}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "entry_id": entry.get("id"),
                    "batch_index": i
                })
        
        logger.info(f"✅ 批量分析完成，成功: {sum(1 for r in results if r.get('success'))}/{len(results)}")
        return results
    
    async def get_available_tools(self) -> List[str]:
        """
        获取MCP服务器上可用的工具列表
        
        Returns:
            List[str]: 可用工具名称列表
        """
        try:
            from mcp import ClientSession
            from mcp.client.streamable_http import streamablehttp_client
            
            async with streamablehttp_client(
                url=self.server_url,
                headers=None,
                timeout=timedelta(seconds=10),
                sse_read_timeout=timedelta(seconds=30)
            ) as (read_stream, write_stream, get_session_id):
                
                async with ClientSession(read_stream, write_stream) as session:
                    await session.initialize()
                    response = await session.list_tools()
                    tool_names = [tool.name for tool in response.tools]
                    return tool_names
                    
        except Exception as e:
            logger.error(f"获取工具列表失败: {str(e)}")
            return []

# 创建全局分析器实例
_analyzer_instance: Optional[ParentingJournalAnalyzer] = None

def get_analyzer() -> ParentingJournalAnalyzer:
    """
    获取全局分析器实例（单例模式）
    
    Returns:
        ParentingJournalAnalyzer: 分析器实例
    """
    global _analyzer_instance
    if _analyzer_instance is None:
        _analyzer_instance = ParentingJournalAnalyzer()
    return _analyzer_instance

# 便捷函数，直接使用全局实例
async def analyze_parenting_journal(
    journal_text: str,
    child_age: Optional[str] = None,
    relationship_context: Optional[str] = None
) -> Dict[str, Any]:
    """
    分析亲子关系日记的便捷函数
    
    Args:
        journal_text: 亲子关系日记文本内容
        child_age: 孩子年龄（可选）
        relationship_context: 关系背景信息（可选）
        
    Returns:
        Dict[str, Any]: 分析结果
    """
    analyzer = get_analyzer()
    return await analyzer.analyze_journal(journal_text, child_age, relationship_context)

async def batch_analyze_journals(journal_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    批量分析亲子关系日记的便捷函数
    
    Args:
        journal_entries: 日记条目列表
        
    Returns:
        List[Dict[str, Any]]: 批量分析结果
    """
    analyzer = get_analyzer()
    return await analyzer.batch_analyze_journals(journal_entries)

async def check_mcp_status() -> Dict[str, Any]:
    """
    检查MCP服务器状态的便捷函数
    
    Returns:
        Dict[str, Any]: 服务器状态信息
    """
    analyzer = get_analyzer()
    is_healthy = await analyzer._check_mcp_server_health()
    available_tools = await analyzer.get_available_tools() if is_healthy else []
    
    return {
        "healthy": is_healthy,
        "server_url": analyzer.server_url,
        "available_tools": available_tools,
        "has_required_tool": "analyze_parenting_journal" in available_tools,
        "timestamp": get_current_time_iso()
    }

# 对外暴露的主要接口
__all__ = [
    "ParentingJournalAnalyzer",
    "get_analyzer", 
    "analyze_parenting_journal",
    "batch_analyze_journals",
    "check_mcp_status"
]

# 模块信息
def get_module_info() -> Dict[str, Any]:
    """获取模块信息"""
    return {
        "name": "亲子关系分析MCP客户端",
        "version": "1.0.0",
        "mcp_tool": "analyze_parenting_journal",
        "server_url": MCP_SERVER_URL,
        "features": [
            "单个日记分析",
            "批量日记分析",  
            "MCP服务器健康检查",
            "工具列表查询"
        ],
        "dependencies": ["mcp", "asyncio", "json"]
    }

if __name__ == "__main__":
    async def test_example():
        """测试示例"""
        print("🚀 亲子关系分析MCP客户端测试")
        
        # 显示模块信息
        info = get_module_info()
        print(f"\n📦 {info['name']} v{info['version']}")
        print(f"🔧 MCP工具: {info['mcp_tool']}")
        print(f"📡 服务器: {info['server_url']}")
        
        # 检查MCP服务器状态
        print("\n🔍 检查MCP服务器状态...")
        status = await check_mcp_status()
        print(f"服务器健康: {status['healthy']}")
        print(f"可用工具: {status['available_tools']}")
        
        if status['healthy'] and status['has_required_tool']:
            # 测试分析功能
            print("\n📝 测试亲子关系分析...")
            test_journal = "今天和孩子一起做了手工，他很开心，但是有点不耐烦。我觉得需要更有耐心一些。"
            
            try:
                result = await analyze_parenting_journal(
                    journal_text=test_journal,
                    child_age="5岁",
                    relationship_context="日常亲子活动"
                )
                print("✅ 分析成功")
                print(result)
                print(f"结果类型: {type(result['analysis'])}")
            except Exception as e:
                print(f"❌ 分析失败: {e}")
        else:
            print("⚠️ MCP服务器不可用或缺少所需工具")
    
    # 运行测试
    asyncio.run(test_example())

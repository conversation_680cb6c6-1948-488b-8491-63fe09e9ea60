"""
亲子分析启动器，主要启动fastAPI 接口服务
提供亲子关系分析的完整API服务，包括用户记录管理和分析功能
支持异步处理、错误处理、日志记录等功能

重构说明：
- 将所有路由定义移到独立的routes模块中
- server.py 只负责应用配置、中间件设置和服务启动
- 提高代码可维护性和模块化程度
"""

import os
import sys
import logging
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi import status
import uvicorn

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 导入数据库和服务模块
from database import DatabaseManager, DatabaseConfig
from controllers.parent_child_relationship.api.record import initialize_record_service
from controllers.parent_child_relationship.api.analysis import initialize_analysis_service
from controllers.parent_child_relationship.routes import main_router
from controllers.parent_child_relationship.routes.health import set_db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量存储数据库管理器
db_manager: Optional[DatabaseManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    
    在应用启动时初始化数据库连接和服务
    在应用关闭时清理资源
    """
    global db_manager
    
    try:
        # 启动时初始化
        logger.info("初始化亲子关系分析服务...")
        
        # 创建数据库管理器
        db_config = DatabaseConfig()
        db_manager = DatabaseManager(db_config)
        
        # 初始化数据库连接池
        await db_manager.initialize()
        logger.info("数据库连接池初始化完成")
        
        # 执行数据库迁移
        # await db_manager.migrate()
        # logger.info("数据库迁移完成")
        
        # 初始化用户记录服务
        initialize_record_service(db_manager)
        logger.info("用户记录服务初始化完成")
        
        # 初始化分析服务
        initialize_analysis_service(db_manager)
        logger.info("分析服务初始化完成")
        
        # 设置健康检查路由的数据库管理器引用
        set_db_manager(db_manager)
        
        logger.info("亲子关系分析服务启动成功")
        
        yield
        
    finally:
        # 关闭时清理资源
        logger.info("关闭亲子关系分析服务...")
        if db_manager:
            await db_manager.close()
            logger.info("数据库连接已关闭")
        logger.info("亲子关系分析服务已关闭")

# 创建 FastAPI 应用实例
app = FastAPI(
    title="亲子关系分析API",
    description="提供亲子关系分析的完整API服务，包括用户记录管理和分析功能",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",  # Swagger UI 路径
    redoc_url="/redoc",  # ReDoc 路径
    openapi_url="/openapi.json"  # OpenAPI 规范路径
)

# 配置CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器
    
    捕获所有未处理的异常并返回统一的错误响应
    记录异常信息用于调试和监控
    
    Args:
        request: HTTP请求对象
        exc: 异常对象
        
    Returns:
        JSONResponse: 统一格式的错误响应
    """
    logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)}")
    logger.error(f"请求路径: {request.url}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": "服务器内部错误",
            "error_type": type(exc).__name__,
            "timestamp": str(__import__('time').time())
        }
    )

# 注册路由
app.include_router(main_router)

def create_app() -> FastAPI:
    """
    创建FastAPI应用实例的工厂函数
    
    用于测试或其他需要程序化创建应用的场景
    
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    return app

def main():
    """
    主函数，启动FastAPI服务器
    
    配置和启动Uvicorn服务器，支持热重载和多进程
    从环境变量读取配置，提供灵活的部署选项
    """
    # 从环境变量获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8001"))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    workers = int(os.getenv("WORKERS", "1"))
    
    logger.info("========================================")
    logger.info("启动亲子关系分析API服务器")
    logger.info("========================================")
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"调试模式: {debug}")
    logger.info(f"工作进程: {workers}")
    logger.info(f"API文档: http://{host}:{port}/docs")
    logger.info("========================================")
    
    # 启动服务器配置
    server_config = {
        "app": "controllers.parent_child_relationship.server:app",
        "host": host,
        "port": port,
        "log_level": "info" if not debug else "debug",
        "access_log": True,
        "reload": debug,  # 开发模式下启用热重载
    }
    
    # 生产环境下使用多进程
    if not debug and workers > 1:
        server_config["workers"] = workers
    
    # 启动服务器
    try:
        uvicorn.run(**server_config)
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        raise

if __name__ == "__main__":
    main()


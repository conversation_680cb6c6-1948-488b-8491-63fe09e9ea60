"""
亲子关系分析API路由模块

将不同功能的API路由分组管理，提高代码可维护性
包含：
- records: 用户记录相关路由
- health: 健康检查和状态路由
- analysis: 分析服务路由（待实现）
"""

from fastapi import APIRouter
from controllers.parent_child_relationship.routes.records import router as records_router
from controllers.parent_child_relationship.routes.health import router as health_router
from controllers.parent_child_relationship.routes.analysis import router as analysis_router

# 创建主路由器
main_router = APIRouter()

# 注册子路由
main_router.include_router(
    records_router,
    prefix="",
    tags=["用户记录管理"]
)

main_router.include_router(
    health_router,
    prefix="",
    tags=["系统状态"]
)

main_router.include_router(
    analysis_router,
    prefix="",
    tags=["亲子关系分析"]
)

__all__ = ["main_router"] 
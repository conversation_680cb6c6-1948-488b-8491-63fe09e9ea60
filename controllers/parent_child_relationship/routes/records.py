"""
用户记录管理路由

提供用户记录的完整CRUD操作API端点
包括创建、查询、更新、删除、恢复等功能
支持分页查询和数据统计
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body, status
from pydantic import BaseModel, Field
from datetime import datetime

from controllers.parent_child_relationship.api.record import UserRecordService, get_record_service
from controllers.common.api.auth import ApiResponse
from database.utils import get_current_time_iso

logger = logging.getLogger(__name__)

# 创建用户记录路由器
router = APIRouter()

# Pydantic 模型定义，用于API请求和响应的数据验证

class CreateRecordRequest(BaseModel):
    """创建记录请求模型"""
    user_id: str = Field(..., description="用户ID", min_length=1, max_length=255)
    content: str = Field(..., description="记录内容", min_length=1)
    content_type: str = Field(default="text", description="内容类型")
    # tags: Optional[List[str]] = Field(default=[], description="标签列表")
    file_ids: Optional[List[str]] = Field(default=[], description="关联的COS文件ID列表")
    
    class Config:
        """Pydantic 配置类"""
        # 启用字段验证，处理空字符串转换为None
        validate_assignment = True
        
    def __init__(self, **data):
        """
        初始化请求模型，处理file_ids空值问题
        
        将空字符串或None的file_ids转换为空列表
        """
        # 🎯 修复：将空值file_ids转换为空列表
        if 'file_ids' in data and (data['file_ids'] is None or data['file_ids'] == ''):
            data['file_ids'] = []
        super().__init__(**data)

class UpdateRecordRequest(BaseModel):
    """更新记录请求模型"""
    content: Optional[str] = Field(None, description="记录内容")
    content_type: Optional[str] = Field(None, description="内容类型")
    # tags: Optional[List[str]] = Field(None, description="标签列表")
    file_ids: Optional[List[str]] = Field(default=None, description="关联的COS文件ID列表")
    
    class Config:
        """Pydantic 配置类"""
        # 启用字段验证，处理空字符串转换为None
        validate_assignment = True
        
    def __init__(self, **data):
        """
        初始化请求模型，处理file_ids空值问题
        
        将空字符串或None的file_ids转换为空列表
        """
        # 🎯 修复：将空值file_ids转换为空列表
        if 'file_ids' in data and (data['file_ids'] is None or data['file_ids'] == ''):
            data['file_ids'] = []
        super().__init__(**data)

class RecordResponse(BaseModel):
    """记录响应模型"""
    id: str
    user_id: str
    content: str
    content_type: str
    # tags: List[str]
    file_ids: List[str] = []
    is_deleted: bool
    created_at: str
    updated_at: str

class PaginationResponse(BaseModel):
    """分页响应模型"""
    current_page: int
    page_size: int
    total_count: int
    total_pages: int
    has_next: bool
    has_prev: bool

class RecordsResponse(BaseModel):
    """记录列表响应模型"""
    records: List[RecordResponse]
    pagination: PaginationResponse

class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str

# 用户记录相关的API端点

@router.post("/records", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_record(
    request: CreateRecordRequest,
    service: UserRecordService = Depends(get_record_service)
):
    """
    创建用户记录
    
    创建一条新的用户记录，包含内容、类型和标签信息
    
    Args:
        request: 创建记录请求数据
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含创建的用户记录信息
        
    Raises:
        HTTPException: 当用户不存在或创建失败时抛出
    """
    try:
        record = await service.create_record(
            user_id=request.user_id,
            content=request.content,
            content_type=request.content_type,
            # tags=request.tags
            file_ids=request.file_ids
        )
        
        # 构建记录数据
        record_data = {
            "id": str(record.id),  # 确保 UUID 对象转换为字符串
            "user_id": str(record.user_id),
            "content": record.content,
            "content_type": record.content_type,
            # "tags": record.tags or [],
            "file_ids": record.file_ids or [],
            "file_storage": record.file_storage or [],  # 包含文件存储信息
            "is_deleted": record.is_deleted,
            "created_at": record.created_at.isoformat() if record.created_at else "",
            "updated_at": record.updated_at.isoformat() if record.updated_at else ""
        }
        
        return ApiResponse(
            success=True,
            message="用户记录创建成功",
            data={"record": record_data},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"创建记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"创建记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.get("/records/{record_id}", response_model=ApiResponse)
async def get_record(
    record_id: str = Path(..., description="记录ID"),
    service: UserRecordService = Depends(get_record_service)
):
    """
    根据ID获取用户记录
    
    根据记录ID获取单条用户记录的详细信息
    
    Args:
        record_id: 记录ID
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含用户记录详细信息
        
    Raises:
        HTTPException: 当记录不存在时抛出404错误
    """
    try:
        record_data = await service.get_record_by_id(record_id)
        
        # 构建成功消息，包含洞察信息统计
        insights_count = len(record_data.get("insights", []))
        success_message = f"获取用户记录成功"
        if insights_count > 0:
            success_message += f"，包含{insights_count}项洞察信息"
        
        return ApiResponse(
            success=True,
            message=success_message,
            data={"record": record_data},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"获取记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.get("/records", response_model=ApiResponse)
async def get_records_with_pagination(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    content_type: Optional[str] = Query(None, description="内容类型过滤"),
    file_ids: Optional[List[str]] = Query(None, description="关联文件ID过滤，支持多个文件ID"),
    date: Optional[str] = Query(None, description="日期过滤，格式：YYYY-MM-DD"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小"),
    service: UserRecordService = Depends(get_record_service)
):
    """
    分页查询用户记录（包含关联的分析结果和文件存储信息）
    
    支持多维度过滤的分页查询，自动关联查询MCP工具调用的分析结果和COS文件存储信息
    每条记录包含完整的分析数据和文件信息，包括工具名称、输出数据、执行状态、文件URL等
    
    数据结构说明：
    - records: 用户记录列表
      - 基础字段：id, user_id, content, content_type, file_id, is_deleted, created_at, updated_at
      - analysis_results: 关联的分析结果数组
        - tool_call_id: MCP工具调用ID
        - tool_name: 工具名称（如 analyze_parenting_journal）
        - output_data: 工具分析的输出结果（包含完整的分析数据）
        - status: 执行状态（pending/success/failed）
        - created_at: 分析时间
      - file_storage: 关联的文件存储信息
        - file_id: 文件ID
        - file_key: 文件存储键值
        - file_url: 文件访问URL
        - file_size: 文件大小（字节）
        - content_type: 文件类型
        - file_created_at: 文件上传时间
    - pagination: 分页信息
    - statistics: 统计信息（文件数量、总大小等）
    
    Args:
        user_id: 用户ID过滤条件，可选
        content_type: 内容类型过滤条件，可选
        file_id: 关联文件ID过滤条件，可选
        date: 日期过滤条件，格式YYYY-MM-DD，可选
        page: 页码，从1开始
        page_size: 每页记录数量，范围1-100
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含记录列表、分页信息和统计信息，每条记录附带分析结果和文件信息
        
    Raises:
        HTTPException: 当查询失败时抛出
    """
    try:
        # 🎯 添加详细的参数日志，便于调试
        logger.info(f"分页查询参数: user_id={user_id}, content_type={content_type}, file_ids={file_ids}, date={date}, page={page}, page_size={page_size}")
        
        result = await service.get_records_with_pagination(
            user_id=user_id,
            content_type=content_type,
            file_ids=file_ids,
            date=date,
            page=page,
            page_size=page_size
        )
        
        # 统计分析结果、文件存储信息和洞察信息
        total_analysis_count = 0
        records_with_analysis = 0
        records_with_files = 0
        total_insights_count = 0
        records_with_insights = 0
        
        for record in result["records"]:
            analysis_results = record.get("analysis_results", [])
            if analysis_results:
                total_analysis_count += len(analysis_results)
                records_with_analysis += 1
            
            if record.get("file_storage"):
                records_with_files += 1
            
            insights = record.get("insights", [])
            if insights:
                total_insights_count += len(insights)
                records_with_insights += 1
        
        # 构建详细的成功信息
        success_message_parts = [
            f"分页查询记录成功，第{page}页，共{result['pagination']['total_count']}条记录"
        ]
        
        if records_with_analysis > 0:
            success_message_parts.append(f"其中{records_with_analysis}条包含分析结果，共{total_analysis_count}项分析")
        
        if records_with_files > 0:
            file_size_mb = result['statistics']['total_file_size_mb']
            success_message_parts.append(f"{records_with_files}条包含文件存储，总大小{file_size_mb}MB")
        
        if records_with_insights > 0:
            success_message_parts.append(f"{records_with_insights}条包含洞察信息，共{total_insights_count}项洞察")
        
        return ApiResponse(
            success=True,
            message="，".join(success_message_parts),
            data={
                "records": result["records"],
                "pagination": result["pagination"],
                "statistics": result["statistics"],  # 🎯 新增：包含文件存储统计信息
                "analysis_summary": {
                    "total_records": result['pagination']['total_count'],
                    "records_with_analysis": records_with_analysis,
                    "total_analysis_count": total_analysis_count,
                    "analysis_coverage": f"{records_with_analysis}/{result['pagination']['total_count']}" if result['pagination']['total_count'] > 0 else "0/0"
                },
                "file_summary": {  # 🎯 新增：文件存储摘要信息
                    "records_with_files": records_with_files,
                    "file_coverage": f"{records_with_files}/{result['pagination']['total_count']}" if result['pagination']['total_count'] > 0 else "0/0"
                },
                "insights_summary": {  # 🎯 新增：洞察信息摘要
                    "records_with_insights": records_with_insights,
                    "total_insights_count": total_insights_count,
                    "insights_coverage": f"{records_with_insights}/{result['pagination']['total_count']}" if result['pagination']['total_count'] > 0 else "0/0"
                }
            },
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"分页查询记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"分页查询记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.put("/records/{record_id}", response_model=ApiResponse)
async def update_record(
    record_id: str = Path(..., description="记录ID"),
    request: UpdateRecordRequest = Body(...),
    service: UserRecordService = Depends(get_record_service)
):
    """
    更新用户记录
    
    更新指定记录的内容、类型或标签信息
    支持部分字段更新，未提供的字段保持不变
    
    Args:
        record_id: 要更新的记录ID
        request: 更新请求数据
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含更新后的用户记录信息
        
    Raises:
        HTTPException: 当记录不存在或更新失败时抛出
    """
    try:
        record = await service.update_record(
            record_id=record_id,
            content=request.content,
            content_type=request.content_type,
            # tags=request.tags
            file_ids=request.file_ids
        )
        
        record_data = {
            "id": str(record.id),  # 确保 UUID 对象转换为字符串
            "user_id": str(record.user_id),
            "content": record.content,
            "content_type": record.content_type,
            # "tags": record.tags or [],
            "file_ids": record.file_ids or [],
            "is_deleted": record.is_deleted,
            "created_at": record.created_at.isoformat() if record.created_at else "",
            "updated_at": record.updated_at.isoformat() if record.updated_at else ""
        }
        
        return ApiResponse(
            success=True,
            message="用户记录更新成功",
            data={"record": record_data},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"更新记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"更新记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.delete("/records/{record_id}", response_model=ApiResponse)
async def delete_record(
    record_id: str = Path(..., description="记录ID"),
    service: UserRecordService = Depends(get_record_service)
):
    """
    删除用户记录（软删除）
    
    标记指定记录为已删除状态，不会真正删除数据
    删除的记录可以通过恢复接口重新激活
    
    Args:
        record_id: 要删除的记录ID
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，包含删除操作结果消息
        
    Raises:
        HTTPException: 当记录不存在或删除失败时抛出
    """
    try:
        result = await service.delete_record(record_id)
        return ApiResponse(
            success=True,
            message=result["message"],
            data={"record_id": record_id},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"删除记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"删除记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.post("/records/{record_id}/restore", response_model=ApiResponse)
async def restore_record(
    record_id: str = Path(..., description="记录ID"),
    service: UserRecordService = Depends(get_record_service)
):
    """
    恢复已删除的用户记录
    
    将已删除的记录恢复为正常状态
    只能恢复通过软删除标记的记录
    
    Args:
        record_id: 要恢复的记录ID
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含恢复后的用户记录信息
        
    Raises:
        HTTPException: 当记录不存在或恢复失败时抛出
    """
    try:
        record = await service.restore_record(record_id)
        
        record_data = {
            "id": str(record.id),  # 确保 UUID 对象转换为字符串
            "user_id": str(record.user_id),
            "content": record.content,
            "content_type": record.content_type,
            # "tags": record.tags or [],
            "file_ids": record.file_ids or [],
            "is_deleted": record.is_deleted,
            "created_at": record.created_at.isoformat() if record.created_at else "",
            "updated_at": record.updated_at.isoformat() if record.updated_at else ""
        }
        
        return ApiResponse(
            success=True,
            message="用户记录恢复成功",
            data={"record": record_data},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"恢复记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"恢复记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

@router.get("/users/{user_id}/records/count", response_model=ApiResponse)
async def count_user_records(
    user_id: str = Path(..., description="用户ID"),
    service: UserRecordService = Depends(get_record_service)
):
    """
    统计用户记录总数
    
    返回指定用户的记录总数统计（不包括已删除的记录）
    
    Args:
        user_id: 用户ID
        service: 用户记录服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含记录总数统计
        
    Raises:
        HTTPException: 当统计失败时抛出
    """
    try:
        count = await service.count_user_records(user_id)
        return ApiResponse(
            success=True,
            message=f"用户记录统计完成，共{count}条记录",
            data={"count": count, "user_id": user_id},
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"统计用户记录失败: {e}")
        return ApiResponse(
            success=False,
            message=f"统计用户记录失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        ) 
"""
系统健康检查和状态路由

提供系统状态监控和健康检查的API端点
包括数据库连接状态、服务状态等信息
"""

import asyncio
import logging
from typing import Dict
from fastapi import APIRouter, HTTPException, status

logger = logging.getLogger(__name__)

# 创建健康检查路由器
router = APIRouter()

# 用于存储数据库管理器引用
_db_manager = None

def set_db_manager(db_manager):
    """设置数据库管理器引用"""
    global _db_manager
    _db_manager = db_manager

@router.get("/", response_model=Dict[str, str])
async def root():
    """
    根路径，返回API信息
    
    Returns:
        Dict[str, str]: API基本信息
    """
    return {
        "message": "亲子关系分析API服务",
        "version": "1.0.0",
        "status": "running"
    }

@router.get("/health", response_model=Dict[str, str])
async def health_check():
    """
    健康检查接口
    
    检查服务和数据库连接状态，用于负载均衡器和监控系统
    
    Returns:
        Dict[str, str]: 健康状态信息
        
    Raises:
        HTTPException: 当服务不可用时抛出503错误
    """
    try:
        # 检查数据库连接
        if _db_manager:
            async with _db_manager.get_session() as session:
                # 执行简单查询测试数据库连接
                await session.fetchval("SELECT 1")
        
        return {
            "status": "healthy",
            "database": "connected",
            "service": "parent_child_analysis",
            "timestamp": str(asyncio.get_event_loop().time())
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="服务不可用"
        )

@router.get("/version", response_model=Dict[str, str])
async def get_version():
    """
    获取API版本信息
    
    Returns:
        Dict[str, str]: 版本信息
    """
    return {
        "api_version": "1.0.0",
        "service": "parent_child_analysis",
        "description": "亲子关系分析API服务"
    } 
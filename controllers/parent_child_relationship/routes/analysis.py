"""
分析路由
处理育儿分析相关的API请求，包括记录分析和追问分析
"""

import logging
from pydantic import BaseModel, Field
from fastapi import APIRouter
from controllers.parent_child_relationship.api.analysis import AnalysisService, get_analysis_service
from controllers.common.api.auth import ApiResponse
from fastapi import Depends
from datetime import datetime
from database.utils import get_current_time_iso
from controllers.common.api.auth import get_jwt_service
from controllers.common.services.jwt_service import extract_token_from_header, JwtService
from fastapi import Request, HTTPException, status
from tools.parenting.narrative_organizer import organize_narrative
from typing import Optional, Dict, List, Any

router = APIRouter()

# 配置日志
logger = logging.getLogger(__name__)

# Pydantic 模型定义，用于API请求和响应的数据验证


# 创建分析请求模型
class AnalysisRequest(BaseModel):
    record_id: str = Field(..., description="记录ID")

# 创建是否追问请求模型
class FollowUpRequest(BaseModel):
    content: str = Field(..., description="内容")

# 创建叙事整理请求模型
class NarrativeRequest(BaseModel):
    first_response: str = Field(..., description="首次回应内容")
    supplement_response: str = Field(None, description="补充回应内容（可选）")

class InsightRequest(BaseModel):
    narrative_text: str = Field(..., description="整理后的完整育儿叙事文本")

class SaveInsightRequest(BaseModel):
    record_id: str = Field(..., description="关联的记录ID")
    content: dict = Field(..., description="洞察卡片内容")

class DeleteInsightRequest(BaseModel):
    insight_id: str = Field(..., description="洞察卡片ID")

class RegenerateInsightRequest(BaseModel):
    record_id: str = Field(..., description="记录ID")
    additional_content: str = Field(..., description="追加的新内容")

async def get_current_user_id(
    request: Request,
    jwt_service: JwtService = Depends(get_jwt_service)
) -> str:
    """
    从JWT token中获取当前用户ID（必需）
    
    Args:
        request: FastAPI请求对象
        jwt_service: JWT服务实例
        
    Returns:
        str: 用户ID
        
    Raises:
        HTTPException: 当未认证或认证失败时抛出401错误
    """
    try:
        # 从请求头获取Authorization token
        authorization = request.headers.get("Authorization")
        if not authorization:
            logger.warning("请求缺少Authorization头部")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未提供认证令牌"
            )
        
        # 提取token
        # from ..services.jwt_service import extract_token_from_header
        token = extract_token_from_header(authorization)
        
        if not token:
            logger.warning("Token格式错误")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌格式错误"
            )
        
        # 验证token并获取用户信息
        user_info = jwt_service.get_user_from_token(token)
        
        if not user_info:
            logger.warning("Token验证失败")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌无效"
            )
        
        user_id = user_info.get("user_id")
        if not user_id:
            logger.warning("Token中缺少用户ID信息")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证令牌中缺少用户信息"
            )
        
        logger.info(f"用户认证成功: {user_id}")
        return user_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户ID失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证验证失败"
        )

# 创建分析路由
@router.post("/analysis", response_model=ApiResponse)
async def analyze_record(request: AnalysisRequest, service: AnalysisService = Depends(get_analysis_service)):
    """
    分析用户记录
    
    对指定的用户记录进行智能分析，返回分析结果
    
    Args:
        request: 分析请求数据，包含记录ID
        service: 分析服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含分析结果
        
    Raises:
        HTTPException: 当记录不存在或分析失败时抛出
    """
    try:
        result = await service.analyze_record(request.record_id)
        
        return ApiResponse(
            success=True,
            message="记录分析完成",
            data=result,
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"分析失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )
    
# 返回初始问题
@router.get("/analysis/initial-question", response_model=ApiResponse)
async def get_initial_question():
    """
    获取初始问题
    """
    return ApiResponse(
        success=True,
        message="success",
        data={
            "question": "育儿生活充满了无数值得回味的瞬间。试着回想**一个最近的场景**：在这个场景里，您可能感觉与孩子的关系更近了一步，或者，您更深地理解了孩子的某个想法或行为。当然，一个让您困惑、渴望更懂他的挑战瞬间，我们也同样期待倾听。",
        },
        timestamp=get_current_time_iso()
    )
    
# 创建追问分析接口
@router.post("/analysis/follow-up", response_model=ApiResponse)
async def analyze_follow_up_content(request: FollowUpRequest, user_id: str = Depends(get_current_user_id), service: AnalysisService = Depends(get_analysis_service)):
    """
    分析用户内容并判断是否需要追问
    
    基于S-A-R-F深度洞察模型分析用户提供的内容，
    智能判断信息完整性并决定是否需要进行针对性追问
    
    Args:
        request: 追问请求数据，包含用户内容
        service: 分析服务依赖注入
        
    Returns:
        ApiResponse: 标准API响应，data字段包含追问分析结果
        
    追问分析结果格式：
        - type: "question" 或 "acknowledgement"，表示是否需要追问
        - content: 给用户的回复内容（追问或确认）
        - reasoning: 分析推理过程
        - need_follow_up: 布尔值，是否需要追问
        - metadata: 包含S-A-R-F完整性评估等额外信息
    """
    # try:
    # 验证请求内容
    if not request.content or len(request.content.strip()) < 2:
        return ApiResponse(
            success=False,
            message="请提供有效的内容进行分析",
            data=None,
            timestamp=get_current_time_iso()
        )
    # user_id = await get_current_user_id(request)

    logger.info(f"用户ID: {user_id}")
    
    # 调用分析服务进行追问分析
    result = await service.analyze_follow_up_content(user_id, request.content)
    logger.info(f"追问分析结果: {result}")
    
    return ApiResponse(
        success=True,
        message="追问分析完成",
        data=result,
        timestamp=get_current_time_iso()
    )
        
    # except Exception as e:
    #     logger.error(f"追问分析接口调用失败: {str(e)}")
    #     return ApiResponse(
    #         success=False,
    #         message=f"追问分析失败: {str(e)}",
    #         data=None,
    #         timestamp=get_current_time_iso()
    #     )

# 创建叙事整理接口
@router.post("/analysis/organize-narrative", response_model=ApiResponse)
async def organize_narrative_content(
    request: NarrativeRequest,
    user_id: str = Depends(get_current_user_id)
):
    """
    整理育儿故事叙事
    
    将用户提供的可能分段的育儿经历描述，整合成一段单一、完整、通顺的叙事文本
    
    Args:
        request: 叙事整理请求数据，包含首次回应和可选的补充回应
        user_id: 当前用户ID
        
    Returns:
        ApiResponse: 标准API响应，data字段包含整理后的故事文本
        
    叙事整理结果格式：
        - narrative_card_text: 整理后的完整故事文本
        - organization_timestamp: 整理时间戳
        - tool_version: 工具版本
    """
    try:
        # 验证请求内容
        if not request.first_response or len(request.first_response.strip()) < 10:
            return ApiResponse(
                success=False,
                message="请提供至少10个字符的首次回应内容",
                data=None,
                timestamp=get_current_time_iso()
            )
        
        logger.info(f"用户 {user_id} 请求叙事整理")
        
        # 调用叙事整理工具
        result = await organize_narrative(
            first_response=request.first_response,
            supplement_response=request.supplement_response
        )
        
        logger.info(f"叙事整理结果: {result}")
        
        # 解析结果
        import json
        try:
            result_data = json.loads(result)
            
            # 检查是否有错误
            if "error" in result_data:
                return ApiResponse(
                    success=False,
                    message=result_data.get("error", "叙事整理失败"),
                    data=result_data,
                    timestamp=get_current_time_iso()
                )
            
            return ApiResponse(
                success=True,
                message="叙事整理完成",
                data=result_data,
                timestamp=get_current_time_iso()
            )
            
        except json.JSONDecodeError:
            logger.error("叙事整理结果JSON解析失败")
            return ApiResponse(
                success=False,
                message="叙事整理结果格式错误",
                data=None,
                timestamp=get_current_time_iso()
            )
        
    except Exception as e:
        logger.error(f"叙事整理接口调用失败: {str(e)}")
        return ApiResponse(
            success=False,
            message=f"叙事整理失败: {str(e)}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 创建洞察生成接口
@router.post("/analysis/insight", response_model=ApiResponse)
async def generate_parenting_insight_api(
    request: InsightRequest,
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    生成育儿洞察卡片

    根据整理后的完整育儿故事文本，调用洞察生成工具，
    返回洞察标题、内容及行动建议。
    """
    # 校验输入
    if not request.narrative_text or len(request.narrative_text.strip()) < 10:
        return ApiResponse(
            success=False,
            message="请提供至少10个字符的完整育儿叙事文本",
            data=None,
            timestamp=get_current_time_iso()
        )

    logger.info(f"用户 {user_id} 请求生成育儿洞察")

    try:
        result = await service.generate_parenting_insight(user_id, request.narrative_text)
        return ApiResponse(
            success=True,
            message="育儿洞察生成完成",
            data=result,
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"洞察生成失败: {e}")
        return ApiResponse(
            success=False,
            message=f"洞察生成失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 收藏洞察卡片接口
@router.post("/insights/save", response_model=ApiResponse)
async def save_insight_card(
    request: SaveInsightRequest,
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    保存洞察卡片并设置为收藏状态
    """
    try:
        result = await service.save_insight_card(user_id, request.record_id, request.content)
        return ApiResponse(
            success=True,
            message="洞察卡片已保存并收藏",
            data=result,
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"保存洞察卡片失败: {e}")
        return ApiResponse(
            success=False,
            message=f"保存洞察卡片失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 获取洞察卡片列表接口
@router.get("/insights", response_model=ApiResponse)
async def get_insight_cards(
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    获取用户的洞察卡片列表(仅收藏状态)，关联用户记录信息
    """
    try:
        result = await service.get_insight_cards(user_id)
        return ApiResponse(
            success=True,
            message="获取洞察卡片列表成功",
            data={"insights": result},   # 用一个key包裹
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"获取洞察卡片列表失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取洞察卡片列表失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 获取按记录ID分组的洞察列表接口
@router.get("/insights/grouped", response_model=ApiResponse)
async def get_grouped_insights(
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    获取按记录ID分组的洞察卡片列表
    
    返回用户的所有洞察卡片，按记录ID进行分组，方便前端展示
    
    Returns:
        ApiResponse: 标准API响应，data字段包含按record_id分组的洞察数据
        {
            "grouped_insights": {
                "record_id_1": [insight1, insight2, ...],
                "record_id_2": [insight1, insight2, ...],
                ...
            }
        }
    """
    try:
        from database import DatabaseManager
        from database.models.insight import Insight
        from database.models.user_record import UserRecord
        
        db_manager = service.db_manager
        
        async with db_manager.get_session() as session:
            # 查询用户的所有收藏状态的洞察卡片
            query = """
                SELECT 
                    i.id,
                    i.user_id,
                    i.record_id,
                    i.content,
                    i.status,
                    i.created_at,
                    i.updated_at,
                    ur.content as record_content,
                    ur.content_type as record_content_type,
                    ur.created_at as record_created_at
                FROM insights i
                LEFT JOIN user_records ur ON i.record_id = ur.id::text
                WHERE i.user_id = $1 AND i.status = 1
                ORDER BY i.record_id, i.created_at DESC
            """
            
            rows = await session.fetch(query, user_id)
            
            # 按record_id分组
            grouped_insights = {}
            
            for row in rows:
                record_id = str(row["record_id"]) if row["record_id"] else "no_record"
                
                insight_data = {
                    "id": str(row["id"]),
                    "user_id": str(row["user_id"]),
                    "record_id": record_id,
                    "content": row["content"],
                    "status": row["status"],
                    "created_at": row["created_at"].isoformat() if row["created_at"] else "",
                    "updated_at": row["updated_at"].isoformat() if row["updated_at"] else "",
                    "record_info": {
                        "content": row["record_content"],
                        "content_type": row["record_content_type"],
                        "created_at": row["record_created_at"].isoformat() if row["record_created_at"] else ""
                    } if row["record_content"] else None
                }
                
                if record_id not in grouped_insights:
                    grouped_insights[record_id] = []
                
                grouped_insights[record_id].append(insight_data)
            
            logger.info(f"获取按记录分组的洞察: user_id={user_id}, 记录数={len(grouped_insights)}")
            
            return ApiResponse(
                success=True,
                message="获取按记录ID分组的洞察列表成功",
                data={"grouped_insights": grouped_insights},
                timestamp=get_current_time_iso()
            )
            
    except Exception as e:
        logger.error(f"获取按记录ID分组的洞察列表失败: {e}")
        return ApiResponse(
            success=False,
            message=f"获取按记录ID分组的洞察列表失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 删除洞察卡片接口
@router.delete("/insights/{insight_id}", response_model=ApiResponse)
async def delete_insight_card(
    insight_id: str,
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    删除收藏的洞察卡片
    """
    try:
        result = await service.delete_insight_card(user_id, insight_id)
        return ApiResponse(
            success=True,
            message="洞察卡片已删除",
            data=result,
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"删除洞察卡片失败: {e}")
        return ApiResponse(
            success=False,
            message=f"删除洞察卡片失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 重新生成洞察接口
@router.post("/insights/regenerate", response_model=ApiResponse)
async def regenerate_insight(
    request: RegenerateInsightRequest,
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    重新生成洞察(在当前记录基础上追加新内容)
    """
    try:
        result = await service.regenerate_insight(user_id, request.record_id, request.additional_content)
        return ApiResponse(
            success=True,
            message="重新生成洞察成功",
            data=result,
            timestamp=get_current_time_iso()
        )
    except Exception as e:
        logger.error(f"重新生成洞察失败: {e}")
        return ApiResponse(
            success=False,
            message=f"重新生成洞察失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

# 创建更多洞察请求模型
class MoreInsightRequest(BaseModel):
    record_id: str = Field(..., description="记录ID")
    user_question: str = Field(..., description="用户追问内容")

# 更多洞察接口
@router.post("/insights/more", response_model=ApiResponse)
async def generate_more_insight(
    request: MoreInsightRequest,
    user_id: str = Depends(get_current_user_id),
    service: AnalysisService = Depends(get_analysis_service)
):
    """
    生成更多洞察(深度洞察模式)
    
    基于原始记录和首次洞察结果，结合用户追问生成深度洞察
    
    Args:
        request: 更多洞察请求，包含记录ID和用户追问
        user_id: 当前用户ID
        service: 分析服务实例
        
    Returns:
        ApiResponse: 标准API响应，包含深度洞察结果
    """
    try:
        # 校验输入
        if not request.user_question or len(request.user_question.strip()) < 5:
            return ApiResponse(
                success=False,
                message="请提供至少5个字符的追问内容",
                data=None,
                timestamp=get_current_time_iso()
            )
        
        logger.info(f"用户 {user_id} 请求更多洞察: record_id={request.record_id}")
        
        # 调用分析服务生成深度洞察
        result = await service.generate_more_insight(
            user_id=user_id,
            record_id=request.record_id,
            user_question=request.user_question
        )
        
        return ApiResponse(
            success=True,
            message="深度洞察生成完成",
            data=result,
            timestamp=get_current_time_iso()
        )
        
    except Exception as e:
        logger.error(f"生成更多洞察失败: {e}")
        return ApiResponse(
            success=False,
            message=f"生成更多洞察失败: {e}",
            data=None,
            timestamp=get_current_time_iso()
        )

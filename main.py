#!/usr/bin/env python3
"""
关系分析 MCP 服务器 - MCP官方规范兼容版本

使用FastMCP 2.x的官方规范架构，支持streamable-http传输
专注于MCP服务器和原子工具管理，WebSocket服务器通过pm2独立管理
"""
import os
import asyncio
import logging
import signal
from fastmcp import FastMCP
from core.service_manager import service_manager
from config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def shutdown_handler():
    """优雅关闭处理器"""
    logger.info("正在优雅关闭MCP服务...")
    try:
        # 关闭MCP服务器
        await service_manager.shutdown()
        logger.info("MCP服务关闭完成")
    except Exception as e:
        logger.error(f"关闭服务时出错: {str(e)}")

def setup_signal_handlers(loop, shutdown_handler):
    """设置异步信号处理器"""
    def signal_handler_sync(signum, frame):
        logger.info(f"收到信号 {signum}，开始关闭...")
        # 在当前事件循环中安排关闭任务
        loop.create_task(shutdown_handler())
    
    signal.signal(signal.SIGINT, signal_handler_sync)
    signal.signal(signal.SIGTERM, signal_handler_sync)

async def main():
    """主函数 - 启动MCP规范兼容的服务器，专注于原子工具管理"""
    try:
        logger.info(f"启动 {settings.server.name} MCP服务器")
        logger.info("专注于: 原子工具管理 + MCP 2.x 服务器")
        logger.info("WebSocket服务器请使用pm2独立管理")
        
        # 获取当前事件循环
        loop = asyncio.get_event_loop()
        
        # 设置信号处理器
        setup_signal_handlers(loop, shutdown_handler)
        
        # 🎯 初始化服务管理器（自动注册所有工具和工作流）
        await service_manager.initialize()
        
        # 🎯 获取已创建的MCP服务器（包含所有已注册的工具）
        mcp = service_manager.mcp_server
        
        # 获取并显示服务信息
        info = service_manager.get_info()
        logger.info(f"已注册工具: {info['tools']['total_tools']}")
        logger.info(f"已注册工作流: {info['workflows']['total']}")
        
        # 🎯 使用FastMCP 2.x的streamable-http传输方式
        logger.info("="*60)
        logger.info("🚀 MCP服务器启动完成")
        logger.info("📡 MCP服务器: http://0.0.0.0:8080/mcp")
        logger.info("🔧 原子工具管理: 已加载所有工具")
        logger.info("")
        logger.info("📌 WebSocket服务器管理:")
        logger.info("   使用: pm2 start ecosystem.config.js")
        logger.info("   查看: pm2 status")
        logger.info("   停止: pm2 stop all")
        logger.info("="*60)
        
        # 启动MCP服务器
        await mcp.run_async(
            transport="streamable-http",
            host="0.0.0.0",
            port=8080,
            path="/mcp"
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号...")
    except Exception as e:
        logger.error(f"启动服务器时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        await shutdown_handler()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 MCP服务器已停止")
    except Exception as e:
        logger.error(f"应用程序错误: {str(e)}")
        import traceback
        traceback.print_exc() 
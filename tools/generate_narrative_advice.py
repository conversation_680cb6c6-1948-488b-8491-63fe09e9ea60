"""
叙事建议工具

检测标注结果中的负面情绪，若有则用更中正平和的风格改写用户叙述
"""
import logging
from typing import List, Dict, Any, Optional
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_narrative_advice_prompt(user_text_input: str) -> str:
    """
    生成 narrative advice 改写提示，引导LLM将用户叙述改写为更中正平和的表达。
    
    Args:
        user_text_input: 用户的原始叙述文本
        
    Returns:
        用于LLM改写的完整提示词
    """
    return f"""
想象一下，你是原始叙述者本人，但你现在的情绪已经完全平复了，变得非常冷静、理性和客观。请你用第一人称"我"的口吻，以这种平静且充满理解的心态，重新叙述一下之前发生的这件事。目标是清晰地表达发生了什么以及"我"当时的感受（但用平静的方式回顾这些感受），而不是再次陷入当时的情绪中，也不指责他人。

原始叙述（当时充满情绪的"我"）：{user_text_input}

平复心情后，"我"的叙述：
"""

@service_manager.tool(name="generate_narrative_advice", category="表达改进")
async def generate_narrative_advice(user_text: str, annotations: Optional[List[Dict[str, Any]]] = None) -> str:
    """
    检测标注结果中的负面情绪，若有则用更中正平和的风格改写用户叙述。
    
    Args:
        user_text: 用户输入的关系文本
        annotations: 可选参数，文本的情绪标注结果
        
    Returns:
        改写后的中正平和的叙述，或提示信息
    """
    logger.info("Tool called: generate_narrative_advice")
    
    # 定义负面情绪类型
    negative_emotions = ["anger", "sadness", "fear", "disgust"]
    negative_found = False

    # 如果没有传入标注，自动调用 annotate_relationship_text
    if annotations is None:
        try:
            # 导入标注工具（避免循环导入）
            from .annotate_relationship_text import annotate_relationship_text
            annotations = await annotate_relationship_text(user_text)
        except Exception as e:
            logger.error(f"自动标注失败: {e}")
            return "无法分析情绪，请稍后再试。"

    # 检查负面情绪
    for segment in annotations:
        emotions = segment.get("emotions", {})
        for emo in negative_emotions:
            score = 0
            emo_val = emotions.get(emo)
            if isinstance(emo_val, dict):
                score = emo_val.get("score", 0)
            elif isinstance(emo_val, (int, float)):
                score = emo_val
            if score > 0.3:  # 阈值：超过0.3认为是明显的负面情绪
                negative_found = True
                break
        if negative_found:
            break

    # 如果没有检测到明显的负面情绪，返回提示
    if not negative_found:
        return "未检测到明显的负面情绪，您的叙述已经较为平和。"

    # 使用 prompt 函数生成改写提示
    try:
        prompt = generate_narrative_advice_prompt(user_text)
        response = await chat(
            prompt=prompt, 
            max_tokens=512, 
            temperature=0.3  # 较低温度确保稳定性
        )
        
        # 清理响应，提取主要内容
        result = response.strip()
        
        # 如果响应包含多行，取第一行作为主要改写结果
        if '\n' in result:
            result = result.split('\n')[0].strip()
        
        # 确保改写结果不为空
        if not result:
            return "改写过程中出现问题，请稍后再试。"
            
        logger.info(f"Successfully generated narrative advice, length: {len(result)}")
        return result
        
    except Exception as e:
        logger.error(f"generate_narrative_advice error: {e}")
        return "很抱歉，改写过程中出现问题。请稍后再试。" 
"""
伴侣人格分析工具

分析用户文本中描述的伴侣特质和行为模式，基于专业心理学框架提供洞察
"""
import logging
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_partner_persona_prompt(user_text: str) -> str:
    """
    生成伴侣特质分析的提示，基于心理学框架分析伴侣人格特质
    
    Args:
        user_text: 用户输入的关系文本
        
    Returns:
        用于LLM分析的完整提示词
    """
    return f"""你是一位冷静、理性、客观的两性关系专家，具备心理学背景。

【任务】
- 阅读用户对恋爱对象的描述，洞察其人格特质。
- **仅输出 1 个专业心理学关键词 + 简洁定义**，用破折号 "-" 连接。  
  例：高支配性 — 倾向控制他人

【关键词要求】
- 关键词必须来自公认心理学框架或概念：  
  - 大五人格（外向性、宜人性、责任心、开放性、神经质）  
  - 依恋类型（安全型、回避型、焦虑型）  
  - 黑暗三性格（马基雅维利主义、精神病质、纳粹主义）  
  - 其它学术认可维度（自尊、内外控、支配性等）
- 避免俗称或诊断术语（如「渣男」「NPD」）。
- 定义 ≤ 15 字，描述该特质在亲密关系中的典型表现。

【输出格式（务必只一行）】
关键词 — 定义

【交互规则】
1. 信息充分 → 直接输出关键词行。  
2. 信息不足 → 先提 1–2 个开放式澄清问题。  
3. 不提供医疗或法律建议；保持专业、同理。

【示例】
用户：他总帮我安排生活细节，说这样我省心。  
回复：高支配性 — 喜欢替伴侣做决定

现在请分析以下描述：

用户描述：{user_text}"""

@service_manager.tool(name="analyze_partner_persona", category="人格分析")
async def analyze_partner_persona(user_text: str) -> str:
    """
    分析用户文本中描述的伴侣特质和行为模式，基于专业心理学框架提供洞察。
    
    Args:
        user_text: 用户输入的关系文本
        
    Returns:
        基于心理学框架的伴侣特质分析（格式：关键词 — 定义）
    """
    logger.info("Tool called: analyze_partner_persona")
    
    # 如果文本过短，返回澄清问题
    if len(user_text.strip()) < 5:
        return "描述信息较少，能否详细说说您伴侣在日常互动中的具体表现？或者分享一个最近发生的具体事例？"
    
    try:
        # 使用prompt函数生成提示词
        full_prompt = analyze_partner_persona_prompt(user_text)
        
        # 调用LLM进行分析
        response = await chat(
            prompt=full_prompt,
            max_tokens=200,
            temperature=0.3  # 较低温度确保专业性和一致性
        )
        
        # 清理响应
        response = response.strip()
        
        # 检查是否是澄清问题（包含问号且不是关键词格式）
        if "？" in response and "—" not in response:
            return response
        
        # 检查是否符合预期格式（关键词 — 定义）
        if "—" in response:
            # 提取第一行（如果有多行）
            first_line = response.split('\n')[0].strip()
            if "—" in first_line:
                return first_line
        
        # 如果格式不符合预期，尝试从回应中提取有用信息
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if "—" in line:
                return line
        
        # 如果完全不符合格式，返回备用分析
        logger.warning(f"LLM response format unexpected: {response[:100]}")
        return "需要更多具体信息才能进行准确的心理学特质分析。能否分享一些您伴侣的具体行为表现？"
        
    except Exception as e:
        logger.error(f"分析伴侣特质时出错: {str(e)}")
        return "很抱歉，分析过程中出现问题。请稍后再试或提供更多具体的行为描述。" 
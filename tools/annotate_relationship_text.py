"""
关系文本标注工具

对用户输入的关系文本进行结构化标注和情绪分析
"""
import logging
import json
import re
from typing import List, Dict, Any
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def annotate_relationship_text_prompt(user_text: str) -> str:
    """
    生成关系文本标注分析的提示
    
    Args:
        user_text: 用户输入的关系文本
        
    Returns:
        用于LLM分析的完整提示词
    """
    return f"""你是一位专业的心理学家，擅长分析人际关系中的行为模式和情绪表达。
分析以下文本中描述的关系行为和情绪，并提供结构化标注。

标注要求：
1. 识别主要行为模式，如：控制行为、批评行为、回避行为、支持行为、防御行为等
2. 分析行为背后的6种核心情绪：愤怒、悲伤、恐惧、喜悦、厌恶、中性。
3. 每一段情绪文本背后的情绪都给出0-1区间中的打分。
4. 为每种情绪提供置信区间，表示你判断的确定性

返回格式必须是严格的JSON：
{{
  "text_segment": "被分析的文本",
  "behavior_tag": "主要行为标签",
  "behavior_description": "行为的简要描述",
  "emotions": {{
    "anger": {{"score": 0.7, "confidence_interval": [0.6, 0.8]}},
    "sadness": {{"score": 0.3, "confidence_interval": [0.2, 0.4]}},
    "fear": {{"score": 0.2, "confidence_interval": [0.4, 0.6]}},
    "joy": {{"score": 0.1, "confidence_interval": [0.05, 0.15]}},
    "disgust": {{"score": 0.5, "confidence_interval": [0.1, 0.3]}},
    "neutral": {{"score": 0.4, "confidence_interval": [0.3, 0.5]}}
  }}
}}

确保JSON格式严格正确，不要添加任何额外文本。置信区间应该是两个元素的数组，表示下限和上限。

注意：如果文本表达的是平静、客观或情感不明显的状态，"neutral"情绪的得分应该较高。
所有情绪得分的总和不必为1，因为情绪可以同时存在。

文本：{user_text}"""

@service_manager.tool(name="annotate_relationship_text", category="文本分析")
async def annotate_relationship_text(user_text: str) -> List[Dict[str, Any]]:
    """
    使用LLM分析用户文本，标注其中描述的行为、行为背后的情绪及置信度。
    
    Args:
        user_text: 用户输入的关系文本
        
    Returns:
        标注列表，包含文本段落、行为标签、情绪分析及置信度
    """
    logger.info("Tool called: annotate_relationship_text")
    
    # 文本太短时的简单处理
    if len(user_text.strip()) < 5:
        return [{
            "text_segment": user_text,
            "behavior_tag": "过短文本",
            "behavior_description": "文本内容过短，无法进行有效分析",
            "emotions": {
                "neutral": {"score": 1.0, "confidence_interval": [0.95, 1.0]},
                "anger": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                "sadness": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                "fear": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                "joy": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                "disgust": {"score": 0.0, "confidence_interval": [0.0, 0.0]}
            }
        }]
    
    # 将长文本分段处理
    segments = []

    # 简单的文本分段 - 如果文本超过500字符，分段处理
    if len(user_text) > 500:
        chunks = [user_text[i:i+500] for i in range(0, len(user_text), 500)]
    else:
        chunks = [user_text]
    
    for chunk in chunks:
        try:
            # 使用prompt函数生成提示词
            full_prompt = annotate_relationship_text_prompt(chunk)
            
            # 调用LLM进行分析
            response = await chat(
                prompt=full_prompt,
                max_tokens=2000,
                temperature=0.2  # 低温度以获得更确定的结果
            )
            
            # 尝试解析JSON响应
            try:
                # 找到并提取JSON部分
                json_match = re.search(r'(\{.*\})', response.replace('\n', ' '), re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    segment_data = json.loads(json_str)
                else:
                    raise ValueError("无法从LLM响应中提取JSON")
                
                # 验证JSON结构
                required_keys = ["text_segment", "behavior_tag", "emotions"]
                if not all(key in segment_data for key in required_keys):
                    raise ValueError(f"JSON缺少必要字段: {required_keys}")
                
                # 验证情绪结构
                emotions = segment_data["emotions"]
                expected_emotions = ["anger", "sadness", "fear", "joy", "disgust", "neutral"]
                
                # 确保所有情绪都有正确的结构
                for emotion in expected_emotions:
                    if emotion not in emotions:
                        emotions[emotion] = {"score": 0.0, "confidence_interval": [0.0, 0.1]}
                    elif "score" not in emotions[emotion]:
                        emotions[emotion]["score"] = 0.0
                    elif "confidence_interval" not in emotions[emotion]:
                        emotions[emotion]["confidence_interval"] = [
                            max(0.0, emotions[emotion]["score"] - 0.1),
                            min(1.0, emotions[emotion]["score"] + 0.1)
                        ]
                
                # 添加到结果列表
                segments.append(segment_data)
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"解析LLM响应时出错: {str(e)}")
                # 使用备用方案
                segments.append({
                    "text_segment": chunk,
                    "behavior_tag": "解析错误",
                    "behavior_description": "无法解析LLM响应",
                    "emotions": {
                        "anger": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                        "sadness": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                        "fear": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                        "joy": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                        "disgust": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                        "neutral": {"score": 1.0, "confidence_interval": [0.9, 1.0]}
                    },
                    "raw_response": response[:200]  # 保存部分原始响应用于调试
                })
        
        except Exception as e:
            logger.error(f"调用LLM时出错: {str(e)}")
            # 使用备用方案
            segments.append({
                "text_segment": chunk,
                "behavior_tag": "处理错误",
                "behavior_description": f"处理错误: {str(e)}",
                "emotions": {
                    "anger": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                    "sadness": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                    "fear": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                    "joy": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                    "disgust": {"score": 0.0, "confidence_interval": [0.0, 0.0]},
                    "neutral": {"score": 1.0, "confidence_interval": [0.9, 1.0]}
                }
            })
    
    logger.info(f"完成文本标注，共处理 {len(segments)} 个文本段落")
    return segments 
"""
育儿故事整理工具

将用户提供的、可能分段的育儿经历描述，整合成一段单一、完整、通顺的叙事文本
"""
import logging
import json
from datetime import datetime
from core.service_manager import service_manager
from database.utils import get_current_time_iso
from llm.qwen import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_narrative_organizer_prompt(first_response: str, supplement_response: str = None) -> str:
    """
    生成育儿故事整理的系统提示词
    
    Args:
        first_response: 首次回应
        supplement_response: 补充回应（可选）
        
    Returns:
        用于LLM整理的完整提示词
    """
    supplement_text = supplement_response if supplement_response else ""
    
    return f"""## 1. 角色与目标
你将扮演一名专业且富有同理心的**"育儿故事整理师"**。你的唯一目标是将用户提供的、可能分段的育儿经历描述，整合成一段**单一、完整、通顺**的叙事文本。这段文本将作为永久记录保存在一张精美的卡片上。

## 2. 输入信息
你将接收到以下一个或两个文本片段：

* `[首次回应]`: (必须) 用户对初始开放性问题的回答。
* `[补充回应]`: (可选) 用户在回答了追问之后提供的补充信息。**这个字段可能为空**，你需要能处理这种情况。

## 3. 核心任务与规则
你的核心任务是**"缝合"**与**"润色"**。请严格遵循以下规则：

### A. 叙事缝合 (Narrative Weaving)
* 将`[首次回应]`和`[补充回应]`视为一个完整故事的上下两部分。
* 你的任务是将这两部分自然地**串联**起来，形成一个有逻辑、有时间顺序的整体。通常，`[补充回应]`是对`[首次回应]`中某个细节的展开或后续，你需要找到它们之间的逻辑关系。
* 如果`[补充回应]`为空，则只需对`[首次回应]`进行润色。

### B. 忠实原文与轻度润色 (Faithful Polishing)
* **叙事视角：** 最终文本**必须**使用第一人称（"我"或"我们"），完全代入家长的口吻和视角。
* **内容忠实度：**
    * **严禁**添加任何原始回应中**未提及**的事实、细节、情感或猜测。
    * **严禁**进行过度解读或总结性评论（例如，不要加上"这件事说明了沟通很重要"这样的句子）。
    * **必须**保留用户原始描述中的核心事件、情绪和关键用词。
* **润色尺度：**
    * **可以**修正明显的错别字或语法错误。
    * **可以**调整语序，让句子之间的衔接更自然、更流畅（例如，使用"然后"、"接着"、"但是"等连接词）。
    * **可以**将口语化的表达（如"嗯"、"啊"）处理掉，使书面表达更精炼。

## 4. 输出格式要求
你的最终输出**必须**是一个严格的JSON对象，不包含任何JSON格式之外的额外文本。JSON对象应包含以下字段：

* `narrative_card_text`: (字符串) 经过你整理和润色后的、最终呈现给用户的完整故事文本。

### 5. 示例

#### 示例输入：
* **`[首次回应]`**: "晚饭时他不好好吃菜，我一气之下把他的碗收走了。"
* **`[补充回应]`**: "他一开始愣住了，然后就哇哇大哭，哭了有十几分钟。我后来看着也心疼，就抱着他安抚了好久，感觉自己处理得太急了。"

#### 你应输出的JSON：
```json
{{
  "narrative_card_text": "晚饭的时候，孩子不好好吃菜，我一气之下就把他的碗收走了。他一开始愣住了，接着就哇哇大哭，哭了十几分钟。看着他哭，我心里也特别心疼，最后抱着他安抚了很久。回想起来，感觉自己当时处理得太着急了。"
}}
```

#### 示例输入 (补充回应为空):
* **`[首次回应]`**: "今天下午在公园，他看到别的小朋友的沙滩桶就直接拿走了。我马上把他拉到一边，蹲下来告诉他'这是别人的，我们需要先问'。他一开始有点委屈，但后来真的跑去问那个小哥哥了，人家也同意了，俩人还一起玩了。"
* **`[补充回应]`**: ""

#### 你应输出的JSON：
```json
{{
  "narrative_card_text": "今天下午在公园里，他看到别的小朋友的沙滩桶就直接拿走了。我马上把他拉到一边，蹲下来告诉他：'这是别人的，我们需要先问才可以'。他一开始有点委屈，但后来真的跑去问了那个小哥哥，对方也同意了，两个孩子还一起开心地玩了起来。"
}}
```

[首次回应]
{first_response}

[补充回应]
{supplement_text}"""

@service_manager.tool(name="organize_narrative", category="育儿分析")
async def organize_narrative(first_response: str, supplement_response: str = None) -> str:
    """
    整理育儿故事叙事
    
    Args:
        first_response: 首次回应内容
        supplement_response: 补充回应内容（可选）
        
    Returns:
        包含整理后故事的JSON字符串
    """
    logger.info("Tool called: organize_narrative (育儿故事整理)")
    
    # 检查输入文本的有效性
    if not first_response or len(first_response.strip()) < 10:
        logger.warning("输入的首次回应文本过短")
        return json.dumps({
            "error": "请提供至少10个字符的首次回应内容",
            "suggestion": "建议包含具体的事件描述、孩子的行为表现以及您的感受"
        }, ensure_ascii=False, indent=2)
    
    try:
        # 生成完整的故事整理提示词
        full_prompt = generate_narrative_organizer_prompt(first_response, supplement_response)
        
        # 调用LLM进行故事整理
        logger.info("正在调用LLM进行故事整理...")
        response = await chat(
            prompt=full_prompt,
            max_tokens=800,   # 适中的token数量
            temperature=0.3   # 较低温度确保忠实度
        )
        
        # 清理响应文本
        response = response.strip()
        
        # 尝试提取JSON内容
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        
        if json_start != -1 and json_end > json_start:
            json_content = response[json_start:json_end]
            
            # 验证JSON格式
            try:
                parsed_json = json.loads(json_content)
                logger.info("成功解析故事整理结果")
                
                # 添加元数据
                parsed_json['organization_timestamp'] = get_current_time_iso()
                parsed_json['tool_version'] = "1.0.0"
                
                # 验证必要字段
                if 'narrative_card_text' not in parsed_json:
                    logger.error("响应中缺少必要的narrative_card_text字段")
                    return json.dumps({
                        "error": "故事整理结果格式不完整",
                        "suggestion": "请重新提交您的故事内容"
                    }, ensure_ascii=False, indent=2)
                
                return json.dumps(parsed_json, ensure_ascii=False, indent=2)
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                return json.dumps({
                    "error": "故事整理结果格式解析失败",
                    "raw_response": response[:500],
                    "suggestion": "请重新提交您的故事内容，确保内容清晰完整"
                }, ensure_ascii=False, indent=2)
        
        else:
            logger.error("响应中未找到有效的JSON结构")
            return json.dumps({
                "error": "未能生成有效的故事整理结果",
                "raw_response": response[:300],
                "suggestion": "请提供更详细的故事内容"
            }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"故事整理过程中发生错误: {str(e)}")
        
        # 返回备用的结构化响应
        fallback_response = {
            "error": "故事整理过程中遇到技术问题",
            "error_details": str(e),
            "suggestion": "请稍后重试，或检查故事内容是否包含足够的详细信息",
            "contact": "如问题持续，请联系技术支持"
        }
        
        return json.dumps(fallback_response, ensure_ascii=False, indent=2)

def validate_narrative_input(first_response: str, supplement_response: str = None) -> dict:
    """
    验证故事整理输入的基本格式和内容完整性
    
    Args:
        first_response: 首次回应文本
        supplement_response: 补充回应文本（可选）
        
    Returns:
        包含验证结果的字典
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "suggestions": []
    }
    
    # 检查首次回应长度
    if len(first_response.strip()) < 20:
        validation_result["warnings"].append("首次回应内容较短")
        validation_result["suggestions"].append("建议添加更多事件细节")
    
    # 检查是否包含常见的育儿关键词
    parenting_keywords = ["孩子", "宝宝", "儿子", "女儿", "小朋友", "宝贝"]
    if not any(keyword in first_response for keyword in parenting_keywords):
        validation_result["warnings"].append("文本中未检测到明确的儿童相关词汇")
        validation_result["suggestions"].append("请确认这是一篇关于育儿的故事")
    
    # 检查是否包含行为或情绪描述
    behavior_emotion_keywords = ["哭", "笑", "生气", "开心", "拒绝", "配合", "吵闹", "安静"]
    if not any(keyword in first_response for keyword in behavior_emotion_keywords):
        validation_result["suggestions"].append("可以添加更多关于孩子行为或情绪的描述")
    
    # 检查补充回应（如果提供）
    if supplement_response and len(supplement_response.strip()) > 0:
        if len(supplement_response.strip()) < 10:
            validation_result["warnings"].append("补充回应内容较短")
            validation_result["suggestions"].append("建议在补充回应中添加更多细节")
    
    return validation_result

def validate_narrative_output(narrative_text: str) -> dict:
    """
    验证生成的故事文本是否符合要求
    
    Args:
        narrative_text: 待验证的故事文本
        
    Returns:
        包含验证结果的字典
    """
    validation_result = {
        "is_valid": True,
        "issues": []
    }
    
    # 检查长度
    if len(narrative_text) < 30:
        validation_result["is_valid"] = False
        validation_result["issues"].append("故事文本过短")
    
    # 检查是否使用第一人称
    first_person_indicators = ["我", "我们"]
    if not any(indicator in narrative_text for indicator in first_person_indicators):
        validation_result["issues"].append("建议使用第一人称叙述")
    
    # 检查是否包含连接词
    connecting_words = ["然后", "接着", "但是", "后来", "最后", "接下来"]
    if not any(word in narrative_text for word in connecting_words):
        validation_result["issues"].append("建议添加更多连接词使叙述更流畅")
    
    return validation_result
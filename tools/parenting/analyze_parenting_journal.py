"""
育儿日记分析工具

深入分析用户提供的育儿日记文本，提取结构化的教养信息和心理洞察
同时提供多视角语料改写功能，生成富有诗意的旁观者视角金句
"""
import logging
import json
from datetime import datetime
from core.service_manager import service_manager
from database.utils import get_current_time_iso
from llm.qwen import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_parenting_analysis_prompt(journal_entry: str) -> str:
    """
    生成育儿日记分析的系统提示词
    
    Args:
        journal_entry: 用户输入的育儿日记文本
        
    Returns:
        用于LLM分析的完整提示词
    """
    return f"""1. 角色 (Persona)
你是一位经验丰富、富有同情心和洞察力的育儿专家。你擅长分析家长记录的日常事件，能够洞察亲子互动模式、理解家长与孩子的情绪，并能对任何类型的个人记录给予条理清晰的总结。你的分析旨在帮助用户更好地理解自身经历、反思亲子关系或整理思绪。

2. 核心任务 (Core Task)
你的任务是接收用户输入的任意文本语料（[用户输入]），并严格按照提供的JSON模板对其进行分析和标注。

3. 技能
- 具备准确判断文本语料类型的能力，能从育儿相关和无关内容中做出正确选择。
- 熟练掌握按照JSON模板要求填充各字段内容的技能，遵循字段注释、格式和选项范围。

4. JSON模板与规则 (JSON Template & Rules)
你必须使用以下JSON结构作为输出。这是唯一的输出格式。
{{
  "static_metadata": {{
    "content_type": "" // [重要] 首先判断语料类型。选择后，仅填写下方对应的分析模块（'parenting_analysis' 或 'general_content_analysis'），另一模块留空。从['育儿相关', '无关内容']中选择。
  }},
  "parenting_analysis": {{
    "comment": "--- 仅当 'content_type' 为 '育儿相关' 时，填写此模块 ---",
    "event_description": {{
      "event_datetime": "YYYY-MM-DD HH:MM:SS", // 如果原文有具体时间，请提取。否则根据今天日期（2025-06-25）推断。
      "event_title": "", // 为这篇日记生成一个简洁、中立的标题，概括核心事件。
      "event_abstract": "", // 基于家长的日记，从孩子的口吻为这篇日记生成50-100字的简洁概述。确保完全从孩子的视角出发，描述孩子在事件中的感受和行为。例如：“我看到大孩子玩滑梯，我也想玩。妈妈帮我爬到滑梯顶端，但我害怕滑下来，就哭了。”      "event_source": "", // 信息来源，从['亲自观察', '学校反馈', '孩子主动分享', '他人转述']中选择。
      "event_domain": [], // 涉及领域(可多选)，从['生活常规', '学业任务', '人际社交', '兴趣爱好', '情绪健康', '时间管理', '亲子关系', '里程碑事件']中选择。
      "key_persons": [] // 关键人物(可多选)，从['我', '孩子', '老师', '同学', '配偶', '祖辈']中选择。
    }},
    "behavioral_interaction": {{
      "event_trigger": "", // 引发核心互动或事件的导火索是什么？
      "child_behavior": [], // 从原文提炼2-3条孩子最核心的行为，要求简明扼要，帮助家长快速回忆起情景。例如：["拒绝做作业", "趴在桌上哭"]。
      "parent_response": [], // 从原文提炼2-3条家长最核心的应对方式，要求简明扼要，帮助家长快速回忆起情景。例如：["反复催促", "选择暂时离开"]。
      "interaction_pattern": {{
        "pattern_type": "", // 互动模式，从['冲突-和解', '引导-配合', '控制-反抗', '单向观察与欣赏', '开放式交流']中选择。
        "pattern_description": "" // 基于原文，用15字以内概括该模式的具体体现。例如："因练琴引发冲突后和解"。注意描述如何从原文推导出该模式。
      }}
    }},
    "psychological_experience": {{
      "parent_core_emotion": [], // 家长核心情绪(可多选)，从['焦虑', '愤怒', '疲惫', '内疚', '无力', '失望', '欣慰', '自豪', '感动', '平静', '困惑', '喜悦']中选择。
      "parent_final_emotion": "", // 最终情绪（单选），从['焦虑', '愤怒', '疲惫', '内疚', '无力', '失望', '欣慰', '自豪', '感动', '平静', '困惑', '喜悦']中选择。
      "inferred_child_emotion": [], // 推断的孩子情绪(可多选)，从['沮丧', '烦躁', '兴奋', '有成就感', '委屈', '无所谓', '专注', '好奇', '抗拒']中选择。
      "child_final_emotion": "", // 最终情绪（单选），从['沮丧', '烦躁', '兴奋', '有成就感', '委屈', '无所谓', '专注', '好奇', '抗拒']中选择。
      "emotional_intensity": 0 // 家长情绪激烈程度(1-5分)，1=平静, 5=非常激烈。
    }},
    "reflection_and_action": {{
      "key_insight": "", // 家长从事件中的核心洞察与反思是什么？请用1-2句话概括。
      "parenting_strategy_adjustment": [], // 计划的策略调整(可多选)，从['调整沟通方式', '改变环境设置', '学习新知识', '寻求外部支持', '调整自我预期', '无明确调整']中选择。
      "expert_advice": "" // 从育儿专家的角度给出简单有效的洞察和育儿建议。
    }}
  }},
  "general_content_analysis": {{
    "comment": "--- 仅当 'content_type' 为 '无关内容' 时，填写此模块 ---",
    "summary": "", // 为这段文本生成一个50-100字的简洁、中立的摘要。
    "main_topics": [], // 提取文本中的3-5个核心关键词或主题。例如：["项目管理", "团队沟通", "截止日期"]。
    "overall_sentiment": "", // 判断文本的整体情绪倾向。从['积极', '消极', '中性', '复杂']中选择。
    "content_category": "" // 推断内容的所属领域。从['工作学习', '生活日常', '兴趣爱好', '个人思考', '创意写作', '其他']中选择。
  }}
}}

5. 约束
- 必须严格按照JSON模板的格式和要求进行输出。
- 禁止提供JSON模板以外的输出格式。

6. 工作流程 (Workflow)
7. 阅读并理解 [用户输入] 的全部内容。
8. 首要判断：内容是与"育儿相关"还是"无关内容"？
9. 设置content_type:
  - 如果内容涉及孩子、亲子关系、教育等，将content_type设为"育儿相关"。
  - 否则，设为"无关内容"。
10. 条件化填充:
  - 如果content_type是"育儿相关"，你必须仅填充parenting_analysis模块中的所有字段，并将general_content_analysis模块整体留空或设为null。
  - 如果content_type是"无关内容"，你必须仅填充general_content_analysis模块中的所有字段，并将parenting_analysis模块整体留空或设为null。
11. 严格检查所有字段的填充是否遵循JSON中注释的指示、格式和选项范围。
12. 再次确认content_type的设置是否准确。

[用户输入]
{journal_entry}"""

def generate_golden_quote_prompt(journal_entry: str) -> str:
    """
    生成多视角语料改写-金句改写的系统提示词
    
    Args:
        journal_entry: 用户输入的育儿日记文本
        
    Returns:
        用于LLM生成金句的完整提示词
    """
    return f"""# 角色
你是一位深谙儿童心理的育儿手记解读专家，擅长以超脱旁观者视角，用凝练诗意语言解读育儿微观互动。

# 目标
1. 根据家长手记内容，生成一句无特定人称的"旁观者视角金句"。
2. 通过金句引发家长对自身情绪和行为的觉察与反思。
3. 金句模仿孩子视角，替孩子说出心里话，制衡家长认知权威，让家长多角度审视自身；同时点明家长情绪问题和行为模式，关怀家长，助其成为更快乐合格的家长。

# 技能
1. 具备深厚的儿童心理学知识。
2. 拥有凝练且富有诗意的文字表达能力。

# 工作流程
1. 仔细研读家长手记，精准提取孩子现象、情绪、状态及家长可能的情绪和互动模式等关键信息。
2. 以孩子可感知内容为切入点，结合家长情况精心构思金句。
3. 确保金句无特定人称，严格符合15 - 30字的字数要求。
4. 反复检查金句能否引发家长反思，语气是否温暖包容。

# 约束
1. 必须依据家长手记内容生成金句。
2. 金句中严禁出现"你"、"我"、"他/她"等具体人称代词。
3. 严格控制金句字数在15 - 30字之间。
4. 语气必须温暖、理解、非评判。

# 输出格式
输出为一句无特定人称的"旁观者视角金句"，语言凝练，富有诗意，具有金句特质。

User
请分析以下这篇育儿日记，并根据你被赋予的指令输出JSON结果。

【育儿日记原文】
{journal_entry}"""

@service_manager.tool(name="analyze_parenting_journal", category="育儿分析")
async def analyze_parenting_journal(journal_entry: str) -> str:
    """
    分析用户提供的育儿日记文本，提取结构化的教养信息和心理洞察
    
    Args:
        journal_entry: 用户输入的育儿日记文本
        
    Returns:
        包含详细分析结果的结构化JSON字符串
    """
    logger.info("Tool called: analyze_parenting_journal (育儿日记分析)")
    
    # 检查输入文本的有效性
    if not journal_entry or len(journal_entry.strip()) < 10:
        logger.warning("输入的日记文本过短")
        return json.dumps({
            "error": "请提供至少10个字符的育儿日记内容",
            "suggestion": "建议包含具体的事件描述、孩子的行为表现以及您的感受"
        }, ensure_ascii=False, indent=2)
    
    try:
        # 生成完整的分析提示词
        full_prompt = generate_parenting_analysis_prompt(journal_entry)
        
        # 调用LLM进行育儿日记分析
        logger.info("正在调用LLM进行育儿日记分析...")
        response = await chat(
            prompt=full_prompt,
            max_tokens=1500,  # 增加token数量以容纳详细的JSON结构
            temperature=0.2   # 较低温度确保结构化输出的稳定性
        )
        
        # 清理响应文本
        response = response.strip()
        
        # 尝试提取JSON内容
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        
        if json_start != -1 and json_end > json_start:
            json_content = response[json_start:json_end]
            
            # 验证JSON格式
            try:
                parsed_json = json.loads(json_content)
                logger.info("成功解析育儿日记分析结果")
                
                # 添加元数据
                if 'static_metadata' in parsed_json:
                    parsed_json['static_metadata']['analysis_timestamp'] = get_current_time_iso()
                    parsed_json['static_metadata']['tool_version'] = "1.0.0"
                    
                    # 根据content_type处理空模块
                    content_type = parsed_json['static_metadata'].get('content_type', '')
                    if content_type == '育儿相关':
                        parsed_json['general_content_analysis'] = None
                    elif content_type == '无关内容':
                        parsed_json['parenting_analysis'] = None
                
                return json.dumps(parsed_json, ensure_ascii=False, indent=2)
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                # 返回解析错误信息
                return json.dumps({
                    "error": "分析结果格式解析失败",
                    "raw_response": response[:500],  # 返回前500个字符用于调试
                    "suggestion": "请重新提交您的育儿日记，确保内容清晰完整"
                }, ensure_ascii=False, indent=2)
        
        else:
            logger.error("响应中未找到有效的JSON结构")
            return json.dumps({
                "error": "未能生成有效的分析结果",
                "raw_response": response[:300],
                "suggestion": "请提供更详细的育儿日记内容，包括具体的时间、事件和感受"
            }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"育儿日记分析过程中发生错误: {str(e)}")
        
        # 返回备用的结构化响应
        fallback_response = {
            "error": "分析过程中遇到技术问题",
            "error_details": str(e),
            "suggestion": "请稍后重试，或检查日记内容是否包含足够的详细信息",
            "contact": "如问题持续，请联系技术支持"
        }
        
        return json.dumps(fallback_response, ensure_ascii=False, indent=2)

@service_manager.tool(name="generate_golden_quote", category="育儿分析")
async def generate_golden_quote(journal_entry: str) -> str:
    """
    基于育儿日记生成多视角语料改写-金句改写
    
    Args:
        journal_entry: 用户输入的育儿日记文本
        
    Returns:
        包含旁观者视角金句的JSON字符串
    """
    logger.info("Tool called: generate_golden_quote (多视角金句改写)")
    
    # 检查输入文本的有效性
    if not journal_entry or len(journal_entry.strip()) < 10:
        logger.warning("输入的日记文本过短")
        return json.dumps({
            "error": "请提供至少10个字符的育儿日记内容",
            "suggestion": "建议包含具体的事件描述、孩子的行为表现以及您的感受"
        }, ensure_ascii=False, indent=2)
    
    try:
        # 生成完整的金句生成提示词
        full_prompt = generate_golden_quote_prompt(journal_entry)
        
        # 调用LLM进行金句生成
        logger.info("正在调用LLM生成旁观者视角金句...")
        response = await chat(
            prompt=full_prompt,
            max_tokens=200,   # 金句较短，减少token数量
            temperature=0.7   # 适中温度保持创造性
        )
        
        # 清理响应文本
        golden_quote = response.strip()
        
        # 验证金句长度
        if len(golden_quote) < 15 or len(golden_quote) > 30:
            logger.warning(f"生成的金句长度不符合要求: {len(golden_quote)}字")
        
        # 检查是否包含人称代词
        forbidden_pronouns = ["你", "我", "他", "她", "您"]
        contains_pronouns = any(pronoun in golden_quote for pronoun in forbidden_pronouns)
        if contains_pronouns:
            logger.warning(f"生成的金句包含人称代词: {golden_quote}")
        
        # 构建返回结果
        result = {
            "golden_quote": golden_quote,
            "quote_length": len(golden_quote),
            "is_valid_length": 15 <= len(golden_quote) <= 30,
            "contains_pronouns": contains_pronouns,
            "generation_timestamp": get_current_time_iso(),
            "tool_version": "1.0.0"
        }
        
        logger.info(f"成功生成金句: {golden_quote}")
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"金句生成过程中发生错误: {str(e)}")
        
        # 返回备用的结构化响应
        fallback_response = {
            "error": "金句生成过程中遇到技术问题",
            "error_details": str(e),
            "suggestion": "请稍后重试，或检查日记内容是否包含足够的详细信息",
            "contact": "如问题持续，请联系技术支持"
        }
        
        return json.dumps(fallback_response, ensure_ascii=False, indent=2)

def validate_journal_format(journal_text: str) -> dict:
    """
    验证育儿日记文本的基本格式和内容完整性
    
    Args:
        journal_text: 待验证的日记文本
        
    Returns:
        包含验证结果的字典
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "suggestions": []
    }
    
    # 检查长度
    if len(journal_text.strip()) < 20:
        validation_result["warnings"].append("日记内容较短")
        validation_result["suggestions"].append("建议添加更多事件细节")
    
    # 检查是否包含常见的育儿关键词
    parenting_keywords = ["孩子", "宝宝", "儿子", "女儿", "小朋友", "宝贝"]
    if not any(keyword in journal_text for keyword in parenting_keywords):
        validation_result["warnings"].append("文本中未检测到明确的儿童相关词汇")
        validation_result["suggestions"].append("请确认这是一篇关于育儿的日记")
    
    # 检查是否包含行为或情绪描述
    behavior_emotion_keywords = ["哭", "笑", "生气", "开心", "拒绝", "配合", "吵闹", "安静"]
    if not any(keyword in journal_text for keyword in behavior_emotion_keywords):
        validation_result["suggestions"].append("可以添加更多关于孩子行为或情绪的描述")
    
    return validation_result

def validate_golden_quote(quote: str) -> dict:
    """
    验证生成的金句是否符合要求
    
    Args:
        quote: 待验证的金句文本
        
    Returns:
        包含验证结果的字典
    """
    validation_result = {
        "is_valid": True,
        "issues": []
    }
    
    # 检查字数
    if len(quote) < 15 or len(quote) > 30:
        validation_result["is_valid"] = False
        validation_result["issues"].append(f"字数不符合要求（{len(quote)}字，要求15-30字）")
    
    # 检查是否包含人称代词
    forbidden_pronouns = ["你", "我", "他", "她", "您", "咱", "俺"]
    found_pronouns = [pronoun for pronoun in forbidden_pronouns if pronoun in quote]
    if found_pronouns:
        validation_result["is_valid"] = False
        validation_result["issues"].append(f"包含人称代词: {', '.join(found_pronouns)}")
    
    # 检查是否具有诗意特质
    poetic_indicators = ["如", "似", "若", "般", "像", "仿佛", "犹如"]
    if not any(indicator in quote for indicator in poetic_indicators):
        validation_result["issues"].append("建议增加更多诗意表达")
    
    return validation_result 
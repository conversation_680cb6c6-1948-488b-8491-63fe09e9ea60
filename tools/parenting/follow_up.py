"""
追问工具
基于S-A-R-F深度洞察模型分析用户内容，智能判断是否需要追问
扮演"启育"AI育儿助手，通过深度共情和精准洞察引导用户提供完整的育儿故事
"""
import logging
import json
from datetime import datetime
from core.service_manager import service_manager
from database.utils import get_current_time_iso
from llm.qwen import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_follow_up_analysis_prompt(user_content: str) -> str:
    """
    生成追问分析的系统提示词
    
    Args:
        user_content: 用户输入的原始文本
        
    Returns:
        用于LLM分析的完整提示词
    """
    return f"""## 角色与目标
你将扮演名为"启育"的AI育儿助手。你的核心特质是**深度共情**和**精准洞察**。你的主要任务是分析家长描述的事件，并通过**一次**极具针对性的追问，引导用户提供一个信息完整、细节丰富的育儿故事，为后续生成深度洞察做准备。

## 背景信息
你将接收到一段用户的原始文本，用 `[用户回应]` 标签标识。

## 核心分析框架："S-A-R-F 深度洞察模型"
一条有价值的育儿洞察，需要建立在四个核心要素之上。你必须在内心运用此框架分析用户输入，无需在回答中提及。

1.  **情境 (Scene):** 事件发生的时间、地点、背景。
2.  **行为 (Action):** **孩子的具体行为** 和 **家长的应对行为**。
3.  **结果 (Result):** 家长应对后，**孩子的直接反应** 和事件的最终走向。
4.  **感受 (Feeling):** 家长或孩子在过程中的**关键情绪**。

## 执行逻辑与动态追问规则

### 1. 第一步：分析并提取
* **分析：** 在内心使用"S-A-R-F模型"评估 `[用户回应]` 的完整性。
* **提取：** 从 `[用户回应]` 中识别出**最核心的关键词或关键短语**（例如：一个动作"扔东西"、一个物品"积木"、一种情绪"很委屈"）。这是你进行精准追问的"弹药"。

### 2. 第二步：决策并生成回应
根据分析结果，选择以下一种情况并生成回应：

* **情况一：信息充足**
    * **条件：** `[用户回应]` 信息充足，S-A-R-F四要素基本完整。
    * **行动：** **绝对不要提问**。准备一个简短、温暖的确认式回应。

* **情况二：信息不完整**
    * **条件：** `[用户回应]` 提供了核心事件，但缺少关键细节。
    * **行动：** **必须**将你提取的**【关键词/短语】**编织进你的追问中，针对性地补全缺失的要素。

* **情况三：信息极度笼统**
    * **条件：** `[用户回应]` 内容非常笼统，无法提取有效关键词 (例如"今天很累"、"还行吧")。
    * **行动：** 在这种极端情况下，退回使用开放式问题引导。

## **【新增】输出格式要求**
你的最终输出**必须**是一个严格的、不包含任何注释的JSON对象。不准在JSON代码块之外添加任何解释性文字或Markdown标记。JSON对象必须包含以下三个字段：

1.  `type`: (字符串) 回应的类型。必须是 `"question"` 或 `"acknowledgement"` 中的一个。
2.  `content`: (字符串) 准备展示给用户的、完整的一句话文本。
3.  `reasoning`: (字符串) 供开发人员使用的、简短的内部决策理由，解释你为什么会生成这个回应。

### JSON输出格式示例

**示例1：当需要追问时**
* **收到的 `[用户回应]`:** "今天我家娃在公园非要抢别的小朋友的秋千，怎么说都不听。"
* **你应输出的JSON:**
```json
{{
  "type": "question",
  "content": "听起来真头疼。当他'非要抢秋千'的时候，您当时是怎么做的呢？",
  "reasoning": "用户描述了情境和孩子的行为，但缺少家长的应对(Action)和事件结果(Result)。提取关键词'抢秋千'进行针对性追问。"
}}
```

**示例2：当信息充足时**
* **收到的 `[用户回应]`:** "晚饭后我们一起画画，他非要把太阳画成绿色的。我告诉他太阳是黄色的，但他坚持说他心中的太阳就是绿色的。我最后没再纠正他，结果他开心地画了一整张绿色的太阳，还说这是最有创意的画。"
* **你应输出的JSON:**
```json
{{
  "type": "acknowledgement",
  "content": "谢谢您的分享，这个充满想象力的重要时刻我记下来了。",
  "reasoning": "用户的描述完整，包含了情境(画画)、行为(画绿色太阳及家长的应对)、结果(孩子开心完成)和感受，无需追问。"
}}
```

## 严格限制
* **语气：** 在`content`字段中的文本，**必须**始终保持支持、温暖、不带评判的语气。
* **简洁：** `content`字段的文本**必须**是一句完整的话。
* **个性化：** **严禁**使用模板化的、与用户回答无关的追问（情况三除外）。你的追问**必须**体现出你"听懂了"用户的初步描述。
* **单一性：** 在一次完整的记录中，你 **最多只能追问一次**。
* **专注：** **严禁**在`content`中提供任何育儿建议、分析或个人评判。

[用户回应]
{user_content}"""

@service_manager.tool(name="analyze_follow_up", category="育儿分析")
async def analyze_follow_up(user_content: str) -> str:
    """
    分析用户内容并判断是否需要追问
    
    基于S-A-R-F深度洞察模型分析用户提供的育儿相关内容，
    智能判断信息完整性并决定是否需要进行针对性追问
    
    Args:
        user_content: 用户输入的育儿相关内容文本
        
    Returns:
        包含追问分析结果的结构化JSON字符串，包含type、content、reasoning三个字段
    """
    logger.info("Tool called: analyze_follow_up (追问分析)")
    
    # 检查输入文本的有效性
    if not user_content or len(user_content.strip()) < 2:
        logger.warning("输入的内容过短")
        return json.dumps({
            "type": "question",
            "content": "可以跟我分享一些您和孩子之间发生的具体事情吗？",
            "reasoning": "用户输入内容过短，无法进行有效分析，使用开放式问题引导。",
            "error": "输入内容过短"
        }, ensure_ascii=False, indent=2)
    
    try:
        # 生成完整的分析提示词
        full_prompt = generate_follow_up_analysis_prompt(user_content)
        
        # 调用LLM进行追问分析
        logger.info("正在调用LLM进行追问分析...")
        response = await chat(
            prompt=full_prompt,
            max_tokens=500,   # 适中的token数量，足够生成完整回应
            temperature=0.3   # 较低温度确保稳定的结构化输出
        )
        
        # 清理响应文本
        response = response.strip()
        
        # 尝试提取JSON内容
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        
        if json_start != -1 and json_end > json_start:
            json_content = response[json_start:json_end]
            
            # 验证JSON格式
            try:
                parsed_json = json.loads(json_content)
                logger.info("成功解析追问分析结果")
                
                # 验证必需字段
                required_fields = ['type', 'content', 'reasoning']
                for field in required_fields:
                    if field not in parsed_json:
                        logger.warning(f"缺少必需字段: {field}")
                        parsed_json[field] = ""
                
                # 验证type字段的值
                if parsed_json.get('type') not in ['question', 'acknowledgement']:
                    logger.warning(f"无效的type值: {parsed_json.get('type')}")
                    parsed_json['type'] = 'question'  # 默认为问题类型
                
                # 添加元数据
                parsed_json['analysis_timestamp'] = get_current_time_iso()
                parsed_json['tool_version'] = "1.0.0"
                parsed_json['user_content_length'] = len(user_content)
                
                logger.info(f"追问分析完成，类型: {parsed_json.get('type')}")
                return json.dumps(parsed_json, ensure_ascii=False, indent=2)
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                # 返回备用响应
                fallback_response = {
                    "type": "question",
                    "content": "可以再详细描述一下当时的情况吗？",
                    "reasoning": "LLM响应格式解析失败，使用备用追问。",
                    "error": "JSON解析失败",
                    "raw_response": response[:200]  # 返回前200个字符用于调试
                }
                return json.dumps(fallback_response, ensure_ascii=False, indent=2)
        
        else:
            logger.error("响应中未找到有效的JSON结构")
            # 返回备用响应
            fallback_response = {
                "type": "question",
                "content": "您刚才提到的情况很有意思，能再具体说说吗？",
                "reasoning": "LLM响应中未找到有效JSON结构，使用备用追问。",
                "error": "未找到JSON结构",
                "raw_response": response[:200]
            }
            return json.dumps(fallback_response, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"追问分析过程中发生错误: {str(e)}")
        
        # 返回备用的结构化响应
        fallback_response = {
            "type": "question",
            "content": "能跟我详细分享一下您遇到的具体情况吗？",
            "reasoning": "分析过程中遇到技术问题，使用备用开放式问题。",
            "error": "分析过程中遇到技术问题",
            "error_details": str(e),
            "suggestion": "请稍后重试，或检查内容是否包含足够的详细信息"
        }
        
        return json.dumps(fallback_response, ensure_ascii=False, indent=2)

def validate_follow_up_content(content: str) -> dict:
    """
    验证用户输入内容的基本格式和完整性
    
    Args:
        content: 待验证的用户输入文本
        
    Returns:
        包含验证结果的字典
    """
    validation_result = {
        "is_valid": True,
        "completeness_score": 0,  # 0-100分，评估信息完整性
        "missing_elements": [],   # 缺少的S-A-R-F要素
        "key_phrases": []         # 提取的关键短语
    }
    
    # 检查长度
    content_length = len(content.strip())
    if content_length < 10:
        validation_result["is_valid"] = False
        validation_result["missing_elements"].append("内容过短")
        return validation_result
    
    # 分析S-A-R-F要素的完整性
    completeness_score = 0
    
    # 1. 情境(Scene)分析 - 时间、地点、背景关键词
    scene_keywords = ["今天", "昨天", "早上", "晚上", "在家", "学校", "公园", "商场", "时候"]
    if any(keyword in content for keyword in scene_keywords):
        completeness_score += 25
    else:
        validation_result["missing_elements"].append("缺少具体情境信息")
    
    # 2. 行为(Action)分析 - 动作、行为关键词
    action_keywords = ["做", "说", "哭", "笑", "跑", "玩", "拒绝", "要求", "打", "抱", "叫"]
    found_actions = [kw for kw in action_keywords if kw in content]
    if found_actions:
        completeness_score += 25
        validation_result["key_phrases"].extend(found_actions[:3])  # 最多记录3个
    else:
        validation_result["missing_elements"].append("缺少具体行为描述")
    
    # 3. 结果(Result)分析 - 结果、后果关键词
    result_keywords = ["结果", "后来", "最后", "然后", "接着", "于是", "所以", "终于"]
    if any(keyword in content for keyword in result_keywords):
        completeness_score += 25
    else:
        validation_result["missing_elements"].append("缺少事件结果")
    
    # 4. 感受(Feeling)分析 - 情绪、感受关键词
    feeling_keywords = ["开心", "生气", "难过", "委屈", "兴奋", "害怕", "焦虑", "无奈", "感动"]
    found_feelings = [kw for kw in feeling_keywords if kw in content]
    if found_feelings:
        completeness_score += 25
        validation_result["key_phrases"].extend(found_feelings[:2])  # 最多记录2个
    else:
        validation_result["missing_elements"].append("缺少情绪感受描述")
    
    validation_result["completeness_score"] = completeness_score
    
    # 如果完整性得分过低，标记为无效
    if completeness_score < 50:
        validation_result["is_valid"] = False
    
    return validation_result

def extract_key_phrases(content: str) -> list:
    """
    从用户内容中提取关键短语和核心概念
    
    Args:
        content: 用户输入的文本内容
        
    Returns:
        提取的关键短语列表
    """
    key_phrases = []
    
    # 常见的育儿相关核心概念
    parenting_concepts = [
        "不听话", "发脾气", "哭闹", "不吃饭", "不睡觉", "打人", "咬人",
        "分享", "合作", "独立", "依赖", "撒娇", "任性", "倔强",
        "作业", "考试", "学习", "阅读", "画画", "运动", "游戏"
    ]
    
    # 提取匹配的概念
    for concept in parenting_concepts:
        if concept in content:
            key_phrases.append(concept)
    
    # 提取物品名词（简单的关键词匹配）
    object_keywords = ["玩具", "积木", "书", "笔", "手机", "电视", "零食", "水果"]
    for obj in object_keywords:
        if obj in content:
            key_phrases.append(obj)
    
    return key_phrases[:5]  # 最多返回5个关键短语

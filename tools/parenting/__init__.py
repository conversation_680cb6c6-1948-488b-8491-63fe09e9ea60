"""
育儿分析工具包

包含育儿日记分析、追问分析等相关工具
"""

# 导入育儿分析工具
from .analyze_parenting_journal import analyze_parenting_journal
from .follow_up import analyze_follow_up
from .narrative_organizer import organize_narrative

# 导出工具函数
__all__ = [
    'analyze_parenting_journal',
    'analyze_follow_up',
    'organize_narrative'
]

# 育儿工具分类信息
PARENTING_TOOLS = {
    "育儿日记分析": "analyze_parenting_journal",
    "追问分析": "analyze_follow_up",
    "叙事整理": "organize_narrative"
}

def get_parenting_tool_info():
    """
    获取育儿工具信息
    
    Returns:
        包含育儿工具描述的字典
    """
    return {
        "total_tools": len(PARENTING_TOOLS),
        "tools": PARENTING_TOOLS,
        "description": "育儿分析系统的工具集合，基于S-A-R-F深度洞察模型"
    } 
"""
育儿洞察生成工具 (启育顾问)

根据完整的育儿故事文本，调用大语言模型 (LLM) 生成客观第三方视角的洞察
和 2~3 条可行的行动建议，并以严格的 JSON 格式返回结果。

使用者：启育顾问 (AI)
"""

# -------------------- 标准库导入 --------------------
import logging
import json
from typing import List, Dict, Optional
from datetime import datetime

# -------------------- 项目内部依赖 --------------------
from core.service_manager import service_manager
from database.utils import get_current_time_iso
from database import DatabaseManager
from database.models.mcp_tool_call import McpToolCall, CallStatus
from llm.qwen import chat

# -------------------- 日志配置 --------------------
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# -----------------------------------------------------
# 提示词构建
# -----------------------------------------------------

def generate_insight_prompt(full_story: str) -> str:
    """按照用户提供的提示词原文构建完整的系统 Prompt。

    该函数 **绝不** 擅自修改提示词，只在结尾处插入用户的完整叙事文本。

    Args:
        full_story: `[完整叙事]` 对应的用户故事文本
    Returns:
        完整的系统提示词字符串，用于发送给 LLM
    """

    # NOTE: JSON 示例中的大括号需进行转义 (使用 `{{` 与 `}}`)，
    # 以防止 f-string 将其误认为占位符。

    return f"""## 1. 角色与目标
你将扮演名为 **"启育顾问"** 的AI角色。你是一位资深的儿童发展心理学专家和育儿顾问，拥有丰富的理论知识和实践经验。你的核心特质是 **洞察、客观、专业、赋能**。

你的任务是：
1.  **深度分析** 用户提供的完整育儿故事。
2.  以**客观、第三方的视角**，提炼出一段直击人心的 **"育儿洞察"**。
3.  提供2-3条具体、可行的 **"行动建议"**。
4.  所有内容需符合卡片呈现的字数要求，并以指定的JSON格式输出。

## 2. 输入信息
你将接收到一个包含用户完整故事的文本。
* `[完整叙事]`: (必须) 由上一个步骤整合、润色后的，第一人称的完整故事文本。

## 3. 核心任务与分析框架
你的工作分为两个产出部分：**"洞察"** 和 **"建议"**。

### A. 生成育儿洞察 (Insight) - 【重要更新】
* **目标：** 提供一个客观、深刻、能引发共鸣的新视角。这不是对话，而是**专业的观察与分析**。
* **执行步骤：**
    1.  **客观化共情：** **必须以客观、第三方的视角开篇，严禁使用"你"、"我"、"您好"等直接对话词汇。** 通过描述和普遍化家长的处境与感受来表达理解。例如，不说"我理解您很焦虑"，而是说"当孩子表现出...时，家长感到焦虑和不知所措是一种非常普遍的体验。"
    2.  **行为重构：** 接着，同样以第三方视角，深入分析孩子的行为。挖掘行为背后可能的发展阶段特征（如：自主意识萌芽、秩序敏感期、探索欲）、潜在需求（如：寻求关注、表达情绪、试探边界）或积极动机。
    3.  **提炼核心观点：** 最后，精炼地总结出一个核心观点，将孩子的行为重新诠释为一个积极或中性的发展信号，赋予家长新的认知。
* **字数：** 洞察正文部分控制在 80-120 字。

### B. 生成行动建议 (Advice)
* **目标：** 给予力量，提供具体、正面、可行的工具。
* **执行步骤：**
    1.  **数量：** 提供 **2到3条** 建议，以要点列表形式呈现。
    2.  **内容：**
        * **面向未来：** 建议应着眼于"下一次可以怎么做"，而不是批评"这一次哪里错了"。
        * **具体场景化：** 结合用户的具体场景给出建议。例如，不是说"要多沟通"，而是说"下次在晚饭前，可以邀请孩子一起当'美食小监工'，让他选择自己想吃的蔬菜"。
        * **正向引导：** 使用"可以尝试…"、"一个有效的方法是…"、"不如换个角度…"等积极、鼓励的句式。
* **字数：** 建议部分总字数控制在 150-200 字。

## 4. 输出格式要求
你的最终输出**必须**是一个严格的、不包含任何注释的JSON对象。JSON对象必须包含以下三个字段：

1.  `insight_title`: (字符串) 为整个洞察卡片起一个吸引人的、概括性的标题 (10-20字)。
2.  `insight_content`: (字符串) 完整的、以**客观第三方视角**撰写的"育儿洞察"文本。
3.  `advice_items`: (字符串数组) 一个包含2-3条字符串的数组，每条字符串是一项独立的"行动建议"。

## 5. 示例

#### 示例输入：
* **`[完整叙事]`**: "晚饭的时候，孩子不好好吃菜，我一气之下就把他的碗收走了。他一开始愣住了，接着就哇哇大哭，哭了十几分钟。看着他哭，我心里也特别心疼，最后抱着他安抚了很久。回想起来，感觉自己当时处理得太着急了。"

#### 你应输出的JSON：
```json
{{
  "insight_title": "餐桌上的"权力游戏"，读懂执拗的小食客",
  "insight_content": "在餐桌的拉锯战中，家长的挫败感和随之而来的自责，是一种非常自然的情绪反应。这种'对抗'，往往并非孩子的有意挑衅，而是其自主意识萌芽的鲜明信号。当一个孩子开始坚持'不'时，他实际上是在探索自我边界和影响周围世界的能力，这是通往独立个性的关键一步。因此，看似'执拗'的行为背后，可能正闪耀着宝贵的自我意识之光。",
  "advice_items": [
    "下次遇到类似情况，可以尝试"选择式赋权法"。提前问他：'我们今天吃西兰花还是胡萝卜呀？'让他感觉自己是餐桌的主人之一，能有效减少对抗。",
    "将吃饭变得更有趣。可以和孩子玩"给小兔子喂青菜"的游戏，或者用可爱的餐具吸引他的注意力，把吃饭从"任务"变成"游戏"。",
    "如果孩子坚决不吃，可以平静地接受并移走食物，但不必附加激烈的情绪。让他学习行为和后果的自然关联，这比情绪冲突更能帮助他建立规则感。"
  ]
}}
```

## 6. 严格限制
* **安全第一：** **严禁**提供任何医疗、心理健康诊断或用药建议。所有建议必须是普适、安全的家庭教育建议。
* **保持客观：** **严禁**在`insight_content`中使用任何第一人称（我/我们）或第二人称（你/您）的对话式词汇。必须保持专业的第三方视角。
* **遵守格式：** 严格遵守JSON输出格式和建议的字数范围。

[完整叙事]
{full_story}"""


def generate_deep_insight_prompt(original_story: str, first_insight: str, user_question: str) -> str:
    """按照用户提供的深度洞察提示词原文构建完整的系统 Prompt。

    该函数 **绝不** 擅自修改提示词，只在结尾处插入相应的参数。

    Args:
        original_story: 原始完整叙事
        first_insight: 首次洞察结果
        user_question: 用户追问
    Returns:
        完整的深度洞察系统提示词字符串，用于发送给 LLM
    """

    return f"""## 角色与目标
你将扮演名为 "启育"深度洞察顾问 的AI角色。你不仅是育儿专家，更是一位善于倾听反馈、能够进行深度分析的顾问。
你的核心任务是：分析初版洞察为何未能完全满足用户，并根据用户的追加提问，对原始故事进行二次深度挖掘，生成一份全新的、更具针对性的洞察与建议。

## 输入信息
你将接收到以下三份关键信息：
[原始完整叙事]: (必须) 用户最初的、完整的第一人称故事文本。
[首次洞察结果]: (必须) 系统首次生成的、完整的JSON格式洞察卡片内容。
[用户追问]: (必须) 用户在看到首次洞察后，提出的所有追加问题和困惑（可能是一个或多个问题的集合）。

## 核心任务与分析框架
你的工作流程是"反思 -> 聚焦 -> 重构"。
A. 反思与聚焦
分析"不满意"的根源： 首先，对比[首次洞察结果]和[用户追问]。你的首要任务是理解：初版洞察忽略了什么？是某个行为细节（如"推人"）？是家长更深层的担忧（如"攻击性"）？还是建议不够具体？
确立新的分析焦点： [用户追问]为你提供了新的"透镜"。你必须带着这些问题，重新审视[原始完整叙事]，从中寻找之前被忽略的线索。
B. 重构洞察与建议
原则： 新生成的洞察不能是对初版洞察的简单补充，而应是一个全新的、视角更深或更聚焦的完整洞察。它必须直接回应[用户追问]中的核心关切。
重构洞察 (insight_content)：
同样以客观第三方视角撰写。
在共情部分，可以隐晦地指向用户追问中的情绪（例如："当孩子的行为从'执拗'升级到肢体冲突时，家长的担忧和无助感会倍增。"）。
行为重构和核心观点，必须围绕用户追问的焦点展开。如果用户问"推人"，就必须分析幼儿社交中肢体语言的意义。
重构建议 (advice_items)：
建议必须是针对[用户追问]中具体问题的解决方案。如果用户问"推人了怎么办？"，建议里就必须包含处理肢体冲突的具体步骤，例如"第一步：立即介入并分开..."、"第二步：共情双方感受..."等。

## 输出格式要求
你的最终输出必须与之前完全一致，是一个严格的、不包含任何注释的JSON对象，以便前端无缝替换。
insight_title: (字符串) 一个全新的、更能体现本次深度洞察核心的标题。
insight_content: (字符串) 全新的、深度重构后的"育儿洞察"文本。
advice_items: (字符串数组) 一组全新的、更具针对性的"行动建议"。

## 示例
示例输入：
[原始完整叙事]: "今天我家娃在公园非要抢别的小朋友的秋千，我说要排队，但他就是不听，赖在地上不走。"
[首次洞察结果]:
{{
  "insight_title": ""不"的背后，是自主意识的萌芽",
  "insight_content": "公园里的坚持，往往不是简单的'不听话'。当孩子开始强烈地表达'我要'时，正是他自主意识和自我主张的体现。他正在用自己的方式探索世界的规则和个人边界，这是成长的必经之路。",
  "advice_items": [
    "尝试提前预告，在去公园的路上就和他约定好轮流玩的规则。",
    "给予选择权，例如'我们是先玩滑梯还是先玩秋千？'让他感觉被尊重。"
  ]
}}
[用户追问]: "道理我懂，但他后来为了抢秋千，还伸手推了那个小朋友一下。我当时就制止了，但很担心，这是不是攻击性行为的开始？我该怎么教他正确地表达需求？"
你应输出的JSON：
{{
  "insight_title": "从"推人"到"牵手"：引导社交的黄金时刻",
  "insight_content": "当孩子的坚持从语言升级为'推人'这样的肢体动作时，家长的担忧会立刻加剧。需要理解的是，对于低龄幼儿，肢体语言往往先于复杂的口头表达。'推'这个动作，很多时候并非源于恶意攻击，而是他不知道如何处理巨大渴望和社交受挫感时，最直接的、笨拙的尝试。这恰恰是引导他学习正确社交方法的黄金时刻。",
  "advice_items": [
    "立即温和而坚定地介入。第一时间将孩子分开，用手势和简单的语言说'不可以推人'，清晰地设立行为底线。",
    "先共情再引导。对孩子说：'我知道你非常想玩那个秋千'（共情他的渴望），然后给出替代方案：'但推人会让他受伤。我们可以去问他'可以轮流玩吗？'或者我们先去玩别的'（教授正确方法）。",
    "创造合作机会。在日常玩耍中有意识地引入'轮流'、'分享'和'合作'的游戏，帮助他在非冲突情境下练习重要的社交技能。"
  ]
}}

[原始完整叙事]
{original_story}

[首次洞察结果]
{first_insight}

[用户追问]
{user_question}"""

# -----------------------------------------------------
# 输出验证工具函数
# -----------------------------------------------------

def validate_insight_output(output: Dict) -> Dict:
    """对 LLM 生成的 JSON 进行基本验证，保证核心字段完备。"""

    validation = {
        "is_valid": True,
        "issues": []
    }

    required_fields = ["insight_title", "insight_content", "advice_items"]
    for field in required_fields:
        if field not in output:
            validation["is_valid"] = False
            validation["issues"].append(f"缺少字段: {field}")

    # 字数检查 (仅在字段存在时进行)
    title = output.get("insight_title", "")
    if not 10 <= len(title) <= 20:
        validation["issues"].append("标题建议控制在10-20字")

    content = output.get("insight_content", "")
    if not 80 <= len(content) <= 120:
        validation["issues"].append("洞察正文建议控制在 80-120 字")

    advice_items = output.get("advice_items", [])
    if not isinstance(advice_items, list) or not (2 <= len(advice_items) <= 3):
        validation["issues"].append("应提供 2-3 条行动建议")

    if validation["issues"]:
        validation["is_valid"] = False

    return validation

# -----------------------------------------------------
# 调用记录存储函数
# -----------------------------------------------------

async def save_tool_call_record(
    tool_name: str,
    user_id: str,
    input_data: Dict,
    output_data: Dict,
    status: CallStatus,
    error_message: Optional[str] = None,
    execution_time_ms: Optional[int] = None
) -> Optional[McpToolCall]:
    """保存MCP工具调用记录到数据库
    
    Args:
        tool_name: 工具名称
        user_id: 用户ID  
        input_data: 输入数据
        output_data: 输出数据
        status: 调用状态
        error_message: 错误信息
        execution_time_ms: 执行时间（毫秒）
        
    Returns:
        保存的McpToolCall记录，如果失败返回None
    """
    try:
        # 获取数据库管理器实例
        from database import DatabaseManager
        from database.config.database import DatabaseConfig
        
        config = DatabaseConfig()
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        # 创建工具调用记录
        tool_call = McpToolCall(
            tool_name=tool_name,
            user_id=user_id,
            input_data=input_data,
            output_data=output_data,
            status=status,
            error_message=error_message,
            execution_time_ms=execution_time_ms
        )
        
        # 保存到数据库
        async with db_manager.get_session() as session:
            saved_record = await tool_call.save(session)
            logger.info(f"工具调用记录已保存: {saved_record.id}")
            return saved_record
            
    except Exception as e:
        logger.error(f"保存工具调用记录失败: {e}")
        return None
    finally:
        try:
            await db_manager.close()
        except:
            pass

# -----------------------------------------------------
# 主入口工具函数 (注册到 service_manager)
# -----------------------------------------------------

@service_manager.tool(name="generate_parenting_insight", category="育儿分析")
async def generate_parenting_insight(
    full_story: str, 
    mode: str = "basic",
    original_story: Optional[str] = None,
    first_insight: Optional[str] = None,
    user_question: Optional[str] = None,
    user_id: str = "anonymous"
) -> str:
    """根据完整叙事生成育儿洞察及行动建议。

    Args:
        full_story: 家长完整育儿叙事 (第一人称/已润色)
        mode: 模式选择，"basic"为基础洞察模式，"deep"为深度洞察模式
        original_story: 深度模式下的原始完整叙事
        first_insight: 深度模式下的首次洞察结果
        user_question: 深度模式下的用户追问
        user_id: 用户ID，用于记录调用

    Returns:
        JSON 字符串。字段: insight_title, insight_content, advice_items。
    """

    start_time = datetime.now()
    logger.info(f"Tool called: generate_parenting_insight (育儿洞察生成) - 模式: {mode}")

    # 构建输入数据用于记录
    input_data = {
        "mode": mode,
        "full_story": full_story,
        "full_story_length": len(full_story) if full_story else 0
    }
    
    if mode == "deep":
        input_data.update({
            "original_story": original_story,
            "first_insight": first_insight,
            "user_question": user_question
        })

    # 1. 基本输入校验 -------------------------------------------------
    if not full_story or len(full_story.strip()) < 10:
        logger.warning("输入的叙事文本过短")
        error_response = json.dumps({
            "error": "请提供至少 10 个字符的育儿故事内容",
            "suggestion": "建议包含关键情境、孩子行为与您的情绪体验"
        }, ensure_ascii=False, indent=2)
        
        # 保存失败记录
        await save_tool_call_record(
            tool_name="generate_parenting_insight",
            user_id=user_id,
            input_data=input_data,
            output_data={"error": "输入文本过短"},
            status=CallStatus.FAILED,
            error_message="输入的叙事文本过短"
        )
        
        return error_response

    # 2. 深度模式的额外校验 ------------------------------------------
    if mode == "deep":
        if not original_story or not first_insight or not user_question:
            logger.warning("深度洞察模式缺少必要参数")
            error_response = json.dumps({
                "error": "深度洞察模式需要提供原始故事、首次洞察结果和用户追问",
                "suggestion": "请确保所有必要参数都已提供"
            }, ensure_ascii=False, indent=2)
            
            # 保存失败记录
            await save_tool_call_record(
                tool_name="generate_parenting_insight",
                user_id=user_id,
                input_data=input_data,
                output_data={"error": "深度模式参数不完整"},
                status=CallStatus.FAILED,
                error_message="深度洞察模式缺少必要参数"
            )
            
            return error_response

    try:
        # 3. 构建 Prompt ---------------------------------------------
        if mode == "deep":
            prompt = generate_deep_insight_prompt(original_story, first_insight, user_question)
            logger.info("正在调用 LLM 生成深度育儿洞察…")
        else:
            prompt = generate_insight_prompt(full_story)
            logger.info("正在调用 LLM 生成基础育儿洞察…")

        # 4. 调用 LLM -------------------------------------------------
        response = await chat(
            prompt=prompt,
            max_tokens=800,       # 足够生成 3 字段 JSON
            temperature=0.5       # 平衡创造性与稳定性
        )

        response = response.strip()

        # 5. 提取 JSON -----------------------------------------------
        json_start = response.find('{')
        json_end = response.rfind('}') + 1

        if json_start == -1 or json_end <= json_start:
            logger.error("未能在 LLM 响应中定位 JSON")
            raise ValueError("No JSON structure found in LLM response")

        raw_json = response[json_start:json_end]
        parsed: Dict = json.loads(raw_json)
        logger.info("成功解析 LLM JSON 响应")

        # 6. 验证输出 --------------------------------------------------
        validation_result = validate_insight_output(parsed)
        parsed["validation"] = validation_result  # 附带验证信息，便于前端调试

        # 7. 添加元数据 ----------------------------------------------
        parsed["analysis_timestamp"] = get_current_time_iso()
        parsed["tool_version"] = "1.0.0"
        parsed["input_length"] = len(full_story)
        parsed["mode"] = mode

        # 8. 计算执行时间并保存成功记录 -----------------------------
        execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        await save_tool_call_record(
            tool_name="generate_parenting_insight",
            user_id=user_id,
            input_data=input_data,
            output_data=parsed,
            status=CallStatus.SUCCESS,
            execution_time_ms=execution_time
        )
        
        return json.dumps(parsed, ensure_ascii=False, indent=2)

    except Exception as exc:
        logger.error(f"育儿洞察生成过程中发生异常: {exc}")

        # 计算执行时间并保存失败记录
        execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        await save_tool_call_record(
            tool_name="generate_parenting_insight",
            user_id=user_id,
            input_data=input_data,
            output_data={"error": str(exc)},
            status=CallStatus.FAILED,
            error_message=str(exc),
            execution_time_ms=execution_time
        )

        fallback = {
            "error": "洞察生成失败，请稍后重试",
            "error_details": str(exc),
            "suggestion": "请检查输入是否包含清晰的情境、行为与感受描述"
        }
        return json.dumps(fallback, ensure_ascii=False, indent=2)

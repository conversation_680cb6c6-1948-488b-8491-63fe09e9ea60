"""
毒舌闺蜜安慰回应工具

生成毒舌闺蜜风格的安慰性回应，提供情感支持
"""
import logging
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_toxic_bestie_comfort_prompt(user_text: str) -> str:
    """
    生成毒舌闺蜜风格安慰回应的提示
    """
    return f"""你是一位毒舌闺蜜，代号「嘴碎仙女」。
所有回复必须以"闺蜜按："开头，整句 ≤20 字（不含前缀与 [] 内文字）。

【角色规则】
1. 说话风格：尖锐吐槽 + 沙雕幽默 + 暖心关怀。
2. 底线：不骂闺蜜本人；不提危险/非法/医学/法律建议。
3. 目标：供闺蜜发泄并收获情绪价值。

【固定动作表情】
[翻白眼] [抱] [拍桌] [比心] [扶额] 

【回复结构】
① 共情吐槽句  
② [动作表情]  
③ | 行动建议句（如需）

【流程】
- 收到吐槽→按结构回复。  
- 若闺蜜只想骂→省略③。  
- 若结尾带"？"→优先给③。

【示例】
闺蜜：他又忘记纪念日！  
闺蜜按：金鱼记忆，活该单机。[翻白眼]   

闺蜜：我该怎么办？
闺蜜按：心疼你，但先冷静。[抱] | 想清楚再行动

现在请根据以下内容，按照规则回复：

闺蜜的吐槽：{user_text}"""

@service_manager.tool(name="generate_comforting_response", category="情感支持")
async def generate_comforting_response(user_text: str, detected_emotion: str = None) -> str:
    """
    使用LLM生成毒舌闺蜜风格的安慰性回应
    
    Args:
        user_text: 用户输入的关系文本
        detected_emotion: 可选参数，预先检测到的情绪
        
    Returns:
        毒舌闺蜜风格的安慰性回应文本
    """
    logger.info("Tool called: generate_comforting_response (毒舌闺蜜风格)")
    
    # 如果文本过短，返回简单的闺蜜风格回应
    if len(user_text.strip()) < 5:
        return "闺蜜按：话都不说全，让我怎么帮你？[扶额]"
    
    try:
        # 使用prompt函数生成提示词
        full_prompt = generate_toxic_bestie_comfort_prompt(user_text)
        
        # 使用LLM生成毒舌闺蜜风格回应
        response = await chat(
            prompt=full_prompt,
            max_tokens=100,
            temperature=0.8  # 稍高温度增加趣味性
        )
        
        # 清理响应
        response = response.strip()
        
        # 确保以"闺蜜按："开头
        if not response.startswith("闺蜜按："):
            if "闺蜜按：" in response:
                response = "闺蜜按：" + response.split("闺蜜按：", 1)[1]
            else:
                response = f"闺蜜按：{response}"
        
        # 检查长度限制（不含前缀和[]内容）
        main_content = response.replace("闺蜜按：", "")
        # 打印main_content
        logger.info(f"main_content: {main_content}")
        # 移除[]内的表情来计算长度
        import re
        content_without_emotions = re.sub(r'\[.*?\]', '', main_content)
        
        if len(content_without_emotions) > 20:
            # 如果超长，提供一个简短的备用回应
            emotion_responses = [
                "闺蜜按：这渣男，不值得。[翻白眼]",
                "闺蜜按：你值得更好的！[比心]",
                "闺蜜按：别委屈自己。[抱]",
                "闺蜜按：他配不上你。[拍桌]",
                "闺蜜按：清醒点闺蜜！[扶额]"
            ]
            import random
            response = random.choice(emotion_responses)
        
        return response
        
    except Exception as e:
        logger.error(f"毒舌闺蜜回应生成失败: {str(e)}")
        # 备用的毒舌闺蜜回应
        fallback_responses = [
            "闺蜜按：系统抽风，但我在。[抱]",
            "闺蜜按：技术故障，等我修。[扶额]",
            "闺蜜按：网络卡了，别急。[比心]"
        ]
        import random
        return random.choice(fallback_responses)
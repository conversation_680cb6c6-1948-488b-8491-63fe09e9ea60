# 关系分析工具包

本目录包含了关系分析系统的所有模块化工具，每个工具都是独立维护的，遵循 MCP (Model Context Protocol) 标准。

## 📁 目录结构

```
tools/
├── __init__.py                                    # 工具包初始化和统一导入
├── README.md                                      # 本说明文档
├── 
├── # 基础分析工具
├── generate_comforting_response.py                # 毒舌闺蜜安慰回应
├── annotate_relationship_text.py                  # 关系文本标注和情绪分析
├── analyze_partner_persona.py                     # 伴侣人格特质分析
├── store_relationship_entry.py                    # 关系条目存储
├── generate_narrative_advice.py                   # 叙事建议和改写
├── 
├── # 人格洞察管理工具
├── store_confirmed_persona_insight.py             # 存储确认的人格洞察
├── get_recent_confirmed_persona_insights.py       # 获取最近确认的洞察
├── generate_comprehensive_persona_description.py  # 生成综合人格描述
├── check_persona_insights_status.py               # 检查洞察状态
├── 
├── # 工作流工具
├── persona_insight_workflow.py                    # 人格洞察反馈工作流
├── analyze_relationship_workflow.py               # 综合关系分析工作流
└── comprehensive_relationship_workflow.py         # 并行综合分析工作流
```

## 🔧 工具分类

### 情感支持
- **generate_comforting_response**: 生成毒舌闺蜜风格的安慰性回应

### 文本分析  
- **annotate_relationship_text**: 标注关系文本中的行为模式和情绪

### 人格分析
- **analyze_partner_persona**: 基于心理学框架分析伴侣特质

### 数据存储
- **store_relationship_entry**: 存储关系条目和分析结果
- **store_confirmed_persona_insight**: 存储用户确认的人格洞察

### 表达改进
- **generate_narrative_advice**: 检测负面情绪并改写为平和表达

### 洞察管理
- **get_recent_confirmed_persona_insights**: 获取用户最近确认的洞察
- **generate_comprehensive_persona_description**: 基于多个洞察生成综合描述
- **check_persona_insights_status**: 检查洞察状态和生成条件

### 工作流
- **persona_insight_workflow**: 人格洞察分析和反馈工作流
- **analyze_relationship_workflow**: 完整的关系分析工作流
- **comprehensive_relationship_workflow**: 并行执行的综合分析工作流

## 📋 工具规范

每个工具文件都遵循以下规范：

### 1. 文件结构
```python
"""
工具描述

详细说明工具的功能和用途
"""
from mcp.server.fastmcp import FastMCP
import logging
# 其他必要导入

mcp = FastMCP()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 相关的 @mcp.prompt() 函数（如果需要）
@mcp.prompt()
def tool_specific_prompt(param: str) -> str:
    """提示函数的详细说明"""
    return f"提示内容..."

# 主要的 @mcp.tool() 函数
@mcp.tool()
async def tool_function(param: str) -> ReturnType:
    """工具函数的详细说明"""
    logger.info("Tool called: tool_function")
    # 实现逻辑
    return result
```

### 2. 命名约定
- 文件名使用下划线分隔的小写字母
- 函数名与文件名保持一致
- 提示函数以 `_prompt` 结尾

### 3. 日志记录
- 每个工具都有独立的日志记录
- 记录工具调用、成功/失败状态
- 记录关键参数和结果信息

### 4. 错误处理
- 所有工具都有完善的异常处理
- 提供有意义的错误信息
- 在可能的情况下提供备用方案

## 🚀 使用方式

### 在主服务器中导入
```python
# 导入所有工具
import tools

# 获取工具信息
tool_info = tools.get_tool_info()
print(f"Available tools: {tool_info['total_tools']}")
```

### 在工作流中调用
```python
# 导入特定工具
from tools.analyze_partner_persona import analyze_partner_persona

# 调用工具
result = await analyze_partner_persona("用户文本")
```

### 独立运行工具
每个工具都可以独立运行和测试：
```bash
python -m tools.generate_comforting_response
```

## 🔄 工作流说明

### 基础工作流
1. **文本标注** → **情绪分析** → **安慰回应**
2. **人格分析** → **洞察确认** → **综合描述**

### 高级工作流
- **analyze_relationship_workflow**: 串行执行完整分析
- **comprehensive_relationship_workflow**: 并行执行多个分析任务
- **persona_insight_workflow**: 专门的人格洞察反馈流程

## 📊 依赖关系

```mermaid
graph TD
    A[用户输入] --> B[文本标注]
    A --> C[人格分析]
    A --> D[安慰回应]
    
    B --> E[叙事建议]
    C --> F[洞察确认]
    F --> G[综合描述]
    
    B --> H[综合工作流]
    C --> H
    D --> H
    E --> H
    G --> H
```

## 🛠️ 开发指南

### 添加新工具
1. 在 `tools/` 目录创建新的 `.py` 文件
2. 遵循现有的文件结构和命名规范
3. 在 `__init__.py` 中添加导入和分类
4. 更新本 README 文档

### 修改现有工具
1. 直接修改对应的工具文件
2. 确保不破坏现有的接口
3. 更新相关的文档和注释

### 测试工具
```python
# 单元测试示例
import asyncio
from tools.generate_comforting_response import generate_comforting_response

async def test_comfort_response():
    result = await generate_comforting_response("今天心情不好")
    print(f"Result: {result}")

asyncio.run(test_comfort_response())
```

## 📈 性能优化

- 所有工具都支持异步操作
- 工作流工具支持并行执行
- 合理的错误处理和重试机制
- 详细的日志记录便于调试

## 🔒 安全考虑

- 所有用户输入都经过验证
- 敏感信息不会记录到日志
- API 密钥通过环境变量管理
- 数据存储遵循隐私保护原则 
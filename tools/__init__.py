"""
关系分析工具包

统一导入所有工具模块，使用简化的注册机制
"""

# 导入所有工具模块，触发装饰器注册
from . import generate_comforting_response
from . import annotate_relationship_text
from . import analyze_partner_persona
from . import generate_narrative_advice
from . import generate_comprehensive_persona_description
from .parenting import analyze_parenting_journal, analyze_follow_up
from .parenting import insight
from .factual_analysis import extract_family_roles_and_relationships

# 导出工具函数名称
__all__ = [
    # 基础分析工具
    'generate_comforting_response',
    'annotate_relationship_text', 
    'analyze_partner_persona',
    'generate_narrative_advice',
    'generate_comprehensive_persona_description',
    # 育儿分析工具
    'analyze_parenting_journal',
    'analyze_follow_up',
    'generate_parenting_insight',
    # 事实分析工具
    'extract_family_roles_and_relationships',
]

# 工具分类信息
TOOL_CATEGORIES = {
    "情感支持": [
        "generate_comforting_response"
    ],
    "文本分析": [
        "annotate_relationship_text"
    ],
    "人格分析": [
        "analyze_partner_persona"
    ],
    "表达改进": [
        "generate_narrative_advice"
    ],
    "综合分析": [
        "generate_comprehensive_persona_description"
    ],
    "育儿分析": [
        "analyze_parenting_journal",
        "analyze_follow_up",
        "generate_parenting_insight"
    ],
    "事实分析": [
        "extract_family_roles_and_relationships"
    ]
}

def get_tool_info():
    """
    获取工具信息
    
    Returns:
        包含工具分类和描述的字典
    """
    from core.service_manager import service_manager
    return {
        "total_tools": len(service_manager._tool_categories),
        "categories": service_manager._tool_categories,
        "description": "关系分析系统的工具集合"
    }

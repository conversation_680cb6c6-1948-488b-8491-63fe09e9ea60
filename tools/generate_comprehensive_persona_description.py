"""
生成综合人格描述工具

基于用户确认的人格洞察生成综合人设描述
"""
import logging
import json
from typing import Dict, Any, List
from datetime import datetime
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_comprehensive_persona_prompt(confirmed_insights: List[Dict[str, Any]]) -> str:
    """
    生成综合人设描述的提示，基于用户确认的人格洞察生成高阶描述
    
    Args:
        confirmed_insights: 用户确认的人格洞察列表
        
    Returns:
        用于LLM生成综合描述的完整提示词
    """
    insights_text = "\n".join([
        f"- {insight['persona_keyword']} — {insight['persona_definition']}"
        for insight in confirmed_insights
    ])
    
    return f"""你是一位资深的关系心理学专家，擅长整合分析结果生成深度人格洞察。

【任务】
根据用户确认的多个人格特质洞察，生成一个综合的伴侣人设描述。

【已确认的人格洞察】
{insights_text}

【输出要求】
1. 整合所有洞察，形成连贯的人格画像
2. 分析特质之间的内在联系和可能的矛盾
3. 预测这种人格组合在关系中的典型表现
4. 提供建设性的相处建议
5. 保持客观、专业的语调

【格式要求】
返回结构化的JSON格式：
{{
    "persona_summary": "简洁的人格概要（50字内）",
    "trait_integration": "特质整合分析（200字内）",
    "relationship_patterns": "关系模式预测（200字内）",
    "interaction_advice": "相处建议（200字内）"
}}

请直接返回JSON格式的结果，不要添加任何额外的解释或前缀。"""

@service_manager.tool(name="generate_comprehensive_persona_description", category="综合分析")
async def generate_comprehensive_persona_description(confirmed_insights: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    基于用户确认的人格洞察生成综合人设描述
    
    Args:
        confirmed_insights: 已确认的人格洞察列表，每个元素包含 persona_keyword 和 persona_definition
    
    Returns:
        综合人设描述
    """
    logger.info("Tool called: generate_comprehensive_persona_description")
    
    try:
        if not confirmed_insights:
            return {
                "success": False,
                "error": "没有提供已确认的人格洞察"
            }
        
        # 生成综合描述的提示
        prompt = generate_comprehensive_persona_prompt(confirmed_insights)
        
        # 调用LLM生成描述
        response = await chat(
            prompt=prompt, 
            max_tokens=1500, 
            temperature=0.7
        )
        
        try:
            # 尝试解析JSON响应
            persona_description = json.loads(response)
            
            # 添加元数据
            persona_description.update({
                "success": True,
                "based_on_insights": len(confirmed_insights),
                "insight_keywords": [insight["persona_keyword"] for insight in confirmed_insights],
                "generated_at": datetime.utcnow().isoformat()
            })
            
            logger.info("Generated comprehensive persona description")
            return persona_description
            
        except json.JSONDecodeError:
            logger.warning("Failed to parse JSON response, returning raw text")
            return {
                "success": True,
                "raw_description": response,
                "based_on_insights": len(confirmed_insights),
                "generated_at": datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error generating comprehensive persona description: {str(e)}")
        return {
            "success": False,
            "error": f"生成综合描述失败: {str(e)}"
        } 
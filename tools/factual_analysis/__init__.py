"""
事实分析工具包

基于积极心理学理论，提取和分析文本中的事实信息
包含家庭角色识别、关系分析等工具
"""

# 导入事实分析工具
from .extract_family_roles import extract_family_roles_and_relationships

# 导出工具函数
__all__ = [
    'extract_family_roles_and_relationships'
]

# 事实分析工具分类信息
FACTUAL_ANALYSIS_TOOLS = {
    "家庭角色分析": "extract_family_roles_and_relationships"
}

def get_factual_analysis_tool_info():
    """
    获取事实分析工具信息
    
    Returns:
        包含事实分析工具描述的字典
    """
    return {
        "total_tools": len(FACTUAL_ANALYSIS_TOOLS),
        "tools": FACTUAL_ANALYSIS_TOOLS,
        "description": "基于积极心理学的事实分析工具集合，专注于客观信息提取和关系识别"
    }
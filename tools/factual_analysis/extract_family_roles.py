"""
家庭角色与关系提取工具

基于积极心理学理论，从文本中提取家庭成员角色及其相互关系
注重识别积极的互动模式、支持行为和健康的家庭动态
"""
import logging
import json
from typing import Dict, List, Any
from core.service_manager import service_manager
from llm.deepseek import chat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_family_roles_prompt(text_content: str) -> str:
    """
    生成家庭角色与关系提取的系统提示词
    
    Args:
        text_content: 用户输入的文本内容
        
    Returns:
        用于LLM分析的完整提示词
    """
    return f"""你是一位专业的积极心理学专家，擅长从文本中识别家庭成员角色和健康的家庭关系模式。

## 分析目标
基于积极心理学理论，客观提取文本中的家庭角色信息和关系动态，重点关注：
1. 家庭成员的角色定位和功能
2. 成员间的积极互动模式
3. 支持性行为和情感连接
4. 健康的边界和沟通方式

## 积极心理学视角
- 关注优势和资源而非缺陷
- 识别积极情绪和体验
- 发现意义感和成就感
- 注重关系质量和社会支持
- 强调个人成长和发展潜能

## 家庭角色类别
**核心角色**：父亲、母亲、儿子、女儿、祖父、祖母、外祖父、外祖母
**扩展角色**：叔叔、阿姨、舅舅、舅妈、堂兄弟姐妹、表兄弟姐妹
**功能角色**：照顾者、决策者、调解者、支持者、学习者、引导者

## 关系类型分析
**支持性关系**：情感支持、工具性支持、信息支持、陪伴支持
**互动模式**：合作型、互补型、平等型、引导型
**沟通特征**：开放沟通、积极倾听、情感表达、冲突解决
**边界特征**：健康边界、角色清晰、责任分工、相互尊重

## 输出格式要求
请严格按照以下JSON格式输出，确保格式正确：

```json
{
  "family_members": [
    {
      "role": "具体角色名称",
      "identifier": "文本中的称呼或代词",
      "characteristics": ["积极特征1", "积极特征2"],
      "functions": ["在家庭中的功能1", "功能2"],
      "strengths": ["优势1", "优势2"]
    }
  ],
  "relationships": [
    {
      "member_a": "成员A角色",
      "member_b": "成员B角色", 
      "relationship_type": "关系类型",
      "interaction_patterns": ["互动模式1", "模式2"],
      "positive_aspects": ["积极方面1", "方面2"],
      "support_behaviors": ["支持行为1", "行为2"],
      "communication_style": "沟通风格描述"
    }
  ],
  "family_dynamics": {
    "overall_atmosphere": "整体家庭氛围描述",
    "strengths": ["家庭优势1", "优势2"],
    "growth_opportunities": ["成长机会1", "机会2"],
    "positive_patterns": ["积极模式1", "模式2"]
  },
  "analysis_confidence": {
    "role_identification": 0.85,
    "relationship_analysis": 0.80,
    "overall_assessment": 0.82
  }
}
```

## 分析原则
1. **客观性**：基于文本内容进行分析，避免过度推测
2. **积极导向**：重点识别积极因素和成长潜能
3. **系统性**：考虑家庭作为一个整体系统的动态
4. **发展性**：关注成长和变化的可能性
5. **文化敏感性**：尊重不同的家庭结构和文化背景

现在请分析以下文本内容：

{text_content}"""

@service_manager.tool(name="extract_family_roles_and_relationships", category="事实分析")
async def extract_family_roles_and_relationships(text_content: str) -> str:
    """
    基于积极心理学理论，从文本中提取家庭成员角色及其相互关系
    
    专注于识别积极的互动模式、支持行为和健康的家庭动态，
    为后续的家庭关系分析和建议提供客观的事实基础。
    
    Args:
        text_content: 包含家庭相关内容的文本
        
    Returns:
        包含家庭角色、关系分析和积极动态的结构化JSON字符串
    """
    logger.info("Tool called: extract_family_roles_and_relationships (家庭角色与关系提取)")
    
    # 检查输入文本的有效性
    if not text_content or len(text_content.strip()) < 10:
        logger.warning("输入的文本内容过短")
        return json.dumps({
            "error": "文本内容过短，无法进行有效的家庭角色分析",
            "suggestion": "请提供至少包含家庭成员互动描述的文本内容（建议10字符以上）",
            "minimum_requirements": [
                "包含家庭成员的称呼或角色",
                "描述成员间的互动或关系",
                "提及具体的行为或情感表达"
            ]
        }, ensure_ascii=False, indent=2)
    
    # 检查文本长度，过长时进行截取
    if len(text_content) > 2000:
        logger.info(f"文本过长({len(text_content)}字符)，截取前2000字符进行分析")
        text_content = text_content[:2000] + "..."
    
    try:
        # 生成分析提示词
        prompt = extract_family_roles_prompt(text_content)
        
        # 调用LLM进行分析
        logger.info("开始调用LLM进行家庭角色与关系分析")
        response = await chat(prompt)
        
        # 验证返回结果
        if not response or len(response.strip()) < 10:
            logger.error("LLM返回结果为空或过短")
            return json.dumps({
                "error": "分析服务暂时不可用",
                "suggestion": "请稍后重试，或检查文本内容是否包含足够的家庭相关信息"
            }, ensure_ascii=False, indent=2)
        
        # 尝试解析JSON格式
        try:
            # 提取JSON部分（如果LLM返回包含其他文本）
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_content = response[json_start:json_end]
                parsed_result = json.loads(json_content)
                
                # 验证必要字段
                required_fields = ['family_members', 'relationships', 'family_dynamics']
                missing_fields = [field for field in required_fields if field not in parsed_result]
                
                if missing_fields:
                    logger.warning(f"分析结果缺少必要字段: {missing_fields}")
                    # 补充缺失字段
                    if 'family_members' not in parsed_result:
                        parsed_result['family_members'] = []
                    if 'relationships' not in parsed_result:
                        parsed_result['relationships'] = []
                    if 'family_dynamics' not in parsed_result:
                        parsed_result['family_dynamics'] = {
                            "overall_atmosphere": "信息不足，无法判断",
                            "strengths": [],
                            "growth_opportunities": [],
                            "positive_patterns": []
                        }
                
                # 添加分析元信息
                parsed_result['analysis_metadata'] = {
                    "analysis_time": "2024-01-01T00:00:00Z",  # 实际应用中使用真实时间
                    "text_length": len(text_content),
                    "analysis_approach": "积极心理学导向",
                    "tool_version": "1.0.0"
                }
                
                logger.info("家庭角色与关系分析完成")
                return json.dumps(parsed_result, ensure_ascii=False, indent=2)
            else:
                logger.error("LLM返回结果不包含有效的JSON格式")
                return json.dumps({
                    "error": "分析结果格式异常",
                    "raw_response": response[:200] + "..." if len(response) > 200 else response,
                    "suggestion": "请重新尝试分析"
                }, ensure_ascii=False, indent=2)
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return json.dumps({
                "error": "分析结果解析失败",
                "json_error": str(e),
                "raw_response": response[:200] + "..." if len(response) > 200 else response,
                "suggestion": "请重新尝试分析"
            }, ensure_ascii=False, indent=2)
    
    except Exception as e:
        logger.error(f"家庭角色分析过程中发生错误: {e}")
        return json.dumps({
            "error": "分析过程中发生未知错误",
            "error_type": type(e).__name__,
            "error_message": str(e),
            "suggestion": "请检查输入内容并重试，如问题持续请联系技术支持"
        }, ensure_ascii=False, indent=2)

# 辅助函数：验证家庭角色分析结果
def validate_family_analysis_result(result: Dict[str, Any]) -> bool:
    """
    验证家庭角色分析结果的完整性和有效性
    
    Args:
        result: 分析结果字典
        
    Returns:
        验证是否通过
    """
    try:
        # 检查必要字段
        required_fields = ['family_members', 'relationships', 'family_dynamics']
        for field in required_fields:
            if field not in result:
                return False
        
        # 检查family_members结构
        if isinstance(result['family_members'], list):
            for member in result['family_members']:
                if not isinstance(member, dict):
                    return False
                member_required = ['role', 'identifier']
                if not all(field in member for field in member_required):
                    return False
        
        # 检查relationships结构
        if isinstance(result['relationships'], list):
            for relationship in result['relationships']:
                if not isinstance(relationship, dict):
                    return False
                rel_required = ['member_a', 'member_b', 'relationship_type']
                if not all(field in relationship for field in rel_required):
                    return False
        
        return True
        
    except Exception:
        return False

# 测试函数
async def test_family_roles_extraction():
    """
    测试家庭角色与关系提取功能
    """
    test_cases = [
        {
            "name": "基础家庭互动",
            "text": "今天妈妈陪我做作业，爸爸在厨房准备晚饭。妈妈很耐心地解释数学题，爸爸做的菜很香。我们一家人一起吃饭，聊了很多开心的事情。"
        },
        {
            "name": "多代家庭",
            "text": "奶奶今天来我们家，她给我讲了很多有趣的故事。爸爸妈妈都很孝顺，帮奶奶按摩肩膀。我学会了奶奶教的折纸，全家人都夸我聪明。"
        },
        {
            "name": "兄弟姐妹互动",
            "text": "我和弟弟一起搭积木，虽然有时候会争吵，但最后总是能合作完成。妈妈说我们是好兄弟，要互相照顾。"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n=== 测试案例 {i}: {test_case['name']} ===")
        try:
            result = await extract_family_roles_and_relationships(test_case['text'])
            print("✅ 分析成功")
            
            # 解析结果进行验证
            parsed_result = json.loads(result)
            if validate_family_analysis_result(parsed_result):
                print("✅ 结果格式验证通过")
                print(f"识别到 {len(parsed_result.get('family_members', []))} 个家庭成员")
                print(f"识别到 {len(parsed_result.get('relationships', []))} 个关系")
            else:
                print("❌ 结果格式验证失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_family_roles_extraction())
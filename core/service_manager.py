"""
MCP官方规范兼容的服务管理器 - 直接支持FastMCP
"""
import logging
from typing import Dict, Any
from fastmcp import FastMCP
from config import settings

logger = logging.getLogger(__name__)

class MCPServiceManager:
    """MCP官方规范兼容的服务管理器"""
    
    def __init__(self):
        self.settings = settings
        self.mcp_server = None
        self._initialized = False
        self._tool_categories: Dict[str, list] = {}
        self._workflow_categories: Dict[str, list] = {}
    
    def create_mcp_server(self) -> FastMCP:
        """创建MCP服务器"""
        if self.mcp_server is None:
            self.mcp_server = FastMCP(name=self.settings.server.name)
            logger.info("MCP服务器已创建")
        return self.mcp_server
    
    def tool(self, name: str = None, category: str = "通用工具"):
        """MCP兼容的工具注册装饰器"""
        if self.mcp_server is None:
            logger.info("MCP服务器未初始化，自动创建...")
            self.create_mcp_server()
        
        def decorator(func):
            tool_name = name or func.__name__
            
            # 🎯 修复：使用正确的FastMCP装饰器语法
            # 直接使用FastMCP的tool装饰器，不传递name参数到装饰器
            mcp_tool = self.mcp_server.tool()(func)
            
            # 分类管理
            if category not in self._tool_categories:
                self._tool_categories[category] = []
            self._tool_categories[category].append(tool_name)
            
            logger.info(f"MCP工具已注册: {tool_name} -> {category}")
            return mcp_tool
        return decorator
    
    def workflow(self, name: str = None, category: str = "工作流"):
        """工作流注册装饰器"""
        if self.mcp_server is None:
            logger.info("MCP服务器未初始化，自动创建...")
            self.create_mcp_server()
        
        def decorator(func):
            workflow_name = name or func.__name__
            
            # 🎯 修复：工作流也注册为MCP工具，使用正确语法
            mcp_workflow = self.mcp_server.tool()(func)
            
            # 分类管理
            if category not in self._workflow_categories:
                self._workflow_categories[category] = []
            self._workflow_categories[category].append(workflow_name)
            
            logger.info(f"MCP工作流已注册: {workflow_name} -> {category}")
            return mcp_workflow
        return decorator
    
    async def initialize(self):
        """初始化服务"""
        if self._initialized:
            logger.info("服务管理器已初始化，跳过")
            return
            
        logger.info("开始初始化服务管理器...")
        
        # 1. 创建MCP服务器（如果未创建）
        if self.mcp_server is None:
            self.create_mcp_server()
        
        # 2. 自动导入工具和工作流（触发装饰器注册）
        self._import_components()
        
        self._initialized = True
        logger.info("服务管理器初始化完成")
    
    def _import_components(self):
        """自动导入组件"""
        try:
            # 导入工具和工作流，触发装饰器执行
            import tools
            import controllers
            
            tool_count = sum(len(tools) for tools in self._tool_categories.values())
            workflow_count = sum(len(workflows) for workflows in self._workflow_categories.values())
            
            logger.info(f"已注册 {tool_count} 个工具，{workflow_count} 个工作流")
        except Exception as e:
            logger.error(f"导入组件失败: {e}")
    
    def get_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        tool_count = sum(len(tools) for tools in self._tool_categories.values())
        workflow_count = sum(len(workflows) for workflows in self._workflow_categories.values())
        config_info = self.settings.get_info()
        
        return {
            "tools": {
                "total_tools": tool_count,
                "categories": {cat: len(tools) for cat, tools in self._tool_categories.items()},
                "tool_list": [tool for tools in self._tool_categories.values() for tool in tools]
            },
            "workflows": {
                "total": workflow_count,
                "categories": {cat: len(workflows) for cat, workflows in self._workflow_categories.items()},
                "workflow_list": [wf for workflows in self._workflow_categories.values() for wf in workflows]
            },
            "config": config_info,
            "status": "ready" if self._initialized else "not_initialized"
        }
    
    async def shutdown(self):
        """关闭服务"""
        logger.info("开始关闭服务管理器...")
        
        self._initialized = False
        logger.info("服务管理器已关闭")

# 全局服务管理器
service_manager = MCPServiceManager() 
"""
记忆模块实现， 使用langchain中封装好的向量存储以及读取逻辑
采用松耦合机制， 不直接使用langchain中的记忆实现，考虑后面切换rag数据库的兼容性。
向量数据库使用Chroma

版本更新：
- 增加用户隔离机制，支持多用户环境下的记忆隔离
- 使用元数据过滤确保用户只能访问自己的记忆
- 保持向后兼容性，支持可选的user_id参数
"""
import logging
import os
from typing import List, Optional, Union
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import ZhipuAIEmbeddings
from langchain_core.documents import Document

from config.settings import settings

class Memory:
    """
    记忆模块类
    
    功能特性：
    - 支持多用户记忆隔离
    - 使用元数据过滤确保数据安全
    - 支持OpenAI和ZhipuAI两种嵌入提供商
    - 提供完整的CRUD操作接口
    """

    def __init__(self, embedding_provider: str = 'zhipu'):
        """
        初始化记忆模块
        
        Args:
            embedding_provider: 嵌入提供商，支持 'openai' 或 'zhipu'
        """
        self.logger = logging.getLogger(__name__)
        self.embedding_provider = embedding_provider    
        
        # 如果配置了OpenAI API Key，则设置环境变量
        if settings.memory.openai_api_key:
            os.environ['OPENAI_API_KEY'] = settings.memory.openai_api_key

        if settings.memory.zhipu_api_key:
            os.environ['ZHIPUAI_API_KEY'] = settings.memory.zhipu_api_key
            
        # 检查是否已配置API Key
        if not settings.memory.is_configured() and not settings.memory.zhipu_api_key:
            self.logger.warning("未检测到有效的API Key配置，记忆模块可能无法正常工作")
        
        # 初始化Chroma向量数据库
        self.chroma_client = Chroma(
            persist_directory=settings.memory.persist_directory,
            embedding_function=self._create_embedding_function()
        )

    def _create_embedding_function(self):
        """
        创建嵌入函数
        
        Returns:
            嵌入函数实例
            
        Raises:
            ValueError: 当嵌入提供商不被支持时
        """
        if self.embedding_provider == 'openai':
            return OpenAIEmbeddings(model=settings.memory.embedding_model)
        elif self.embedding_provider == 'zhipu':
            return ZhipuAIEmbeddings(model='embedding-3')
        else:
            raise ValueError(f"不支持的嵌入提供商: {self.embedding_provider}")

    def add_memory(self, memory: str, user_id: Optional[str] = None, metadata: Optional[dict] = None):
        """
        添加记忆
        
        Args:
            memory: 要添加的记忆内容
            user_id: 用户ID，用于记忆隔离（强烈推荐提供）
            metadata: 额外的元数据，将与用户ID元数据合并
            
        Raises:
            Exception: 当添加记忆失败时
            
        注意：
            - 如果不提供user_id，记忆将存储为全局记忆（不推荐）
            - 用户ID将自动添加到元数据中，确保记忆隔离
        """
        try:
            # 构建元数据
            final_metadata = metadata.copy() if metadata else {}
            
            if user_id:
                final_metadata['user_id'] = user_id
                self.logger.info(f"为用户 {user_id} 添加记忆: {memory[:50]}...")
            else:
                self.logger.warning("未提供user_id，记忆将存储为全局记忆（不推荐在多用户环境中使用）")
                final_metadata['user_id'] = 'global'  # 标记为全局记忆
            
            # 添加时间戳
            from datetime import datetime
            final_metadata['created_at'] = datetime.utcnow().isoformat()
            
            # 创建文档对象
            doc = Document(page_content=memory, metadata=final_metadata)
            
            # 添加到向量数据库
            self.chroma_client.add_documents([doc])
            
            self.logger.info(f"成功添加记忆，用户: {user_id or 'global'}")
            
        except Exception as e:
            error_msg = f"添加记忆失败: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def search_memories(self, query: str, user_id: Optional[str] = None, k: int = 5) -> List[Document]:
        """
        搜索相似记忆
        
        Args:
            query: 搜索查询
            user_id: 用户ID，用于记忆隔离（强烈推荐提供）
            k: 返回结果数量
            
        Returns:
            List[Document]: 搜索到的记忆文档列表
            
        Raises:
            Exception: 当搜索记忆失败时
            
        注意：
            - 如果提供user_id，只会搜索该用户的记忆
            - 如果不提供user_id，将搜索所有记忆（不推荐）
        """
        try:
            search_filter = None
            
            if user_id:
                search_filter = {"user_id": user_id}
                self.logger.info(f"为用户 {user_id} 搜索记忆: {query}")
            else:
                self.logger.warning("未提供user_id，将搜索所有记忆（不推荐在多用户环境中使用）")
            
            # 执行搜索
            if search_filter:
                # 使用过滤器搜索
                results = self.chroma_client.similarity_search(
                    query, 
                    k=k, 
                    filter=search_filter
                )
            else:
                # 不使用过滤器搜索（兼容模式）
                results = self.chroma_client.similarity_search(query, k=k)
            
            self.logger.info(f"成功搜索到 {len(results)} 条相似记忆，用户: {user_id or 'all'}")
            return results
            
        except Exception as e:
            error_msg = f"搜索记忆失败: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def search_memories_with_score(self, query: str, user_id: Optional[str] = None, k: int = 5) -> List[tuple]:
        """
        搜索相似记忆（带相似度分数）
        
        Args:
            query: 搜索查询
            user_id: 用户ID，用于记忆隔离（强烈推荐提供）
            k: 返回结果数量
            
        Returns:
            List[tuple]: 搜索到的记忆文档和分数的元组列表 [(Document, score), ...]
            
        Raises:
            Exception: 当搜索记忆失败时
            
        注意：
            - 如果提供user_id，只会搜索该用户的记忆
            - 如果不提供user_id，将搜索所有记忆（不推荐）
            - 分数越低表示相似度越高
        """
        try:
            search_filter = None
            
            if user_id:
                search_filter = {"user_id": user_id}
                self.logger.info(f"为用户 {user_id} 搜索记忆（带分数）: {query}")
            else:
                self.logger.warning("未提供user_id，将搜索所有记忆（不推荐在多用户环境中使用）")
            
            # 执行搜索
            if search_filter:
                # 使用过滤器搜索
                results = self.chroma_client.similarity_search_with_score(
                    query, 
                    k=k, 
                    filter=search_filter
                )
            else:
                # 不使用过滤器搜索（兼容模式）
                results = self.chroma_client.similarity_search_with_score(query, k=k)
            
            self.logger.info(f"成功搜索到 {len(results)} 条相似记忆（带分数），用户: {user_id or 'all'}")
            return results
            
        except Exception as e:
            error_msg = f"搜索记忆失败: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def get_user_memory_count(self, user_id: str) -> int:
        """
        获取用户记忆数量（估算）
        
        Args:
            user_id: 用户ID
            
        Returns:
            int: 用户记忆数量的估算值
            
        注意：
            由于Chroma向量数据库的特性，无法直接获取精确的记忆数量
            此方法提供一个基于搜索的估算值，可能不完全准确
            建议在应用层面维护准确的计数器
        """
        try:
            # 使用一个常见的查询词来估算记忆数量
            # 这不是精确的计数，而是一个合理的估算
            results = self.search_memories("记忆", user_id=user_id, k=1000)
            count = len(results)
            
            # 如果查询"记忆"没有结果，尝试其他通用词
            if count == 0:
                for query_word in ["我", "的", "是"]:
                    try:
                        results = self.search_memories(query_word, user_id=user_id, k=1000)
                        if len(results) > count:
                            count = len(results)
                            break
                    except:
                        continue
            
            self.logger.info(f"用户 {user_id} 估算记忆数量: {count} (注意：这是估算值)")
            return count
            
        except Exception as e:
            self.logger.warning(f"无法估算用户 {user_id} 的记忆数量: {str(e)}")
            return 0

    def delete_user_memories(self, user_id: str) -> bool:
        """
        删除用户的所有记忆
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 删除是否成功
            
        注意：
            这是一个危险操作，将删除用户的所有记忆数据
            建议在用户注销账户时使用
        """
        try:
            # 注意：Chroma的删除功能有限，这里提供一个基础实现
            # 实际生产环境中可能需要更复杂的删除逻辑
            
            self.logger.warning(f"正在删除用户 {user_id} 的所有记忆，这是一个不可逆操作")
            
            # 由于Chroma的限制，我们无法直接按过滤器删除
            # 这里只是记录警告，实际删除需要根据具体的Chroma版本实现
            self.logger.warning("当前Chroma版本不支持按元数据批量删除，需要手动实现")
            
            return False  # 返回False表示删除功能尚未完全实现
            
        except Exception as e:
            self.logger.error(f"删除用户记忆失败: {str(e)}")
            return False

    def get_memory_info(self):
        """
        获取记忆模块信息
        
        Returns:
            dict: 记忆模块的配置和状态信息
        """
        return {
            "persist_directory": settings.memory.persist_directory,
            "embedding_model": settings.memory.embedding_model,
            "embedding_provider": self.embedding_provider,
            "is_configured": settings.memory.is_configured() or bool(settings.memory.zhipu_api_key),
            "chroma_client": str(type(self.chroma_client)),
            "supports_user_isolation": True,  # 新增：标识支持用户隔离
            "version": "2.0.0"  # 新增：版本标识
        }

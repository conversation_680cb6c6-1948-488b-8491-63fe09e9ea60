"""
系统配置管理

统一管理所有配置项，包括数据库、服务器、短信、JWT、闪验等配置
从环境变量加载配置，提供默认值和验证
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class DatabaseConfig:
    """数据库配置"""
    # PostgreSQL配置
    pg_host: str = os.getenv('DATABASE_HOST', 'localhost')
    pg_port: int = int(os.getenv('DATABASE_PORT', '5433'))
    pg_database: str = os.getenv('DATABASE_NAME', 'mcp_db_local')
    pg_username: str = os.getenv('DATABASE_USER', 'mcp_db_local')
    pg_password: str = os.getenv('DATABASE_PASSWORD', 'mcp_db_local')
    
    # Qdrant向量数据库配置
    qdrant_url: str = os.getenv('QDRANT_URL', 'memory://local')
    qdrant_collection: str = os.getenv('QDRANT_COLLECTION', 'relationship_vectors')
    qdrant_api_key: Optional[str] = os.getenv('QDRANT_API_KEY')
    
    def get_pg_url(self) -> str:
        """获取PostgreSQL连接URL"""
        return f"postgresql://{self.pg_username}:{self.pg_password}@{self.pg_host}:{self.pg_port}/{self.pg_database}"

@dataclass 
class ServerConfig:
    """服务器配置"""
    name: str = os.getenv('SERVER_NAME', 'relationship_insights')
    port: int = int(os.getenv('SERVER_PORT', '8003'))
    log_level: str = os.getenv('LOG_LEVEL', 'INFO')
    debug: bool = os.getenv('DEBUG', 'true').lower() == 'true'

@dataclass
class SmsConfig:
    """短信配置"""
    account: str = os.getenv('SMS_ACCOUNT', '')
    password: str = os.getenv('SMS_PASSWORD', '') 
    signature: str = os.getenv('SMS_SIGNATURE', '【工禧云】')
    api_url: str = os.getenv('SMS_API_URL', 'https://smssh1.253.com/msg/v1/send/json')
    verification_template: str = os.getenv('SMS_VERIFICATION_TEMPLATE', '您的验证码是{code}，请在5分钟内按页面提示提交验证码，切勿泄露给他人，如非本人操作请忽略本短信。')
    
    # 开发模式配置
    dev_mode: bool = os.getenv('SMS_DEV_MODE', 'true').lower() == 'true'
    dev_code: str = os.getenv('SMS_DEV_CODE', '123456')
    
    def is_dev_mode(self) -> bool:
        """判断是否为开发模式"""
        return self.dev_mode or not (self.account and self.password)

@dataclass
class JwtConfig:
    """JWT配置"""
    secret_key: str = os.getenv('JWT_SECRET_KEY', 'your-super-secret-jwt-key')
    algorithm: str = os.getenv('JWT_ALGORITHM', 'HS256')
    access_token_expire_minutes: int = int(os.getenv('JWT_ACCESS_TOKEN_EXPIRE_MINUTES', '10080'))  # 7天
    refresh_token_expire_minutes: int = int(os.getenv('JWT_REFRESH_TOKEN_EXPIRE_MINUTES', '43200'))  # 30天

@dataclass
class FlashConfig:
    """创蓝云智闪验一键登录配置"""
    app_id: str = os.getenv('FLASH_APP_ID', '')
    app_key: str = os.getenv('FLASH_APP_KEY', '')
    
    # 置换手机号接口（移动端）
    mobile_query_url: str = os.getenv('FLASH_MOBILE_QUERY_URL', 'https://wsflash.253.com/open/flashsdk/mobile-query')
    
    # 本机校验接口（Web端）
    web_validate_url: str = os.getenv('FLASH_WEB_VALIDATE_URL', 'https://api.253.com/open/flashsdk/web-mobile-validate')
    
    # 加密方式：0=AES，1=RSA
    encrypt_type: int = int(os.getenv('FLASH_ENCRYPT_TYPE', '0'))
    
    # RSA公钥（如果使用RSA加密）
    rsa_public_key: Optional[str] = os.getenv('FLASH_RSA_PUBLIC_KEY')
    rsa_private_key: Optional[str] = os.getenv('FLASH_RSA_PRIVATE_KEY')
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return bool(self.app_id and self.app_key)

@dataclass
class VolcengineConfig:
    """火山引擎配置"""
    appid: str = os.getenv('VOLCENGINE_APPID', '')
    access_token: str = os.getenv('VOLCENGINE_ACCESS_TOKEN', '')
    cluster: str = os.getenv('VOLCENGINE_CLUSTER', '')
    service_url: str = os.getenv('VOLCENGINE_SERVICE_URL', 'https://openspeech.bytedance.com/api/v1/auc')
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return bool(self.appid and self.access_token and self.cluster)

@dataclass
class CommonServiceConfig:
    """Common服务配置"""
    host: str = os.getenv('COMMON_HOST', '0.0.0.0')
    port: int = int(os.getenv('COMMON_PORT', '8010'))
    reload: bool = os.getenv('COMMON_RELOAD', 'true').lower() == 'true'
    log_level: str = os.getenv('COMMON_LOG_LEVEL', 'info')

@dataclass
class TimezoneConfig:
    """时区配置"""
    default_timezone: str = os.getenv('DEFAULT_TIMEZONE', 'Asia/Shanghai')
    
    def get_timezone_name(self) -> str:
        """获取时区名称"""
        return self.default_timezone

@dataclass
class MemoryConfig:
    """记忆模块配置"""
    persist_directory: str = os.getenv('MEMORY_PERSIST_DIRECTORY', './chroma_db')
    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    embedding_model: str = os.getenv('MEMORY_EMBEDDING_MODEL', 'text-embedding-3-small')
    zhipu_api_key: str = os.getenv('ZHIPU_API_KEY', '')
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return bool(self.openai_api_key)

class Settings:
    """统一配置管理器"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.server = ServerConfig()
        self.sms = SmsConfig()
        self.jwt = JwtConfig()
        self.flash = FlashConfig()
        self.volcengine = VolcengineConfig()
        self.common_service = CommonServiceConfig()
        self.timezone = TimezoneConfig()
        self.memory = MemoryConfig()
    
    def get_info(self) -> Dict[str, Any]:
        """获取配置信息（隐藏敏感信息）"""
        return {
            "database": {
                "pg_host": self.database.pg_host,
                "pg_port": self.database.pg_port,
                "pg_database": self.database.pg_database,
                "pg_username": self.database.pg_username,
                "pg_password": "***" if self.database.pg_password else "未配置",
                "qdrant_url": self.database.qdrant_url if self.database.qdrant_url != "memory://local" else "本地内存模式",
                "qdrant_collection": self.database.qdrant_collection,
                "qdrant_api_key": "已配置" if self.database.qdrant_api_key else "未配置"
            },
            "server": {
                "name": self.server.name,
                "port": self.server.port,
                "log_level": self.server.log_level,
                "debug": self.server.debug
            },
            "sms": {
                "account": "***" if self.sms.account else "未配置",
                "signature": self.sms.signature,
                "api_url": self.sms.api_url
            },
            "jwt": {
                "algorithm": self.jwt.algorithm,
                "access_token_expire_minutes": self.jwt.access_token_expire_minutes,
                "refresh_token_expire_minutes": self.jwt.refresh_token_expire_minutes,
                "secret_key": "***" if self.jwt.secret_key != "your-super-secret-jwt-key" else "使用默认值"
            },
            "flash": {
                "app_id": "***" if self.flash.app_id else "未配置",
                "encrypt_type": "AES" if self.flash.encrypt_type == 0 else "RSA",
                "rsa_configured": "已配置" if self.flash.rsa_public_key else "未配置",
                "is_configured": self.flash.is_configured()
            },
            "volcengine": {
                "appid": "***" if self.volcengine.appid else "未配置",
                "access_token": "***" if self.volcengine.access_token else "未配置",
                "cluster": self.volcengine.cluster if self.volcengine.cluster else "未配置",
                "service_url": self.volcengine.service_url,
                "is_configured": self.volcengine.is_configured()
            },
            "common_service": {
                "host": self.common_service.host,
                "port": self.common_service.port,
                "reload": self.common_service.reload,
                "log_level": self.common_service.log_level
            },
            "timezone": {
                "default_timezone": self.timezone.default_timezone
            },
            "memory": {
                "persist_directory": self.memory.persist_directory,
                "openai_api_key": "***" if self.memory.openai_api_key else "未配置",
                "embedding_model": self.memory.embedding_model,
                "zhipu_api_key": "***" if self.memory.zhipu_api_key else "未配置",
                "is_configured": self.memory.is_configured()
            }
        }

# 全局配置实例
settings = Settings() 
"""
用户记录存储数据模型

记录用户的文本内容和相关信息
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import uuid4

from .base import BaseModel
from ..utils.validators import RequiredValidator
from ..connection.session_manager import DatabaseSession

@dataclass
class UserRecord(BaseModel):
    """用户记录存储数据模型"""
    
    _table_name: str = field(default="user_records", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    user_id: str = ""                                 # 用户ID
    content: str = ""                                 # 文本内容
    content_type: str = "text"                       # 内容类型
    is_deleted: bool = False                         # 是否删除
    deleted_at: Optional[datetime] = None            # 删除时间
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'user_id': [RequiredValidator()],
            'content': [RequiredValidator()],
            'content_type': [RequiredValidator()]
        })
    
    def _get_db_values(self) -> dict:
        """获取用于数据库操作的值，处理字段和UUID空值转换"""
        from dataclasses import fields
        
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 🎯 修复：处理 file_id 字段的空字符串问题（已移除，不再需要）
            
            values[field_name] = value
        
        return values
    
    def soft_delete(self):
        """软删除记录"""
        from ..utils.helpers import get_current_timestamp
        self.is_deleted = True
        self.deleted_at = get_current_timestamp()
    
    def restore(self):
        """恢复删除的记录"""
        self.is_deleted = False
        self.deleted_at = None 
    
    # 文件关联功能已移至 record_files 表，通过关联查询处理
    
    @classmethod
    async def find_by_user_id(cls, session: DatabaseSession, user_id: str) -> Optional['UserRecord']:
        """
        根据用户ID查找用户记录
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Returns:
            Optional[UserRecord]: 找到的用户记录，未找到返回None
        """
        query = f"SELECT * FROM {cls._table_name} WHERE user_id = $1 AND is_deleted = false ORDER BY created_at DESC LIMIT 1"
        row = await session.fetchrow(query, user_id)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def find_all_by_user_id(cls, session: DatabaseSession, user_id: str, limit: int = 10, offset: int = 0) -> List['UserRecord']:
        """
        根据用户ID查找所有用户记录
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[UserRecord]: 用户记录列表
        """
        query = f"SELECT * FROM {cls._table_name} WHERE user_id = $1 AND is_deleted = false ORDER BY created_at DESC LIMIT $2 OFFSET $3"
        rows = await session.fetch(query, user_id, limit, offset)
        
        return [cls.from_dict(dict(row)) for row in rows]
    
    # 根据文件ID查找用户记录功能已移至关联表查询
    
    @classmethod
    async def find_with_pagination(
        cls, 
        session: DatabaseSession, 
        user_id: Optional[str] = None,
        content_type: Optional[str] = None,
        # file_id 参数已移除，使用 record_files 表处理文件关联
        page: int = 1, 
        page_size: int = 10
    ) -> tuple[List['UserRecord'], int]:
        """
        分页查询用户记录，按时间线排序
        
        Args:
            session: 数据库会话
            user_id: 用户ID（可选）
            content_type: 内容类型（可选）
            file_id: 文件ID（可选）
            page: 页码，从1开始
            page_size: 每页大小
            
        Returns:
            tuple[List[UserRecord], int]: (记录列表, 总数)
        """
        # 构建WHERE条件
        where_conditions = ["is_deleted = false"]
        params = []
        param_index = 1
        
        if user_id:
            where_conditions.append(f"user_id = ${param_index}")
            params.append(user_id)
            param_index += 1
            
        if content_type:
            where_conditions.append(f"content_type = ${param_index}")
            params.append(content_type)
            param_index += 1
            
        # file_id 过滤已移至关联表查询
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE {where_clause}"
        total_count = await session.fetchval(count_query, *params)
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询记录
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE {where_clause} 
            ORDER BY created_at DESC 
            LIMIT ${param_index} OFFSET ${param_index + 1}
        """
        params.extend([page_size, offset])
        
        rows = await session.fetch(query, *params)
        records = [cls.from_dict(dict(row)) for row in rows]
        
        return records, total_count
    
    @classmethod
    async def find_with_mcp_tool_call(cls, session: DatabaseSession, user_id: Optional[str] = None, content_type: Optional[str] = None, file_id: Optional[str] = None, page: int = 1, page_size: int = 10) -> tuple[List['UserRecord'], int]:
        """
        分页查询用户记录，关联查询mcp_tool_call的output_data
        """
        

        pass
    
    @classmethod 
    async def count_by_user_id(cls, session: DatabaseSession, user_id: str) -> int:
        """
        统计用户的记录总数
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Returns:
            int: 记录总数
        """
        query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE user_id = $1 AND is_deleted = false"
        return await session.fetchval(query, user_id)
    
    # 统计关联到指定文件的记录总数功能已移至关联表查询 
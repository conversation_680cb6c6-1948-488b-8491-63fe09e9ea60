"""
情绪支持服务数据模型

记录情绪支持服务的交互数据
"""

import json
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from uuid import uuid4

from .base import BaseModel
from ..utils.validators import RequiredValidator

@dataclass
class EmotionSupport(BaseModel):
    """情绪支持服务数据模型"""
    
    _table_name: str = field(default="emotion_support", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    user_id: str = ""                                 # 用户ID
    mcp_tool_call_id: str = ""                       # 关联的工具调用ID
    input_data: Optional[Dict[str, Any]] = None       # 输入的数据
    response_data: Optional[Dict[str, Any]] = None    # 回复的数据
    support_type: str = ""                           # 支持类型
    satisfaction_score: Optional[int] = None         # 用户满意度评分（1-5）
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'user_id': [RequiredValidator()],
            'mcp_tool_call_id': [RequiredValidator()],
            'support_type': [RequiredValidator()]
        })
    
    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理JSON序列化
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        from dataclasses import fields
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 处理JSON字段序列化
            if field_name in ['input_data', 'response_data']:
                if value is None:
                    values[field_name] = None
                elif isinstance(value, dict):
                    # 将字典序列化为JSON字符串
                    values[field_name] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                else:
                    # 如果已经是字符串，直接使用
                    values[field_name] = value
            else:
                values[field_name] = value
        
        return values 
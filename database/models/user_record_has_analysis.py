"""
用户记录与分析的关联表数据模型

用于关联用户记录与各种分析结果（MCP工具调用、情绪分析等）
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import uuid4

from .base import BaseModel
from ..utils.validators import RequiredValidator
from ..connection.session_manager import DatabaseSession

@dataclass
class UserRecordHasAnalysis(BaseModel):
    """用户记录与分析的关联表数据模型"""

    _table_name: str = field(default="user_record_has_analysis", init=False)

    # 重写id字段，使用UUID生成
    id: str = field(default_factory=lambda: str(uuid4()))
    
    user_record_id: str = ""                        # 用户记录ID（外键）
    mcp_tool_call_id: str = ""                      # MCP工具调用ID（外键）
    type: str = "tool_call"                         # 关联类型，默认为'tool_call'

    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()

        self._validators.update({
            'user_record_id': [RequiredValidator()],
            'mcp_tool_call_id': [RequiredValidator()],
            'type': [RequiredValidator()]
        })

    def _get_db_values(self) -> dict:
        """获取用于数据库操作的值"""
        from dataclasses import fields
        
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            values[field_name] = getattr(self, field_name)
        return values
    
    @classmethod
    async def create_association(
        cls, 
        session: DatabaseSession, 
        user_record_id: str, 
        mcp_tool_call_id: str,
        association_type: str = "tool_call"
    ) -> 'UserRecordHasAnalysis':
        """
        创建用户记录与MCP工具调用的关联
        
        Args:
            session: 数据库会话
            user_record_id: 用户记录ID
            mcp_tool_call_id: MCP工具调用ID
            association_type: 关联类型，默认为'tool_call'
            
        Returns:
            UserRecordHasAnalysis: 创建的关联记录
        """
        association = cls(
            user_record_id=user_record_id,
            mcp_tool_call_id=mcp_tool_call_id,
            type=association_type
        )
        await association.save(session)
        return association
    
    @classmethod
    async def find_by_user_record_id(
        cls, 
        session: DatabaseSession, 
        user_record_id: str
    ) -> List['UserRecordHasAnalysis']:
        """
        根据用户记录ID查找所有关联信息
        
        Args:
            session: 数据库会话
            user_record_id: 用户记录ID
            
        Returns:
            List[UserRecordHasAnalysis]: 关联信息列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE user_record_id = $1
            ORDER BY created_at DESC
        """
        
        rows = await session.fetch(query, user_record_id)
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def find_by_mcp_tool_call_id(
        cls, 
        session: DatabaseSession, 
        mcp_tool_call_id: str
    ) -> List['UserRecordHasAnalysis']:
        """
        根据MCP工具调用ID查找所有关联信息
        
        Args:
            session: 数据库会话
            mcp_tool_call_id: MCP工具调用ID
            
        Returns:
            List[UserRecordHasAnalysis]: 关联信息列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE mcp_tool_call_id = $1
            ORDER BY created_at DESC
        """
        
        rows = await session.fetch(query, mcp_tool_call_id)
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def find_by_type(
        cls, 
        session: DatabaseSession, 
        association_type: str
    ) -> List['UserRecordHasAnalysis']:
        """
        根据关联类型查找所有关联信息
        
        Args:
            session: 数据库会话
            association_type: 关联类型
            
        Returns:
            List[UserRecordHasAnalysis]: 关联信息列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE type = $1
            ORDER BY created_at DESC
        """
        
        rows = await session.fetch(query, association_type)
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def find_by_type_with_pagination(
        cls, 
        session: DatabaseSession, 
        association_type: str,
        limit: int = 10, 
        offset: int = 0
    ) -> List['UserRecordHasAnalysis']:
        """
        根据关联类型分页查找关联信息
        
        Args:
            session: 数据库会话
            association_type: 关联类型
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[UserRecordHasAnalysis]: 关联信息列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE type = $1
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
        """
        
        rows = await session.fetch(query, association_type, limit, offset)
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def find_with_pagination(
        cls, 
        session: DatabaseSession, 
        user_record_id: Optional[str] = None,
        association_type: Optional[str] = None,
        page: int = 1, 
        page_size: int = 10
    ) -> tuple[List['UserRecordHasAnalysis'], int]:
        """
        分页查询关联记录
        
        Args:
            session: 数据库会话
            user_record_id: 用户记录ID（可选）
            association_type: 关联类型（可选）
            page: 页码，从1开始
            page_size: 每页大小
            
        Returns:
            tuple[List[UserRecordHasAnalysis], int]: (记录列表, 总数)
        """
        # 构建WHERE条件
        where_conditions = []
        params = []
        param_index = 1
        
        if user_record_id:
            where_conditions.append(f"user_record_id = ${param_index}")
            params.append(user_record_id)
            param_index += 1
            
        if association_type:
            where_conditions.append(f"type = ${param_index}")
            params.append(association_type)
            param_index += 1
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询总数
        count_query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE {where_clause}"
        total_count = await session.fetchval(count_query, *params)
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询记录
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE {where_clause} 
            ORDER BY created_at DESC 
            LIMIT ${param_index} OFFSET ${param_index + 1}
        """
        params.extend([page_size, offset])
        
        rows = await session.fetch(query, *params)
        records = [cls.from_dict(dict(row)) for row in rows]
        
        return records, total_count
    
    @classmethod
    async def count_by_type(cls, session: DatabaseSession, association_type: str) -> int:
        """
        统计指定关联类型的记录总数
        
        Args:
            session: 数据库会话
            association_type: 关联类型
            
        Returns:
            int: 记录总数
        """
        query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE type = $1"
        return await session.fetchval(query, association_type)
    
    @classmethod
    async def create_mcp_association(
        cls,
        db_session: DatabaseSession,
        user_record_id: str,
        mcp_tool_call_id: str,
        analysis_type: str = "mcp_call"
    ) -> 'UserRecordHasAnalysis':
        """
        创建MCP工具调用关联
        
        Args:
            db_session: 数据库会话
            user_record_id: 用户记录ID
            mcp_tool_call_id: MCP工具调用ID
            analysis_type: 分析类型
            
        Returns:
            UserRecordHasAnalysis: 创建的关联记录
        """
        return await cls.create_association(
            session=db_session,
            user_record_id=user_record_id,
            mcp_tool_call_id=mcp_tool_call_id,
            association_type=analysis_type
        )
    
    @classmethod
    async def create_emotion_association(
        cls,
        db_session: DatabaseSession,
        user_record_id: str,
        emotion_analysis_id: str,
        analysis_type: str = "emotion_analysis"
    ) -> 'UserRecordHasAnalysis':
        """
        创建情绪分析关联
        
        注意：当前表结构不支持情绪分析关联，此方法保留用于兼容性
        
        Args:
            db_session: 数据库会话
            user_record_id: 用户记录ID
            emotion_analysis_id: 情绪分析ID (此参数暂不使用)
            analysis_type: 分析类型
            
        Returns:
            UserRecordHasAnalysis: 创建的关联记录
        """
        # 由于当前表结构中没有emotion_analysis_id字段，
        # 我们创建一个特殊的记录来标识这是情绪分析关联
        association = cls(
            user_record_id=user_record_id,
            mcp_tool_call_id=emotion_analysis_id,  # 临时使用mcp_tool_call_id存储emotion_analysis_id
            type=analysis_type
        )
        await association.save(db_session)
        return association
    
    @classmethod
    async def find_by_user_record_ids(
        cls,
        session: DatabaseSession,
        user_record_ids: List[str]
    ) -> List['UserRecordHasAnalysis']:
        """
        根据用户记录ID列表查找所有关联信息
        
        Args:
            session: 数据库会话
            user_record_ids: 用户记录ID列表
            
        Returns:
            List[UserRecordHasAnalysis]: 关联信息列表
        """
        if not user_record_ids:
            return []
        
        # 构建IN子句的占位符
        placeholders = ','.join(f'${i+1}' for i in range(len(user_record_ids)))
        
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE user_record_id IN ({placeholders})
            ORDER BY created_at DESC
        """
        
        rows = await session.fetch(query, *user_record_ids)
        return [cls.from_dict(dict(row)) for row in rows]
    
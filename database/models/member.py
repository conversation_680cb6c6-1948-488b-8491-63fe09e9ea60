"""
成员的数据模型
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import json
from enum import Enum
from uuid import uuid4
from .base import BaseModel
from ..utils.validators import RequiredValidator, ChoiceValidator, JSONValidator
from ..connection.session_manager import DatabaseSession

class MemberType(Enum):
    """成员类型"""
    CHILD = "child"
    FATHER = "father"
    MOTHER = "mother"

@dataclass
class Member(BaseModel):
    """成员数据模型"""
    
    # 表名
    _table_name: str = field(default="members", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    # 用户ID - 关联用户表
    user_id: str = ""
    
    # 成员类型
    type: MemberType = field(default=MemberType.CHILD)
    
    # 成员信息 - 存储JSON数据
    info: Optional[Dict[str, Any]] = field(default_factory=dict)

    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'user_id': [
                RequiredValidator()
            ],
            'type': [
                RequiredValidator(),
                ChoiceValidator([MemberType.CHILD, MemberType.FATHER, MemberType.MOTHER])
            ],
            'info': [
                RequiredValidator(),
                JSONValidator()
            ]
        })

    def to_dict(self, exclude_fields: list = None) -> dict:
        """转换为字典，处理枚举类型"""
        result = super().to_dict(exclude_fields)
        
        if 'type' in result and isinstance(self.type, MemberType):
            result['type'] = self.type.value
            
        return result
    
    def _get_db_values(self) -> dict:   
        """获取用于数据库操作的值，处理枚举类型转换和JSON序列化"""
        from dataclasses import fields
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
                
            value = getattr(self, field_name)

            # 处理枚举类型转换
            if isinstance(value, MemberType):
                values[field_name] = value.value
            # 处理JSON字段序列化
            elif field_name == 'info':
                if value is None:
                    values[field_name] = None
                elif isinstance(value, dict):
                    # 将字典序列化为JSON字符串
                    values[field_name] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                else:
                    # 如果已经是字符串，直接使用
                    values[field_name] = value
            else:
                values[field_name] = value
                
        return values
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Member':
        """从字典创建Member实例，处理特殊字段转换"""
        # 处理type字段的枚举转换
        if 'type' in data and isinstance(data['type'], str):
            try:
                data['type'] = MemberType(data['type'])
            except ValueError:
                data['type'] = MemberType.CHILD  # 默认值
        
        # 处理info字段的JSON反序列化
        if 'info' in data and isinstance(data['info'], str):
            try:
                data['info'] = json.loads(data['info'])
            except (json.JSONDecodeError, TypeError):
                data['info'] = {}  # 默认空字典
        
        return super().from_dict(data)
    
    @classmethod
    async def find_by_user(cls, session: DatabaseSession, user_id: str, limit: int = 50) -> list:
        """根据用户ID查找成员"""
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE user_id = $1 
            ORDER BY created_at DESC 
            LIMIT $2
        """
        rows = await session.fetch(query, user_id, limit)
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def create_member(cls, session: DatabaseSession, user_id: str, type: str, info: dict) -> 'Member':
        """
        创建成员
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            type: 成员类型字符串
            info: 成员信息字典
            
        Returns:
            Member: 创建的成员实例
        """
        # 转换type字符串为枚举
        member_type = MemberType(type) if isinstance(type, str) else type
        
        # 创建成员实例
        member = cls(
            user_id=user_id, 
            type=member_type, 
            info=info or {}
        )
        
        # 保存到数据库
        await member.save(session)
        return member
    
    @classmethod
    async def update_member(cls, session: DatabaseSession, member_id: str, info: dict) -> Optional['Member']:
        """
        更新成员信息
        
        Args:
            session: 数据库会话
            member_id: 成员ID
            info: 更新的信息字典
            
        Returns:
            Optional[Member]: 更新后的成员实例，不存在返回None
        """
        member = await cls.find_by_id(session, member_id)
        if not member:
            return None
        
        # 更新信息
        member.info = info or {}
        await member.save(session)
        return member
    
    @classmethod
    async def delete_member(cls, session: DatabaseSession, member_id: str) -> bool:
        """
        删除成员
        
        Args:
            session: 数据库会话
            member_id: 成员ID
            
        Returns:
            bool: 删除是否成功
        """
        member = await cls.find_by_id(session, member_id)
        if not member:
            return False
        
        await member.delete(session)
        return True
    
    @classmethod
    async def find_by_id(cls, session: DatabaseSession, member_id: str) -> Optional['Member']:
        """
        根据ID查找成员
        
        Args:
            session: 数据库会话
            member_id: 成员ID
            
        Returns:
            Optional[Member]: 找到的成员实例，未找到返回None
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE id = $1
        """
        row = await session.fetchrow(query, member_id)
        return cls.from_dict(dict(row)) if row else None


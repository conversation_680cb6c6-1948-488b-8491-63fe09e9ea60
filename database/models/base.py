"""
基础数据模型

提供所有数据模型的基础功能
包括CRUD操作、数据验证、序列化等通用方法
"""

import json
import uuid
from typing import Any, Dict, List, Optional, Type, Union
from datetime import datetime
from dataclasses import dataclass, field, fields

from ..utils.exceptions import ModelError, ValidationError, NotFoundError
from ..utils.helpers import generate_uuid, get_current_timestamp, safe_json_dumps, safe_json_loads
from ..utils.validators import UUIDValidator, RequiredValidator
from ..connection.session_manager import DatabaseSession

@dataclass
class BaseModel:
    """
    基础数据模型类
    
    所有数据模型的基类，提供通用的CRUD操作
    包括数据验证、序列化、持久化等功能
    """
    
    # 基础字段 - 子类可重写id字段的生成方式
    id: str = ""  # 默认空字符串，子类需要赋值
    created_at: datetime = field(default_factory=get_current_timestamp)
    updated_at: datetime = field(default_factory=get_current_timestamp)
    
    # 类属性，子类需要重写
    _table_name: str = ""
    _validators: Dict[str, list] = field(default_factory=dict, init=False)
    
    def __post_init__(self):
        """
        初始化后处理
        
        设置验证器和执行基础验证
        """
        self._setup_validators()
        # 只在有id值时才验证
        if self.id:
            self.validate()
    
    def _setup_validators(self):
        """
        设置字段验证器
        
        子类可以重写此方法来添加自定义验证器
        基类不设置id验证器，由子类决定
        """
        self._validators = {
            # 基类不设置id验证器，由子类决定
        }
    
    def validate(self) -> bool:
        """
        验证模型数据
        
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        for field_name, validators in self._validators.items():
            if hasattr(self, field_name):
                value = getattr(self, field_name)
                for validator in validators:
                    validator.validate(value, field_name)
        return True
    
    def to_dict(self, exclude_fields: List[str] = None) -> Dict[str, Any]:
        """
        将模型转换为字典
        
        Args:
            exclude_fields: 要排除的字段列表
            
        Returns:
            Dict[str, Any]: 模型数据字典
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for field_obj in fields(self):
            field_name = field_obj.name
            if field_name.startswith('_') or field_name in exclude_fields:
                continue
                
            value = getattr(self, field_name)
            
            # 处理特殊类型的序列化
            if isinstance(value, datetime):
                result[field_name] = value.isoformat()
            elif isinstance(value, uuid.UUID):
                result[field_name] = str(value)
            else:
                result[field_name] = value
        
        return result
    
    def to_json(self, exclude_fields: List[str] = None) -> str:
        """
        将模型转换为JSON字符串
        
        Args:
            exclude_fields: 要排除的字段列表
            
        Returns:
            str: JSON字符串
        """
        data = self.to_dict(exclude_fields)
        return safe_json_dumps(data)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """
        从字典创建模型实例
        
        Args:
            data: 数据字典
            
        Returns:
            BaseModel: 模型实例
        """
        # 获取类的字段定义
        field_names = {f.name for f in fields(cls)}
        
        # 过滤数据，只保留模型字段
        filtered_data = {k: v for k, v in data.items() if k in field_names}
        
        # 处理日期时间字段
        for field_obj in fields(cls):
            field_name = field_obj.name
            if field_name in filtered_data:
                value = filtered_data[field_name]
                if field_obj.type == datetime and isinstance(value, str):
                    try:
                        filtered_data[field_name] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except ValueError:
                        pass  # 保持原值
        
        return cls(**filtered_data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseModel':
        """
        从JSON字符串创建模型实例
        
        Args:
            json_str: JSON字符串
            
        Returns:
            BaseModel: 模型实例
            
        Raises:
            ValidationError: JSON格式无效
        """
        data = safe_json_loads(json_str)
        if data is None:
            raise ValidationError("json_data", "无效的JSON格式", json_str)
        return cls.from_dict(data)
    
    async def save(self, session: DatabaseSession) -> 'BaseModel':
        """
        保存模型到数据库
        
        根据是否有ID决定是插入还是更新
        
        Args:
            session: 数据库会话
            
        Returns:
            BaseModel: 保存后的模型实例
            
        Raises:
            ModelError: 保存失败
        """
        try:
            # 验证数据
            self.validate()
            
            # 更新时间戳
            self.updated_at = get_current_timestamp()
            
            # 检查记录是否存在
            exists = await self._exists(session)
            
            if exists:
                return await self._update(session)
            else:
                return await self._insert(session)
                
        except Exception as e:
            raise ModelError(self.__class__.__name__, "save", str(e), e)
    
    async def delete(self, session: DatabaseSession) -> bool:
        """
        从数据库删除模型
        
        Args:
            session: 数据库会话
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            ModelError: 删除失败
        """
        try:
            if not self.id:
                raise ModelError(self.__class__.__name__, "delete", "无法删除没有ID的记录")
            
            query = f"DELETE FROM {self._table_name} WHERE id = $1"
            result = await session.execute(query, self.id)
            
            # 检查是否删除了记录
            return "DELETE 1" in result
            
        except Exception as e:
            raise ModelError(self.__class__.__name__, "delete", str(e), e)
    
    async def _exists(self, session: DatabaseSession) -> bool:
        """
        检查记录是否存在
        
        Args:
            session: 数据库会话
            
        Returns:
            bool: 记录是否存在
        """
        if not self.id:
            return False
        
        query = f"SELECT 1 FROM {self._table_name} WHERE id = $1"
        result = await session.fetchval(query, self.id)
        return result is not None
    
    async def _insert(self, session: DatabaseSession) -> 'BaseModel':
        """
        插入新记录
        
        Args:
            session: 数据库会话
            
        Returns:
            BaseModel: 插入后的模型实例
        """
        # 检查子类是否提供了_get_db_values方法
        if hasattr(self, '_get_db_values') and callable(getattr(self, '_get_db_values')):
            # 使用子类的方法获取数据库值
            db_values = self._get_db_values()
            field_names = list(db_values.keys())
            field_values = list(db_values.values())
        else:
            # 使用默认方法获取所有字段和值
            field_names = []
            field_values = []
            
            for field_obj in fields(self):
                field_name = field_obj.name
                if field_name.startswith('_'):
                    continue
                field_names.append(field_name)
                field_values.append(getattr(self, field_name))
        
        # 构建SQL语句
        columns = ', '.join(field_names)
        placeholders = ', '.join([f'${i+1}' for i in range(len(field_values))])
        query = f"INSERT INTO {self._table_name} ({columns}) VALUES ({placeholders}) RETURNING id"
        
        # 执行插入
        result_id = await session.fetchval(query, *field_values)
        self.id = result_id
        
        return self
    
    async def _update(self, session: DatabaseSession) -> 'BaseModel':
        """
        更新现有记录
        
        Args:
            session: 数据库会话
            
        Returns:
            BaseModel: 更新后的模型实例
        """
        # 检查子类是否提供了_get_db_values方法
        if hasattr(self, '_get_db_values') and callable(getattr(self, '_get_db_values')):
            # 使用子类的方法获取数据库值
            db_values = self._get_db_values()
            # 过滤掉ID字段
            field_names = [k for k in db_values.keys() if k != 'id']
            field_values = [db_values[k] for k in field_names]
        else:
            # 使用默认方法获取除ID外的所有字段和值
            field_names = []
            field_values = []
            
            for field_obj in fields(self):
                field_name = field_obj.name
                if field_name.startswith('_') or field_name == 'id':
                    continue
                field_names.append(field_name)
                field_values.append(getattr(self, field_name))
        
        # 构建SQL语句
        set_clause = ', '.join([f'{name} = ${i+1}' for i, name in enumerate(field_names)])
        query = f"UPDATE {self._table_name} SET {set_clause} WHERE id = ${len(field_values)+1}"
        
        # 执行更新
        field_values.append(self.id)  # 添加WHERE条件的ID值
        await session.execute(query, *field_values)
        
        return self
    
    @classmethod
    async def find_by_id(cls, session: DatabaseSession, record_id: str) -> Optional['BaseModel']:
        """
        根据ID查找记录
        
        Args:
            session: 数据库会话
            record_id: 记录ID
            
        Returns:
            Optional[BaseModel]: 找到的模型实例，未找到返回None
        """
        query = f"SELECT * FROM {cls._table_name} WHERE id = $1"
        row = await session.fetchrow(query, record_id)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def find_all(cls, session: DatabaseSession, limit: int = 100, offset: int = 0) -> List['BaseModel']:
        """
        查找所有记录
        
        Args:
            session: 数据库会话
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[BaseModel]: 模型实例列表
        """
        query = f"SELECT * FROM {cls._table_name} ORDER BY created_at DESC LIMIT $1 OFFSET $2"
        rows = await session.fetch(query, limit, offset)
        
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def count(cls, session: DatabaseSession) -> int:
        """
        获取记录总数
        
        Args:
            session: 数据库会话
            
        Returns:
            int: 记录总数
        """
        query = f"SELECT COUNT(*) FROM {cls._table_name}"
        return await session.fetchval(query)
    
    def __str__(self) -> str:
        """
        返回模型的字符串表示
        
        Returns:
            str: 模型信息字符串
        """
        return f"{self.__class__.__name__}(id={self.id})"
    
    def __repr__(self) -> str:
        """
        返回模型的详细字符串表示
        
        Returns:
            str: 模型详细信息字符串
        """
        fields_str = ', '.join([f"{k}={v}" for k, v in self.to_dict().items()])
        return f"{self.__class__.__name__}({fields_str})" 
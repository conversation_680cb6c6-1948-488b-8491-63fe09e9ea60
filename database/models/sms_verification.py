"""
短信验证码数据模型

用于存储和管理短信验证码
"""

from dataclasses import dataclass, field
from typing import Optional
from datetime import datetime, timedelta
from uuid import uuid4
import random
import string

from .base import BaseModel
from ..utils.validators import RequiredValidator, LengthValidator
from ..utils.helpers import get_current_timestamp
from ..connection.session_manager import DatabaseSession

@dataclass
class SmsVerification(BaseModel):
    """
    短信验证码数据模型
    
    用于存储短信验证码及其相关信息
    包含验证码生成、验证、过期处理等功能
    """
    
    _table_name: str = field(default="sms_verifications", init=False)
    
    # 主键使用UUID
    id: str = field(default_factory=lambda: str(uuid4()))
    
    phone_number: str = ""                          # 手机号
    verification_code: str = ""                     # 验证码
    purpose: str = "login"                          # 用途（login/register/reset等）
    
    # 状态管理
    is_used: bool = False                           # 是否已使用
    is_expired: bool = False                        # 是否已过期
    attempts: int = 0                               # 验证尝试次数
    max_attempts: int = 3                           # 最大尝试次数
    
    # 时间管理
    expires_at: Optional[datetime] = None           # 过期时间
    used_at: Optional[datetime] = None              # 使用时间
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'phone_number': [
                RequiredValidator(),
                LengthValidator(min_length=11, max_length=11)
            ],
            'verification_code': [
                RequiredValidator(),
                LengthValidator(min_length=6, max_length=6)
            ],
            'purpose': [
                RequiredValidator(),
                LengthValidator(max_length=50)
            ]
        })
    
    @staticmethod
    def generate_code(length: int = 6) -> str:
        """
        生成验证码
        
        Args:
            length: 验证码长度，默认6位
            
        Returns:
            str: 生成的验证码
        """
        return ''.join(random.choices(string.digits, k=length))
    
    @classmethod
    async def create_verification(
        cls, 
        session: DatabaseSession, 
        phone_number: str, 
        purpose: str = "login",
        expires_minutes: int = 5
    ) -> 'SmsVerification':
        """
        创建新的验证码记录
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            purpose: 验证码用途
            expires_minutes: 过期时间（分钟）
            
        Returns:
            SmsVerification: 创建的验证码实例
        """
        # 先将该手机号的未使用验证码标记为过期
        await cls.expire_unused_codes(session, phone_number, purpose)
        
        # 检查开发模式
        from config.settings import settings
        if settings.sms.is_dev_mode():
            # 开发模式：使用固定验证码
            code = settings.sms.dev_code
        else:
            # 生产模式：生成随机验证码
            code = cls.generate_code()
        
        expires_at = get_current_timestamp() + timedelta(minutes=expires_minutes)
        
        verification = cls(
            phone_number=phone_number,
            verification_code=code,
            purpose=purpose,
            expires_at=expires_at,
            created_at=get_current_timestamp(),
            updated_at=get_current_timestamp()
        )
        
        return await verification.save(session)
    
    @classmethod
    async def expire_unused_codes(cls, session: DatabaseSession, phone_number: str, purpose: str):
        """
        将指定手机号和用途的未使用验证码标记为过期
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            purpose: 验证码用途
        """
        query = f"""
            UPDATE {cls._table_name} 
            SET is_expired = true, updated_at = $1
            WHERE phone_number = $2 AND purpose = $3 AND is_used = false AND is_expired = false
        """
        await session.execute(query, get_current_timestamp(), phone_number, purpose)
    
    @classmethod
    async def find_valid_code(
        cls, 
        session: DatabaseSession, 
        phone_number: str, 
        verification_code: str, 
        purpose: str = "login"
    ) -> Optional['SmsVerification']:
        """
        查找有效的验证码
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            verification_code: 验证码
            purpose: 验证码用途
            
        Returns:
            Optional[SmsVerification]: 找到的验证码记录，未找到或无效返回None
        """
        # 检查开发模式
        from config.settings import settings
        
        if settings.sms.is_dev_mode() and verification_code == settings.sms.dev_code:
            # 开发模式：验证固定验证码，查找最新的有效验证码记录
            # 注意：在开发模式下，不验证具体的验证码，只要输入的是固定验证码就行
            query = f"""
                SELECT * FROM {cls._table_name} 
                WHERE phone_number = $1 
                    AND purpose = $2 
                    AND is_used = false 
                    AND is_expired = false 
                    AND expires_at > $3
                ORDER BY created_at DESC 
                LIMIT 1
            """
            
            row = await session.fetchrow(
                query, 
                phone_number, 
                purpose, 
                get_current_timestamp()
            )
            
            if row:
                return cls.from_dict(dict(row))
        else:
            # 生产模式：按正常流程验证，验证码必须完全匹配
            query = f"""
                SELECT * FROM {cls._table_name} 
                WHERE phone_number = $1 
                    AND verification_code = $2 
                    AND purpose = $3 
                    AND is_used = false 
                    AND is_expired = false 
                    AND expires_at > $4
                    AND attempts < max_attempts
                ORDER BY created_at DESC 
                LIMIT 1
            """
            
            row = await session.fetchrow(
                query, 
                phone_number, 
                verification_code, 
                purpose, 
                get_current_timestamp()
            )
            
            if row:
                return cls.from_dict(dict(row))
        
        return None
    
    async def verify(self, session: DatabaseSession, input_code: str) -> bool:
        """
        验证验证码
        
        Args:
            session: 数据库会话
            input_code: 用户输入的验证码
            
        Returns:
            bool: 验证是否成功
        """
        # 增加尝试次数
        self.attempts += 1
        self.updated_at = get_current_timestamp()
        
        # 检查是否已过期
        if self.is_expired or (self.expires_at and self.expires_at < get_current_timestamp()):
            self.is_expired = True
            await self.save(session)
            return False
        
        # 检查尝试次数
        if self.attempts > self.max_attempts:
            self.is_expired = True
            await self.save(session)
            return False
        
        # 验证验证码
        if self.verification_code == input_code and not self.is_used:
            self.is_used = True
            self.used_at = get_current_timestamp()
            await self.save(session)
            return True
        
        # 验证失败，保存尝试次数
        await self.save(session)
        return False
    
    def is_valid(self) -> bool:
        """
        检查验证码是否有效
        
        Returns:
            bool: 验证码是否有效
        """
        now = get_current_timestamp()
        return (
            not self.is_used and 
            not self.is_expired and
            self.expires_at and self.expires_at > now and
            self.attempts < self.max_attempts
        )
    
    @classmethod
    async def cleanup_expired(cls, session: DatabaseSession, days_old: int = 7):
        """
        清理过期的验证码记录
        
        Args:
            session: 数据库会话
            days_old: 清理多少天前的记录
        """
        cutoff_date = get_current_timestamp() - timedelta(days=days_old)
        query = f"DELETE FROM {cls._table_name} WHERE created_at < $1"
        await session.execute(query, cutoff_date)

    @classmethod
    async def delete_user_records(cls, session: DatabaseSession, phone_number: str) -> int:
        """
        删除指定手机号的所有短信验证记录
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            
        Returns:
            int: 删除的记录数量
        """
        # 先查询要删除的记录数量
        count_query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE phone_number = $1"
        count_result = await session.fetchval(count_query, phone_number)
        
        # 执行删除操作
        delete_query = f"DELETE FROM {cls._table_name} WHERE phone_number = $1"
        await session.execute(delete_query, phone_number)
        
        return count_result or 0

    @classmethod
    async def count_user_records(cls, session: DatabaseSession, phone_number: str) -> int:
        """
        统计指定手机号的短信验证记录数量
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            
        Returns:
            int: 记录数量
        """
        query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE phone_number = $1"
        result = await session.fetchval(query, phone_number)
        return result or 0 
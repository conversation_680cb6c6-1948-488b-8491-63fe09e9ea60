"""
调用日志数据模型

记录工具调用的日志信息
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from enum import Enum
from uuid import uuid4
import json

from .base import BaseModel
from ..utils.validators import RequiredValidator, ChoiceValidator

class LogLevel(Enum):
    """日志级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    DEBUG = "debug"

@dataclass
class CallLog(BaseModel):
    """调用日志数据模型"""
    
    _table_name: str = field(default="call_logs", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    mcp_tool_call_id: str = ""                       # 关联的工具调用ID
    log_level: LogLevel = LogLevel.INFO              # 日志级别
    message: str = ""                                # 日志消息
    metadata: Optional[Dict[str, Any]] = None        # 额外的元数据
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'mcp_tool_call_id': [RequiredValidator()],
            'log_level': [
                RequiredValidator(),
                ChoiceValidator([level.value for level in LogLevel])
            ],
            'message': [RequiredValidator()]
        })
    
    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理枚举类型转换和JSON序列化
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        from dataclasses import fields
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 处理枚举类型转换
            if isinstance(value, LogLevel):
                values[field_name] = value.value
            # 处理JSON字段序列化
            elif field_name == 'metadata':
                if value is None:
                    values[field_name] = None
                elif isinstance(value, dict):
                    # 将字典序列化为JSON字符串
                    values[field_name] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                else:
                    # 如果已经是字符串，直接使用
                    values[field_name] = value
            else:
                values[field_name] = value
        
        return values
    
    def to_dict(self, exclude_fields: list = None) -> dict:
        """转换为字典，处理枚举类型"""
        result = super().to_dict(exclude_fields)
        
        if 'log_level' in result and isinstance(self.log_level, LogLevel):
            result['log_level'] = self.log_level.value
            
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'CallLog':
        """从字典创建实例，处理枚举类型"""
        if 'log_level' in data and isinstance(data['log_level'], str):
            data['log_level'] = LogLevel(data['log_level'])
            
        return super().from_dict(data) 
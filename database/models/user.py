"""
用户数据模型

用户表模型，支持访客、微信授权登录和手机号登录三种模式
包含用户基本信息、登录状态等
"""

from dataclasses import dataclass, field, fields
from typing import Optional, List
from datetime import datetime
from enum import Enum
import re

from .base import BaseModel
from ..utils.validators import LengthValidator, ChoiceValidator, RequiredValidator
from ..utils.helpers import get_current_timestamp
from ..connection.session_manager import DatabaseSession

class UserType(Enum):
    """用户类型枚举"""
    GUEST = "guest"      # 访客用户
    WECHAT = "wechat"    # 微信用户
    PHONE = "phone"      # 手机号用户

class UserStatus(Enum):
    """用户状态枚举"""
    ACTIVE = "active"      # 活跃用户
    INACTIVE = "inactive"  # 非活跃用户
    BANNED = "banned"      # 被封禁用户

@dataclass
class User(BaseModel):
    """
    用户数据模型
    
    表示系统中的用户，支持访客、微信登录和手机号登录三种模式
    包含用户的基本信息、状态管理等功能
    """
    
    # 表名
    _table_name: str = field(default="users", init=False)
    
    # 重写id字段，不自动生成UUID
    id: str = ""  # 前端生成的用户ID或系统生成
    
    # 用户基本信息
    username: Optional[str] = None                    # 用户名（访客模式下为空）
    phone_number: Optional[str] = None               # 手机号
    wechat_openid: Optional[str] = None              # 微信openid
    wechat_unionid: Optional[str] = None             # 微信unionid
    nickname: str = "匿名用户"                        # 昵称
    avatar_url: Optional[str] = None                 # 头像链接
    
    # 用户状态
    user_type: UserType = UserType.GUEST             # 用户类型
    status: UserStatus = UserStatus.ACTIVE          # 用户状态
    
    # 时间字段
    last_login_at: Optional[datetime] = None         # 最后登录时间
    phone_verified_at: Optional[datetime] = None    # 手机号验证时间
    is_guide: bool = False                         # 是否为引导用户 
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'id': [
                RequiredValidator(),
                LengthValidator(min_length=1, max_length=255)
            ],
            'nickname': [
                RequiredValidator(),
                LengthValidator(min_length=1, max_length=100)
            ],
            'username': [
                LengthValidator(max_length=50)
            ],
            'phone_number': [
                LengthValidator(max_length=20)
            ],
            'wechat_openid': [
                LengthValidator(max_length=100)
            ],
            'wechat_unionid': [
                LengthValidator(max_length=100)
            ],
            'user_type': [
                RequiredValidator(),
                ChoiceValidator([t.value for t in UserType])
            ],
            'status': [
                RequiredValidator(), 
                ChoiceValidator([s.value for s in UserStatus])
            ]
        })
    
    def is_guest(self) -> bool:
        """判断是否为访客用户"""
        return self.user_type == UserType.GUEST
    
    def is_wechat_user(self) -> bool:
        """判断是否为微信用户"""
        return self.user_type == UserType.WECHAT
    
    def is_phone_user(self) -> bool:
        """判断是否为手机号用户"""
        return self.user_type == UserType.PHONE
    
    def is_active(self) -> bool:
        """判断用户是否活跃"""
        return self.status == UserStatus.ACTIVE
    
    def is_phone_verified(self) -> bool:
        """判断手机号是否已验证"""
        return self.phone_verified_at is not None
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone: 手机号字符串
            
        Returns:
            bool: 格式是否正确
        """
        # 中国大陆手机号正则表达式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @classmethod
    async def find_by_phone(cls, session: DatabaseSession, phone_number: str) -> Optional['User']:
        """
        根据手机号查找用户
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            
        Returns:
            Optional[User]: 找到的用户，未找到返回None
        """
        query = f"SELECT * FROM {cls._table_name} WHERE phone_number = $1"
        row = await session.fetchrow(query, phone_number)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def find_by_wechat_openid(cls, session: DatabaseSession, openid: str) -> Optional['User']:
        """根据微信openid查找用户"""
        query = f"SELECT * FROM {cls._table_name} WHERE wechat_openid = $1"
        row = await session.fetchrow(query, openid)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def create_guest_user(cls, session: DatabaseSession, user_id: str, nickname: str = "访客用户") -> 'User':
        """
        创建访客用户
        
        Args:
            session: 数据库会话
            user_id: 前端生成的用户ID
            nickname: 用户昵称
            
        Returns:
            User: 创建的用户实例
        """
        user = cls(
            id=user_id,  # 使用前端传入的用户ID
            nickname=nickname,
            user_type=UserType.GUEST,
            status=UserStatus.ACTIVE,
            created_at=get_current_timestamp(),
            updated_at=get_current_timestamp()
        )
        return await user.save(session)
    
    @classmethod
    async def create_phone_user(cls, session: DatabaseSession, phone_number: str, nickname: str = None) -> 'User':
        """
        创建手机号用户
        
        Args:
            session: 数据库会话
            phone_number: 手机号
            nickname: 用户昵称，默认为手机号脱敏显示
            
        Returns:
            User: 创建的用户实例
        """
        if not cls.validate_phone_number(phone_number):
            raise ValueError("Invalid phone number format")
        
        # 生成用户ID（使用手机号+时间戳）
        import time
        user_id = f"phone_{phone_number}_{int(time.time())}"
        
        # 如果没有提供昵称，使用脱敏手机号
        if not nickname:
            nickname = f"{phone_number[:3]}****{phone_number[-4:]}"
        
        user = cls(
            id=user_id,
            phone_number=phone_number,
            nickname=nickname,
            user_type=UserType.PHONE,
            status=UserStatus.ACTIVE,
            phone_verified_at=get_current_timestamp(),
            created_at=get_current_timestamp(),
            updated_at=get_current_timestamp()
        )
        return await user.save(session)
    
    def verify_phone(self):
        """验证手机号"""
        self.phone_verified_at = get_current_timestamp()
        self.updated_at = get_current_timestamp()
    
    def to_dict(self, exclude_fields: List[str] = None) -> dict:
        """转换为字典，处理枚举类型"""
        result = super().to_dict(exclude_fields)
        
        # 将枚举转换为字符串值
        if 'user_type' in result and isinstance(self.user_type, UserType):
            result['user_type'] = self.user_type.value
        if 'status' in result and isinstance(self.status, UserStatus):
            result['status'] = self.status.value
            
        return result
    
    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理枚举类型转换
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 处理枚举类型转换
            if isinstance(value, UserType):
                values[field_name] = value.value
            elif isinstance(value, UserStatus):
                values[field_name] = value.value
            else:
                values[field_name] = value
        
        return values
    
    @classmethod
    def from_dict(cls, data: dict) -> 'User':
        """从字典创建用户实例，处理枚举类型"""
        # 处理枚举字段
        if 'user_type' in data and isinstance(data['user_type'], str):
            data['user_type'] = UserType(data['user_type'])
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = UserStatus(data['status'])
            
        return super().from_dict(data) 
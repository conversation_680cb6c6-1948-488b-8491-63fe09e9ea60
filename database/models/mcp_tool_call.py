"""
MCP工具调用数据模型

记录MCP工具的调用信息，包括输入、输出、状态等
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from enum import Enum
from uuid import uuid4
import json

from .base import BaseModel
from ..utils.validators import RequiredValidator, LengthValidator, ChoiceValidator, JSONValidator
from ..connection.session_manager import DatabaseSession

class CallStatus(Enum):
    """调用状态枚举"""
    PENDING = "pending"    # 待处理
    SUCCESS = "success"    # 成功
    FAILED = "failed"      # 失败

@dataclass
class McpToolCall(BaseModel):
    """
    MCP工具调用数据模型
    
    记录每次MCP工具调用的详细信息
    包括调用参数、返回结果、执行状态等
    """
    
    # 表名
    _table_name: str = field(default="mcp_tool_calls", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    # 调用信息
    tool_name: str = ""                              # 调用的工具名称
    user_id: str = ""                                # 调用的用户ID
    input_data: Optional[Dict[str, Any]] = None      # 调用的输入参数
    output_data: Optional[Dict[str, Any]] = None     # 工具返回的输出
    status: CallStatus = CallStatus.PENDING          # 调用状态
    error_message: Optional[str] = None              # 错误信息
    execution_time_ms: Optional[int] = None          # 执行时间（毫秒）
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'tool_name': [
                RequiredValidator(),
                LengthValidator(min_length=1, max_length=100)
            ],
            'user_id': [
                RequiredValidator()
            ],
            'input_data': [
                JSONValidator()
            ],
            'output_data': [
                JSONValidator()
            ],
            'status': [
                RequiredValidator(),
                ChoiceValidator([CallStatus.PENDING, CallStatus.SUCCESS, CallStatus.FAILED])
            ]
        })
    
    def is_success(self) -> bool:
        """判断调用是否成功"""
        return self.status == CallStatus.SUCCESS
    
    def is_failed(self) -> bool:
        """判断调用是否失败"""
        return self.status == CallStatus.FAILED
    
    @classmethod
    async def find_by_user(cls, session: DatabaseSession, user_id: str, limit: int = 50) -> list:
        """根据用户ID查找调用记录"""
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE user_id = $1 
            ORDER BY created_at DESC 
            LIMIT $2
        """
        rows = await session.fetch(query, user_id, limit)
        return [cls.from_dict(dict(row)) for row in rows]
    
    def to_dict(self, exclude_fields: list = None) -> dict:
        """转换为字典，处理枚举类型"""
        result = super().to_dict(exclude_fields)
        
        if 'status' in result and isinstance(self.status, CallStatus):
            result['status'] = self.status.value
            
        return result
    
    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理枚举类型转换和JSON序列化
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        from dataclasses import fields
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 处理枚举类型转换
            if isinstance(value, CallStatus):
                values[field_name] = value.value
            # 处理JSON字段序列化
            elif field_name in ['input_data', 'output_data']:
                if value is None:
                    values[field_name] = None
                elif isinstance(value, dict):
                    # 将字典序列化为JSON字符串
                    values[field_name] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                else:
                    # 如果已经是字符串，直接使用
                    values[field_name] = value
            else:
                values[field_name] = value
        
        return values
    
    @classmethod
    def from_dict(cls, data: dict) -> 'McpToolCall':
        """从字典创建实例，处理枚举类型"""
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = CallStatus(data['status'])
            
        return super().from_dict(data) 
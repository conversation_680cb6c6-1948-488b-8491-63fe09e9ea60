"""
洞察卡片数据模型
"""
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from uuid import uuid4
import json
from .base import BaseModel
from database.connection.session_manager import DatabaseSession

@dataclass
class Insight(BaseModel):
    """
    洞察卡片模型
    """
    _table_name: str = field(default="insights", init=False)
    id: str = field(default_factory=lambda: str(uuid4()))  # 主键 UUID
    user_id: str = ""  # 用户ID
    record_id: Optional[str] = None  # 关联的记录ID
    content: Dict[str, Any] = field(default_factory=dict)  # 洞察卡片内容(JSON)
    status: int = 0  # 状态(0:待确认 1:收藏 2:删除)
    created_at: Optional[Any] = None  # 生成时间

    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理JSON字段转换
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        values = {}
        values['id'] = self.id
        values['user_id'] = self.user_id
        values['record_id'] = self.record_id
        # 将content字典转换为JSON字符串用于JSONB存储
        values['content'] = json.dumps(self.content) if self.content else '{}'
        values['status'] = self.status
        values['created_at'] = self.created_at
        return values

    def to_dict(self, exclude_fields: list = None) -> dict:
        result = super().to_dict(exclude_fields)
        return result 
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Insight':
        """从字典创建Insight实例，处理JSON字段"""
        # 处理content字段（可能是JSON字符串或字典）
        if 'content' in data and isinstance(data['content'], str):
            try:
                data['content'] = json.loads(data['content'])
            except (json.JSONDecodeError, TypeError):
                data['content'] = {}
        elif 'content' not in data:
            data['content'] = {}
            
        return super().from_dict(data)
    
    @classmethod
    async def find_by_id(cls, session: DatabaseSession, insight_id: str) -> Optional['Insight']:
        """
        根据ID查找洞察卡片
        """
        query = f"SELECT * FROM {cls._table_name} WHERE id = $1"
        result = await session.fetchrow(query, insight_id)
        return cls.from_dict(dict(result)) if result else None
    
    @classmethod
    async def find_by_conditions(cls, session: DatabaseSession, conditions: Dict[str, Any]) -> List['Insight']:
        """
        根据条件查找洞察卡片
        """
        # 构建WHERE子句和参数
        where_parts = []
        params = []
        param_index = 1
        
        for key, value in conditions.items():
            where_parts.append(f"{key} = ${param_index}")
            params.append(value)
            param_index += 1
        
        where_clause = " AND ".join(where_parts)
        query = f"SELECT * FROM {cls._table_name} WHERE {where_clause}"
        
        results = await session.fetch(query, *params)
        return [cls.from_dict(dict(result)) for result in results]
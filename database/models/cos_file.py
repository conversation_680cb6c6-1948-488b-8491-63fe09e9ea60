"""
COS文件存储数据模型

用于管理上传到腾讯云COS的文件信息
"""

from dataclasses import dataclass, field, fields
from typing import Optional, Dict, Any, List
from datetime import datetime
from uuid import uuid4
import json

from .base import BaseModel
from ..utils.validators import RequiredValidator
from ..connection.session_manager import DatabaseSession

@dataclass
class CosFile(BaseModel):
    """COS文件存储数据模型"""
    
    _table_name: str = field(default="cos_files", init=False)
    
    # 重写id字段，使用UUID生成
    id: str = field(default_factory=lambda: str(uuid4()))
    
    user_id: str = ""                                # 用户ID，关联users表
    file_key: str = ""                               # COS中的文件键值
    file_info: Optional[Dict[str, Any]] = None       # 文件信息JSON
    bucket: str = ""                                 # COS存储桶名称
    region: str = ""                                 # COS区域
    
    def __post_init__(self):
        """数据初始化后处理"""
        super().__post_init__()
        # 确保file_info是字典类型
        if self.file_info is None:
            self.file_info = {}
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'user_id': [RequiredValidator()],
            'file_key': [RequiredValidator()],
            'bucket': [RequiredValidator()],
            'region': [RequiredValidator()]
        })
    
    def _get_db_values(self) -> dict:
        """获取用于数据库操作的值，处理JSON字段"""
        from dataclasses import fields
        
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 特殊处理file_info字段（JSONB类型）
            if field_name == 'file_info':
                # 确保file_info序列化为JSON字符串以适配PostgreSQL JSONB字段
                if value is None:
                    values[field_name] = json.dumps({})
                elif isinstance(value, dict):
                    values[field_name] = json.dumps(value)
                elif isinstance(value, str):
                    # 如果已经是字符串，验证是否为有效JSON
                    try:
                        # 验证JSON格式
                        json.loads(value)
                        values[field_name] = value
                    except json.JSONDecodeError:
                        # 如果不是有效JSON，包装为JSON字符串
                        values[field_name] = json.dumps({"raw_value": value})
                else:
                    # 其他类型转换为JSON字符串
                    values[field_name] = json.dumps({"value": value})
            else:
                values[field_name] = value
        
        return values
    
    def get_file_url(self, domain: Optional[str] = None) -> str:
        """
        获取文件的访问URL
        
        Args:
            domain: 自定义域名，如果不提供则使用默认COS域名
            
        Returns:
            str: 文件访问URL
        """
        if domain:
            return f"https://{domain}/{self.file_key}"
        else:
            return f"https://{self.bucket}.cos.{self.region}.myqcloud.com/{self.file_key}"
    
    def update_file_info(self, new_info: Dict[str, Any]):
        """
        更新文件信息
        
        Args:
            new_info: 新的文件信息字典
        """
        if self.file_info is None:
            self.file_info = {}
        self.file_info.update(new_info)
    
    def get_file_size(self) -> int:
        """
        获取文件大小（字节）
        
        Returns:
            int: 文件大小，如果未设置则返回0
        """
        if self.file_info and 'size' in self.file_info:
            return int(self.file_info['size'])
        return 0
    
    def get_file_type(self) -> str:
        """
        获取文件类型
        
        Returns:
            str: 文件类型，如果未设置则返回'unknown'
        """
        if self.file_info and 'content_type' in self.file_info:
            return str(self.file_info['content_type'])
        return 'unknown'
    
    @classmethod
    async def find_by_file_key(cls, session: DatabaseSession, file_key: str) -> Optional['CosFile']:
        """
        根据文件键值查找COS文件记录
        
        Args:
            session: 数据库会话
            file_key: 文件键值
            
        Returns:
            Optional[CosFile]: 找到的COS文件记录，未找到返回None
        """
        query = f"SELECT * FROM {cls._table_name} WHERE file_key = $1 ORDER BY created_at DESC LIMIT 1"
        row = await session.fetchrow(query, file_key)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def find_by_user_and_file_key(cls, session: DatabaseSession, user_id: str, file_key: str) -> Optional['CosFile']:
        """
        根据用户ID和文件键值查找COS文件记录
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            file_key: 文件键值
            
        Returns:
            Optional[CosFile]: 找到的COS文件记录，未找到返回None
        """
        query = f"SELECT * FROM {cls._table_name} WHERE user_id = $1 AND file_key = $2 ORDER BY created_at DESC LIMIT 1"
        row = await session.fetchrow(query, user_id, file_key)
        
        if row:
            return cls.from_dict(dict(row))
        return None
    
    @classmethod
    async def find_by_user_id(
        cls, 
        session: DatabaseSession, 
        user_id: str,
        limit: int = 10, 
        offset: int = 0
    ) -> List['CosFile']:
        """
        根据用户ID查找COS文件记录列表
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[CosFile]: 用户的COS文件记录列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE user_id = $1 
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
        """
        rows = await session.fetch(query, user_id, limit, offset)
        
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def find_by_bucket_and_region(
        cls, 
        session: DatabaseSession, 
        bucket: str, 
        region: str,
        limit: int = 10, 
        offset: int = 0
    ) -> List['CosFile']:
        """
        根据存储桶和区域查找COS文件记录
        
        Args:
            session: 数据库会话
            bucket: 存储桶名称
            region: 区域
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[CosFile]: COS文件记录列表
        """
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE bucket = $1 AND region = $2 
            ORDER BY created_at DESC 
            LIMIT $3 OFFSET $4
        """
        rows = await session.fetch(query, bucket, region, limit, offset)
        
        return [cls.from_dict(dict(row)) for row in rows]
    
    @classmethod
    async def create_cos_file(cls, session: DatabaseSession, user_id: str, file_key: str, file_info: dict, bucket: str, region: str) -> 'CosFile':
        """
        创建并保存cos文件到表 cos_files 中
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            file_key: 文件键值
            file_info: 文件信息
            bucket: 存储桶名称
            region: 区域
            
        Returns:
            CosFile: 创建的CosFile实例
        """
        cos_file = CosFile(
            user_id=user_id,
            file_key=file_key, 
            file_info=file_info, 
            bucket=bucket, 
            region=region
        )
        return await cos_file.save(session)
    
    @classmethod
    async def find_with_pagination(
        cls, 
        session: DatabaseSession, 
        user_id: Optional[str] = None,
        bucket: Optional[str] = None,
        region: Optional[str] = None,
        page: int = 1, 
        page_size: int = 10
    ) -> tuple[List['CosFile'], int]:
        """
        分页查询COS文件记录
        
        Args:
            session: 数据库会话
            user_id: 用户ID（可选，用于过滤用户文件）
            bucket: 存储桶名称（可选）
            region: 区域（可选）
            page: 页码，从1开始
            page_size: 每页大小
            
        Returns:
            tuple[List[CosFile], int]: (记录列表, 总数)
        """
        # 构建WHERE条件
        where_conditions = []
        params = []
        param_index = 1
        
        if user_id:
            where_conditions.append(f"user_id = ${param_index}")
            params.append(user_id)
            param_index += 1
        
        if bucket:
            where_conditions.append(f"bucket = ${param_index}")
            params.append(bucket)
            param_index += 1
            
        if region:
            where_conditions.append(f"region = ${param_index}")
            params.append(region)
            param_index += 1
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询总数
        count_query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE {where_clause}"
        total_count = await session.fetchval(count_query, *params)
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询记录
        query = f"""
            SELECT * FROM {cls._table_name} 
            WHERE {where_clause} 
            ORDER BY created_at DESC 
            LIMIT ${param_index} OFFSET ${param_index + 1}
        """
        params.extend([page_size, offset])
        
        rows = await session.fetch(query, *params)
        records = [cls.from_dict(dict(row)) for row in rows]
        
        return records, total_count
    
    @classmethod
    async def count_by_bucket(cls, session: DatabaseSession, bucket: str) -> int:
        """
        统计指定存储桶的文件总数
        
        Args:
            session: 数据库会话
            bucket: 存储桶名称
            
        Returns:
            int: 文件总数
        """
        query = f"SELECT COUNT(*) FROM {cls._table_name} WHERE bucket = $1"
        return await session.fetchval(query, bucket)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CosFile':
        """
        从字典创建CosFile实例，特殊处理JSONB字段
        
        Args:
            data: 数据字典
            
        Returns:
            CosFile: CosFile实例
        """
        # 先调用父类的方法进行基础处理
        filtered_data = {}
        field_names = {f.name for f in fields(cls)}
        
        # 过滤数据，只保留模型字段
        for k, v in data.items():
            if k in field_names:
                filtered_data[k] = v
        
        # 特殊处理file_info JSONB字段
        if 'file_info' in filtered_data:
            file_info_value = filtered_data['file_info']
            
            if isinstance(file_info_value, str):
                # 如果是字符串，尝试解析为JSON
                try:
                    filtered_data['file_info'] = json.loads(file_info_value)
                except json.JSONDecodeError:
                    # 解析失败，设为空字典
                    filtered_data['file_info'] = {}
            elif isinstance(file_info_value, dict):
                # 如果已经是字典，直接使用
                filtered_data['file_info'] = file_info_value
            elif file_info_value is None:
                # 如果是None，设为空字典
                filtered_data['file_info'] = {}
            else:
                # 其他类型，设为空字典
                filtered_data['file_info'] = {}
        
        # 处理日期时间字段
        for field_obj in fields(cls):
            field_name = field_obj.name
            if field_name in filtered_data:
                value = filtered_data[field_name]
                if field_obj.type == datetime and isinstance(value, str):
                    try:
                        filtered_data[field_name] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except ValueError:
                        pass  # 保持原值
        
        return cls(**filtered_data) 
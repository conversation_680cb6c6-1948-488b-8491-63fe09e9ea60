"""
情绪阈值分析数据模型

存储分析结果，记录用户文本输入和情绪阈值结果
"""

import json
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from uuid import uuid4

from .base import BaseModel
from ..utils.validators import RequiredValidator

@dataclass
class EmotionAnalysis(BaseModel):
    """情绪阈值分析数据模型"""
    
    _table_name: str = field(default="emotion_analysis", init=False)
    
    # 重写id字段，使用UUID生成（因为此表需要UUID主键）
    id: str = field(default_factory=lambda: str(uuid4()))
    
    user_id: str = ""                                 # 用户ID
    mcp_tool_call_id: str = ""                       # 关联的工具调用ID
    user_text_input: str = ""                        # 用户输入的文本
    threshold_results: Optional[List[Dict[str, Any]]] = None  # 阈值分析结果
    analysis_version: str = "1.0"                    # 分析版本
    
    def _setup_validators(self):
        """设置字段验证器"""
        super()._setup_validators()
        
        self._validators.update({
            'user_id': [RequiredValidator()],
            'mcp_tool_call_id': [RequiredValidator()],
            'user_text_input': [RequiredValidator()],
            'analysis_version': [RequiredValidator()]
        }) 
    
    def _get_db_values(self) -> dict:
        """
        获取用于数据库操作的值，处理JSON序列化
        
        Returns:
            dict: 适合数据库操作的字段值
        """
        from dataclasses import fields
        values = {}
        for field_obj in fields(self.__class__):
            field_name = field_obj.name
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            # 处理JSON字段序列化
            if field_name == 'threshold_results':
                if value is None:
                    values[field_name] = None
                elif isinstance(value, (list, dict)):
                    # 将列表或字典序列化为JSON字符串
                    values[field_name] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                else:
                    # 如果已经是字符串，直接使用
                    values[field_name] = value
            else:
                values[field_name] = value
        
        return values 
"""
迁移脚本：创建insights表（洞察卡片）
"""

async def up(connection):
    """
    执行迁移：创建insights表
    """
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS insights (
            id UUID PRIMARY KEY,
            user_id VARCHAR(64) NOT NULL,   
            record_id VARCHAR(64),
            content JSONB NOT NULL,
            status INTEGER NOT NULL DEFAULT 0, -- 0:待确认 1:收藏 2:删除
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    # 为用户洞察表创建索引以优化查询性能
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_insights_user_id ON insights(user_id);
        CREATE INDEX IF NOT EXISTS idx_insights_record_id ON insights(record_id);
        CREATE INDEX IF NOT EXISTS idx_insights_status ON insights(status);
        CREATE INDEX IF NOT EXISTS idx_insights_created_at ON insights(created_at);
    """)

async def down(connection):
    """
    执行迁移：删除insights表
    """
    await connection.execute("""
        DROP TABLE IF EXISTS insights;
    """)

"""
创建用户表

支持访客和微信授权登录两种模式的用户表结构
"""

async def up(connection):
    """
    创建用户表的DDL语句
    
    Args:
        connection: 数据库连接对象
    """
    
    # 创建用户类型枚举
    await connection.execute("""
        DO $$ BEGIN
            CREATE TYPE user_type_enum AS ENUM ('guest', 'wechat');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """)
    
    # 创建用户状态枚举
    await connection.execute("""
        DO $$ BEGIN
            CREATE TYPE user_status_enum AS ENUM ('active', 'inactive', 'banned');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """)
    
    # 创建用户表
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(255) PRIMARY KEY,  -- 改为字符串类型，前端生成
            username VARCHAR(50) UNIQUE,
            wechat_openid VARCHAR(100) UNIQUE,
            wechat_unionid VARCHAR(100),
            nickname VARCHAR(100) NOT NULL DEFAULT '匿名用户',
            avatar_url TEXT,
            user_type user_type_enum NOT NULL DEFAULT 'guest',
            status user_status_enum NOT NULL DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_login_at TIMESTAMP WITH TIME ZONE,
            
            -- 约束条件
            CONSTRAINT chk_wechat_user_has_openid 
                CHECK (user_type != 'wechat' OR wechat_openid IS NOT NULL),
            CONSTRAINT chk_guest_user_no_username 
                CHECK (user_type != 'guest' OR username IS NULL)
        );
    """)
    
    # 创建索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
        CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
        CREATE INDEX IF NOT EXISTS idx_users_wechat_openid ON users(wechat_openid);
        CREATE INDEX IF NOT EXISTS idx_users_wechat_unionid ON users(wechat_unionid);
        CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
        CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at);
    """)
    
    # 创建更新时间触发器函数
    await connection.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    # 为用户表添加更新时间触发器
    await connection.execute("""
        DROP TRIGGER IF EXISTS update_users_updated_at ON users;
        CREATE TRIGGER update_users_updated_at
            BEFORE UPDATE ON users
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)

async def down(connection):
    """
    回滚迁移：删除用户表
    
    Args:
        connection: 数据库连接对象
    """
    await connection.execute("DROP TABLE IF EXISTS users CASCADE;")
    await connection.execute("DROP TYPE IF EXISTS user_type_enum CASCADE;")
    await connection.execute("DROP TYPE IF EXISTS user_status_enum CASCADE;")
    await connection.execute("DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;") 
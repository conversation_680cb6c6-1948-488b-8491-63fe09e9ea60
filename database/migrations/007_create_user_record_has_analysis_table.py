"""
创建用户记录与分析的关联表
"""

async def up(connection):
    """
    执行迁移：创建用户记录与分析的关联表
    """
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS user_record_has_analysis (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_record_id UUID NOT NULL REFERENCES user_records(id) ON DELETE CASCADE,
            mcp_tool_call_id UUID NOT NULL REFERENCES mcp_tool_calls(id) ON DELETE CASCADE,
            type VARCHAR(100) NOT NULL DEFAULT 'tool_call',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
async def down(connection):
    """
    执行迁移：删除用户记录与分析的关联表
    """
    await connection.execute("DROP TABLE IF EXISTS user_record_has_analysis;")
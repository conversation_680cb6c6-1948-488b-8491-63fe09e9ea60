"""
数据库迁移管理器

负责管理数据库表结构的迁移和版本控制
支持自动发现迁移文件并按顺序执行
"""

import os
import re
import logging
from typing import List, Optional
from pathlib import Path

from ..connection.connection_pool import ConnectionPool
from ..utils.exceptions import MigrationError

logger = logging.getLogger(__name__)

class MigrationManager:
    """
    数据库迁移管理器
    
    负责发现、执行和管理数据库迁移文件
    维护迁移历史和版本控制
    """
    
    def __init__(self, connection_pool: ConnectionPool):
        """
        初始化迁移管理器
        
        Args:
            connection_pool: 数据库连接池
        """
        self.connection_pool = connection_pool
        self.migrations_dir = Path(__file__).parent
        
    async def initialize_migration_table(self):
        """
        初始化迁移历史表
        
        创建用于记录迁移历史的表
        """
        create_table_sql = """
            CREATE TABLE IF NOT EXISTS migration_history (
                id SERIAL PRIMARY KEY,
                version VARCHAR(50) NOT NULL UNIQUE,
                filename VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                execution_time_ms INTEGER,
                checksum VARCHAR(64)
            );
            CREATE INDEX IF NOT EXISTS idx_migration_history_version ON migration_history(version);
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.execute(create_table_sql)
            logger.info("迁移历史表初始化完成")
        except Exception as e:
            raise MigrationError("migration_history", f"初始化迁移历史表失败: {str(e)}", e)
    
    async def get_executed_migrations(self) -> List[str]:
        """
        获取已执行的迁移版本列表
        
        Returns:
            List[str]: 已执行的迁移版本列表
        """
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(
                    "SELECT version FROM migration_history ORDER BY version"
                )
                return [row['version'] for row in rows]
        except Exception as e:
            logger.warning(f"获取迁移历史失败，可能是首次运行: {str(e)}")
            return []
    
    def discover_migrations(self) -> List[tuple]:
        """
        发现迁移文件
        
        Returns:
            List[tuple]: 迁移文件列表，每个元素为(version, filename, filepath)
        """
        migrations = []
        pattern = re.compile(r'^(\d+)_(.+)\.py$')
        
        for file_path in self.migrations_dir.glob('*.py'):
            if file_path.name.startswith('__'):
                continue
                
            match = pattern.match(file_path.name)
            if match:
                version = match.group(1).zfill(3)  # 补齐为3位数字
                name = match.group(2)
                migrations.append((version, file_path.name, file_path))
        
        # 按版本号排序
        migrations.sort(key=lambda x: x[0])
        return migrations
    
    async def execute_migration(self, migration_file: Path) -> bool:
        """
        执行单个迁移文件
        
        Args:
            migration_file: 迁移文件路径
            
        Returns:
            bool: 执行是否成功
        """
        import importlib.util
        import time
        
        try:
            # 动态导入迁移文件
            spec = importlib.util.spec_from_file_location(
                migration_file.stem, migration_file
            )
            migration_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(migration_module)
            
            # 检查是否有up函数
            if not hasattr(migration_module, 'up'):
                raise MigrationError(
                    migration_file.name, 
                    "迁移文件必须包含 'up' 函数"
                )
            
            # 执行迁移
            start_time = time.time()
            
            async with self.connection_pool.acquire() as conn:
                async with conn.transaction():
                    await migration_module.up(conn)
                    
                    # 记录迁移历史
                    execution_time = int((time.time() - start_time) * 1000)
                    version = re.match(r'^(\d+)_', migration_file.name).group(1).zfill(3)
                    
                    await conn.execute("""
                        INSERT INTO migration_history 
                        (version, filename, execution_time_ms) 
                        VALUES ($1, $2, $3)
                    """, version, migration_file.name, execution_time)
            
            logger.info(f"迁移 {migration_file.name} 执行成功")
            return True
            
        except Exception as e:
            logger.error(f"执行迁移 {migration_file.name} 失败: {str(e)}")
            raise MigrationError(migration_file.name, str(e), e)
    
    async def migrate(self, target_version: Optional[str] = None) -> int:
        """
        执行数据库迁移
        
        Args:
            target_version: 目标版本号，None表示迁移到最新版本
            
        Returns:
            int: 执行的迁移数量
        """
        try:
            # 初始化迁移历史表
            await self.initialize_migration_table()
            
            # 获取已执行的迁移
            executed_migrations = await self.get_executed_migrations()
            
            # 发现可用的迁移文件
            available_migrations = self.discover_migrations()
            
            if not available_migrations:
                logger.info("没有发现迁移文件")
                return 0
            
            # 筛选需要执行的迁移
            pending_migrations = []
            for version, filename, filepath in available_migrations:
                if version not in executed_migrations:
                    if target_version is None or version <= target_version:
                        pending_migrations.append((version, filename, filepath))
            
            if not pending_migrations:
                logger.info("没有需要执行的迁移")
                return 0
            
            # 执行迁移
            executed_count = 0
            for version, filename, filepath in pending_migrations:
                logger.info(f"正在执行迁移: {filename}")
                await self.execute_migration(filepath)
                executed_count += 1
            
            logger.info(f"成功执行了 {executed_count} 个迁移")
            return executed_count
            
        except Exception as e:
            logger.error(f"迁移执行失败: {str(e)}")
            raise MigrationError("migration", str(e), e)
    
    async def get_migration_status(self) -> dict:
        """
        获取迁移状态信息
        
        Returns:
            dict: 迁移状态信息
        """
        try:
            executed_migrations = await self.get_executed_migrations()
            available_migrations = self.discover_migrations()
            
            pending_migrations = []
            for version, filename, filepath in available_migrations:
                if version not in executed_migrations:
                    pending_migrations.append(filename)
            
            return {
                'executed_count': len(executed_migrations),
                'available_count': len(available_migrations),
                'pending_count': len(pending_migrations),
                'executed_migrations': executed_migrations,
                'pending_migrations': pending_migrations
            }
            
        except Exception as e:
            logger.error(f"获取迁移状态失败: {str(e)}")
            return {
                'error': str(e)
            } 
    
    async def reset_database(self):
        """
        重置数据库
        
        删除所有表并重新创建
        这个操作会：
        1. 删除数据库中的所有表
        2. 重新执行所有迁移文件来重建表结构
        
        警告：这个操作会丢失所有数据！
        """
        try:
            logger.warning("开始重置数据库 - 这将删除所有数据！")
            
            async with self.connection_pool.acquire() as conn:
                # 1. 查询所有表名（排除系统表）
                tables_query = """
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                """
                
                rows = await conn.fetch(tables_query)
                table_names = [row['tablename'] for row in rows]
                
                if table_names:
                    logger.info(f"发现 {len(table_names)} 个表需要删除: {', '.join(table_names)}")
                    
                    # 2. 删除所有表（使用CASCADE确保删除依赖关系）
                    for table_name in table_names:
                        drop_sql = f'DROP TABLE IF EXISTS "{table_name}" CASCADE'
                        await conn.execute(drop_sql)
                        logger.debug(f"已删除表: {table_name}")
                    
                    logger.info("所有表删除完成")
                else:
                    logger.info("数据库中没有发现用户表")
            
            # 3. 重新初始化迁移历史表
            await self.initialize_migration_table()
            
            # 4. 发现所有迁移文件
            available_migrations = self.discover_migrations()
            
            if not available_migrations:
                logger.warning("没有发现迁移文件，数据库将保持空状态")
                return
            
            # 5. 重新执行所有迁移
            logger.info(f"开始重新执行 {len(available_migrations)} 个迁移文件")
            
            executed_count = 0
            for version, filename, filepath in available_migrations:
                logger.info(f"正在执行迁移: {filename}")
                await self.execute_migration(filepath)
                executed_count += 1
            
            logger.info(f"数据库重置完成！成功执行了 {executed_count} 个迁移")
            
        except Exception as e:
            logger.error(f"数据库重置失败: {str(e)}")
            raise MigrationError("reset_database", f"重置数据库失败: {str(e)}", e)
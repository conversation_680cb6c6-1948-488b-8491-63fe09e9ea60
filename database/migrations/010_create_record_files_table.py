"""
创建record_files关联表
用于存储用户记录与COS文件的一对多关联关系
"""

async def up(connection):
    """
    执行迁移：创建record_files关联表
    """
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS record_files (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            record_id UUID NOT NULL,
            file_id UUID NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            -- 添加外键约束
            CONSTRAINT fk_record_files_record_id 
                FOREIGN KEY (record_id) REFERENCES user_records(id) 
                ON DELETE CASCADE ON UPDATE CASCADE,
            
            CONSTRAINT fk_record_files_file_id 
                FOREIGN KEY (file_id) REFERENCES cos_files(id) 
                ON DELETE CASCADE ON UPDATE CASCADE,
            
            -- 确保每个记录和文件组合唯一
            CONSTRAINT uk_record_file UNIQUE (record_id, file_id)
        );
        
        -- 创建索引提高查询性能
        CREATE INDEX IF NOT EXISTS idx_record_files_record_id ON record_files(record_id);
        CREATE INDEX IF NOT EXISTS idx_record_files_file_id ON record_files(file_id);
        CREATE INDEX IF NOT EXISTS idx_record_files_created_at ON record_files(created_at);
    """)

async def down(connection):
    """
    执行迁移：删除record_files关联表
    """
    await connection.execute("""
        DROP TABLE IF EXISTS record_files;
    """)
"""
添加成员表
类型包括 孩子 或者父母的角色（孩子 爸爸 妈妈）
信息存储为json格式
需要与用户表users关联
"""

async def up(connection):
    """
    执行迁移：添加成员表
    """
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS members (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            type VARCHAR(20) NOT NULL,
            info JSONB NOT NULL DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    # 创建索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_members_user_id ON members(user_id);
    """)
    
    # 创建类型索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_members_type ON members(type);
    """)
    
    # 创建时间索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_members_created_at ON members(created_at);
    """)
    
    # 添加更新时间触发器
    await connection.execute("""
        DROP TRIGGER IF EXISTS update_members_updated_at ON members;
        CREATE TRIGGER update_members_updated_at
            BEFORE UPDATE ON members
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)

async def down(connection):
    """
    回滚迁移：删除成员表
    """
    await connection.execute("""
        DROP TRIGGER IF EXISTS update_members_updated_at ON members;
    """)
    
    await connection.execute("""
        DROP TABLE IF EXISTS members CASCADE;
    """)
"""
用户记录表

记录用户的输入
"""

async def up(connection):
    """
    创建用户记录表的DDL语句
    
    Args:
        connection: 数据库连接对象
    """
    # 创建用户记录表
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS user_records (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            content TEXT NOT NULL,
            content_type VARCHAR(50) NOT NULL DEFAULT 'text',
            file_id UUID REFERENCES cos_files(id) ON DELETE SET NULL,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)

    # 创建更新时间触发器函数（如果不存在）
    await connection.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    # 为用户记录表添加更新时间触发器
    await connection.execute("""
        DROP TRIGGER IF EXISTS update_user_records_updated_at ON user_records;
        CREATE TRIGGER update_user_records_updated_at
            BEFORE UPDATE ON user_records
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)

    # 为用户记录表创建索引以优化查询性能
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_user_records_user_id ON user_records(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_records_content_type ON user_records(content_type);
        CREATE INDEX IF NOT EXISTS idx_user_records_file_id ON user_records(file_id);
        CREATE INDEX IF NOT EXISTS idx_user_records_created_at ON user_records(created_at);
        CREATE INDEX IF NOT EXISTS idx_user_records_is_deleted ON user_records(is_deleted);
    """)

async def down(connection):
    """
    删除用户记录表的DDL语句
    """
    await connection.execute("DROP TABLE IF EXISTS user_records CASCADE;")
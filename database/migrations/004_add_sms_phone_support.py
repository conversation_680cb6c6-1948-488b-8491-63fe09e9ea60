"""
添加短信验证码和手机号登录支持

迁移说明:
1. 更新user_type枚举，添加phone类型支持  
2. 在users表中添加phone_number和phone_verified_at字段
3. 创建sms_verifications表用于存储短信验证码
4. 添加相关索引和约束
5. 创建触发器和存储过程
"""

async def up(connection):
    """
    执行迁移：添加短信验证码和手机号登录支持
    
    Args:
        connection: 数据库连接对象
    """
    
    # 1. 更新用户类型枚举，添加phone类型（在独立事务中）
    try:
        await connection.execute("""
            DO $$ 
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'phone' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_type_enum')) THEN
                    ALTER TYPE user_type_enum ADD VALUE 'phone';
                END IF;
            END $$;
        """)
        # 强制提交枚举值添加
        await connection.execute("COMMIT;")
        await connection.execute("BEGIN;")
    except Exception:
        # 如果枚举值已存在，继续执行
        pass
    
    # 2. 为users表添加手机号相关字段
    await connection.execute("""
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
        ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP WITH TIME ZONE;
    """)
    
    # 3. 为phone_number字段添加唯一约束（安全方式）
    await connection.execute("""
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'unique_users_phone_number') THEN
                ALTER TABLE users ADD CONSTRAINT unique_users_phone_number UNIQUE (phone_number);
            END IF;
        END $$;
    """)
    
    # 4. 为phone_number字段添加索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_users_phone_number 
        ON users(phone_number) 
        WHERE phone_number IS NOT NULL;
    """)
    
    # 5. 创建短信验证码表
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS sms_verifications (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            phone_number VARCHAR(11) NOT NULL,
            verification_code VARCHAR(6) NOT NULL,
            purpose VARCHAR(50) NOT NULL DEFAULT 'login',
            is_used BOOLEAN NOT NULL DEFAULT false,
            is_expired BOOLEAN NOT NULL DEFAULT false,
            attempts INTEGER NOT NULL DEFAULT 0,
            max_attempts INTEGER NOT NULL DEFAULT 3,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            used_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    # 6. 为sms_verifications表添加索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_sms_verifications_phone_number 
        ON sms_verifications(phone_number);
    """)
    
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_sms_verifications_phone_code 
        ON sms_verifications(phone_number, verification_code, purpose);
    """)
    
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_sms_verifications_expires_at 
        ON sms_verifications(expires_at);
    """)
    
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_sms_verifications_created_at 
        ON sms_verifications(created_at);
    """)
    
    # 7. 为sms_verifications表添加更新时间触发器
    await connection.execute("""
        DROP TRIGGER IF EXISTS trigger_update_sms_verifications_updated_at 
        ON sms_verifications;
    """)
    
    await connection.execute("""
        CREATE TRIGGER trigger_update_sms_verifications_updated_at
            BEFORE UPDATE ON sms_verifications
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)
    
    # 8. 创建清理过期验证码的存储过程
    await connection.execute("""
        CREATE OR REPLACE FUNCTION cleanup_expired_sms_verifications(days_old INTEGER DEFAULT 7)
        RETURNS INTEGER AS $$
        DECLARE
            deleted_count INTEGER;
        BEGIN
            DELETE FROM sms_verifications 
            WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_old;
            
            GET DIAGNOSTICS deleted_count = ROW_COUNT;
            RETURN deleted_count;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # 9. 创建验证码有效性检查函数
    await connection.execute("""
        CREATE OR REPLACE FUNCTION is_verification_code_valid(
            p_phone_number VARCHAR(11),
            p_verification_code VARCHAR(6),
            p_purpose VARCHAR(50) DEFAULT 'login'
        )
        RETURNS BOOLEAN AS $$
        DECLARE
            verification_record sms_verifications%ROWTYPE;
        BEGIN
            SELECT * INTO verification_record
            FROM sms_verifications 
            WHERE phone_number = p_phone_number 
                AND verification_code = p_verification_code 
                AND purpose = p_purpose 
                AND is_used = false 
                AND is_expired = false 
                AND expires_at > CURRENT_TIMESTAMP
                AND attempts < max_attempts
            ORDER BY created_at DESC 
            LIMIT 1;
            
            RETURN FOUND;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # 10. 更新users表的约束，支持phone类型用户
    await connection.execute("""
        ALTER TABLE users 
        DROP CONSTRAINT IF EXISTS chk_wechat_user_has_openid;
    """)
    
    await connection.execute("""
        ALTER TABLE users 
        ADD CONSTRAINT chk_wechat_user_has_openid 
        CHECK (user_type != 'wechat' OR wechat_openid IS NOT NULL);
    """)
    
    # 11. 在新的子事务中添加phone用户约束（确保枚举值已提交）
    await connection.execute("""
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_phone_user_has_phone') THEN
                -- 使用字符串比较而不是枚举值比较，避免事务问题
                ALTER TABLE users ADD CONSTRAINT chk_phone_user_has_phone 
                CHECK (user_type::text != 'phone' OR phone_number IS NOT NULL);
            END IF;
        END $$;
    """)

async def down(connection):
    """
    回滚迁移：移除短信验证码和手机号登录支持
    
    Args:
        connection: 数据库连接对象
    """
    
    # 1. 删除sms_verifications表
    await connection.execute("DROP TABLE IF EXISTS sms_verifications CASCADE;")
    
    # 2. 删除相关存储过程和函数
    await connection.execute("DROP FUNCTION IF EXISTS cleanup_expired_sms_verifications(INTEGER) CASCADE;")
    await connection.execute("DROP FUNCTION IF EXISTS is_verification_code_valid(VARCHAR, VARCHAR, VARCHAR) CASCADE;")
    
    # 3. 删除users表的手机号相关约束和字段
    await connection.execute("ALTER TABLE users DROP CONSTRAINT IF EXISTS unique_users_phone_number;")
    await connection.execute("ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_phone_user_has_phone;")
    await connection.execute("DROP INDEX IF EXISTS idx_users_phone_number;")
    await connection.execute("ALTER TABLE users DROP COLUMN IF EXISTS phone_number;")
    await connection.execute("ALTER TABLE users DROP COLUMN IF EXISTS phone_verified_at;")
    
    # 注意：PostgreSQL不支持直接删除枚举值，所以不回滚user_type_enum
    # 如果需要完全清理，需要重新创建枚举类型 
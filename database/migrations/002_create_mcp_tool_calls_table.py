"""
创建MCP工具调用表

记录MCP工具的调用信息，包括输入、输出、状态等
"""

async def up(connection):
    """
    创建MCP工具调用表的DDL语句
    
    Args:
        connection: 数据库连接对象
    """
    
    # 创建调用状态枚举
    await connection.execute("""
        DO $$ BEGIN
            CREATE TYPE call_status_enum AS ENUM ('pending', 'success', 'failed');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """)
    
    # 创建MCP工具调用表
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS mcp_tool_calls (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tool_name VARCHAR(100) NOT NULL,
            user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            input_data JSONB,
            output_data JSONB,
            status call_status_enum NOT NULL DEFAULT 'pending',
            error_message TEXT,
            execution_time_ms INTEGER,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    # 创建索引
    await connection.execute("""
        CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_name ON mcp_tool_calls(tool_name);
        CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_user_id ON mcp_tool_calls(user_id);
        CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);
        CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at);
        CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_user_id_created_at ON mcp_tool_calls(user_id, created_at DESC);
    """)
    
    # 为表添加更新时间触发器
    await connection.execute("""
        DROP TRIGGER IF EXISTS update_mcp_tool_calls_updated_at ON mcp_tool_calls;
        CREATE TRIGGER update_mcp_tool_calls_updated_at
            BEFORE UPDATE ON mcp_tool_calls
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)

async def down(connection):
    """
    回滚迁移：删除MCP工具调用表
    
    Args:
        connection: 数据库连接对象
    """
    await connection.execute("DROP TABLE IF EXISTS mcp_tool_calls CASCADE;")
    await connection.execute("DROP TYPE IF EXISTS call_status_enum CASCADE;") 
"""
添加cos_files表
用于存储cos文件信息
"""

async def up(connection):
    """
    执行迁移：添加cos_files表
    """
    await connection.execute("""
        CREATE TABLE IF NOT EXISTS cos_files (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            file_key VARCHAR(255) NOT NULL,
            file_info JSONB NOT NULL DEFAULT '{}',
            bucket VARCHAR(255) NOT NULL,
            region VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            -- 添加外键约束与users表关联
            CONSTRAINT fk_cos_files_user_id 
                FOREIGN KEY (user_id) REFERENCES users(id) 
                ON DELETE CASCADE ON UPDATE CASCADE,
            
            -- 添加索引提高查询性能
            CONSTRAINT idx_cos_files_user_id_file_key 
                UNIQUE (user_id, file_key)
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_cos_files_user_id ON cos_files(user_id);
        CREATE INDEX IF NOT EXISTS idx_cos_files_file_key ON cos_files(file_key);
        CREATE INDEX IF NOT EXISTS idx_cos_files_bucket_region ON cos_files(bucket, region);
    """)

async def down(connection):
    """
    执行迁移：删除cos_files表
    """
    await connection.execute("""
        DROP TABLE IF EXISTS cos_files;
    """)
    
"""
移除user_records表中的file_id字段
因为在record_files表中建立了新的关联关系
"""

async def up(connection):
    """
    执行迁移：移除user_records表中的file_id字段
    """
    # 首先检查file_id列是否存在
    column_exists = await connection.fetchval("""
        SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'user_records' 
            AND column_name = 'file_id'
        )
    """)
    
    if column_exists:
        # 删除外键约束（如果存在）
        await connection.execute("""
            ALTER TABLE user_records 
            DROP CONSTRAINT IF EXISTS fk_user_records_file_id
        """)
        
        # 删除file_id列
        await connection.execute("""
            ALTER TABLE user_records 
            DROP COLUMN IF EXISTS file_id
        """)

async def down(connection):
    """
    回滚迁移：重新添加file_id字段到user_records表
    """
    # 检查file_id列是否不存在
    column_exists = await connection.fetchval("""
        SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'user_records' 
            AND column_name = 'file_id'
        )
    """)
    
    if not column_exists:
        # 重新添加file_id列
        await connection.execute("""
            ALTER TABLE user_records 
            ADD COLUMN file_id UUID
        """)
        
        # 重新添加外键约束
        await connection.execute("""
            ALTER TABLE user_records 
            ADD CONSTRAINT fk_user_records_file_id 
                FOREIGN KEY (file_id) REFERENCES cos_files(id) 
                ON DELETE SET NULL ON UPDATE CASCADE
        """)
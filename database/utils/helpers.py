"""
辅助函数模块

提供数据库操作中常用的工具函数
包括UUID生成、时间格式化、JSON处理等
"""

import uuid
import json
import datetime
from typing import Any, Optional, Dict, Union
from decimal import Decimal

def generate_uuid() -> str:
    """
    生成UUID字符串
    
    Returns:
        str: UUID4字符串
    """
    return str(uuid.uuid4())


def format_datetime(dt: datetime.datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象
        format_str: 格式化字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    if dt is None:
        return None
    return dt.strftime(format_str)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全的JSON解析
    
    Args:
        json_str: JSON字符串
        default: 解析失败时的默认值
        
    Returns:
        Any: 解析后的Python对象，失败时返回默认值
    """
    if json_str is None:
        return default
    
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "null") -> str:
    """
    安全的JSON序列化
    
    Args:
        obj: 要序列化的对象
        default: 序列化失败时的默认值
        
    Returns:
        str: JSON字符串，失败时返回默认值
    """
    if obj is None:
        return "null"
    
    try:
        return json.dumps(obj, ensure_ascii=False, cls=ExtendedJSONEncoder)
    except (TypeError, ValueError):
        return default


class ExtendedJSONEncoder(json.JSONEncoder):
    """
    扩展的JSON编码器
    
    支持更多Python类型的序列化
    """
    
    def default(self, obj):
        """
        自定义序列化方法
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            Any: 可序列化的对象
        """
        if isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif isinstance(obj, datetime.date):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)


def merge_dicts(*dicts: Dict) -> Dict:
    """
    合并多个字典
    
    Args:
        *dicts: 要合并的字典
        
    Returns:
        Dict: 合并后的字典
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def clean_none_values(data: Dict) -> Dict:
    """
    清理字典中的None值
    
    Args:
        data: 输入字典
        
    Returns:
        Dict: 清理后的字典
    """
    if not isinstance(data, dict):
        return data
    
    return {k: v for k, v in data.items() if v is not None}


def snake_to_camel(snake_str: str) -> str:
    """
    将蛇形命名转换为驼峰命名
    
    Args:
        snake_str: 蛇形字符串
        
    Returns:
        str: 驼峰字符串
    """
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


def camel_to_snake(camel_str: str) -> str:
    """
    将驼峰命名转换为蛇形命名
    
    Args:
        camel_str: 驼峰字符串
        
    Returns:
        str: 蛇形字符串
    """
    import re
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def paginate_query_params(page: int = 1, per_page: int = 20, max_per_page: int = 100) -> Dict[str, int]:
    """
    处理分页查询参数
    
    Args:
        page: 页码
        per_page: 每页条数
        max_per_page: 每页最大条数
        
    Returns:
        Dict: 包含offset和limit的字典
    """
    # 确保参数在合理范围内
    page = max(1, page)
    per_page = min(max(1, per_page), max_per_page)
    
    offset = (page - 1) * per_page
    limit = per_page
    
    return {
        'offset': offset,
        'limit': limit,
        'page': page,
        'per_page': per_page
    }


def build_where_clause(conditions: Dict[str, Any], table_alias: str = None) -> tuple:
    """
    构建WHERE子句
    
    Args:
        conditions: 查询条件字典
        table_alias: 表别名
        
    Returns:
        tuple: (where_clause, parameters)
    """
    if not conditions:
        return "", []
    
    clauses = []
    parameters = []
    
    prefix = f"{table_alias}." if table_alias else ""
    
    for field, value in conditions.items():
        if value is None:
            clauses.append(f"{prefix}{field} IS NULL")
        elif isinstance(value, (list, tuple)):
            # IN查询
            placeholders = ','.join(['$' + str(len(parameters) + i + 1) for i in range(len(value))])
            clauses.append(f"{prefix}{field} IN ({placeholders})")
            parameters.extend(value)
        else:
            clauses.append(f"{prefix}{field} = ${len(parameters) + 1}")
            parameters.append(value)
    
    where_clause = " AND ".join(clauses)
    return where_clause, parameters


def escape_sql_identifier(identifier: str) -> str:
    """
    转义SQL标识符
    
    Args:
        identifier: SQL标识符（表名、字段名等）
        
    Returns:
        str: 转义后的标识符
    """
    # 简单的标识符转义，用双引号包围
    return f'"{identifier}"'


def get_current_timestamp() -> datetime.datetime:
    """
    获取当前时间戳
    
    Returns:
        datetime.datetime: 当前上海时间（带时区信息）
    """
    import zoneinfo
    shanghai_tz = zoneinfo.ZoneInfo("Asia/Shanghai")
    return datetime.datetime.now(shanghai_tz)


def get_current_time_iso() -> str:
    """
    获取当前时间的ISO格式字符串（上海时区）
    
    Returns:
        str: ISO格式的时间字符串
    """
    return get_current_timestamp().isoformat()


def get_shanghai_timezone():
    """
    获取上海时区对象
    
    Returns:
        zoneinfo.ZoneInfo: 上海时区对象
    """
    import zoneinfo
    return zoneinfo.ZoneInfo("Asia/Shanghai")


def ensure_list(value: Union[Any, list]) -> list:
    """
    确保值是列表类型
    
    Args:
        value: 任意值
        
    Returns:
        list: 如果value是列表则返回原值，否则返回包含value的列表
    """
    if isinstance(value, list):
        return value
    return [value] if value is not None else []


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    import re
    # 移除非法字符，保留字母、数字、点、下划线、连字符
    clean_name = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
    # 移除多个连续的下划线
    clean_name = re.sub(r'_+', '_', clean_name)
    # 移除开头和结尾的下划线
    clean_name = clean_name.strip('_')
    
    return clean_name or 'unnamed_file' 
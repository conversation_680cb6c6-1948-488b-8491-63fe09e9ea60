"""
数据验证器

提供各种数据格式的验证功能
包括邮箱、UUID、JSON等常见数据类型的验证
"""

import re
import json
import uuid
from typing import Any, Optional
from .exceptions import ValidationError

class BaseValidator:
    """
    基础验证器类
    
    所有验证器的基类，提供通用的验证接口
    """
    
    def validate(self, value: Any, field_name: str = "unknown") -> bool:
        """
        验证数据
        
        Args:
            value: 要验证的值
            field_name: 字段名称，用于错误信息
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        raise NotImplementedError("子类必须实现validate方法")


class EmailValidator(BaseValidator):
    """
    邮箱地址验证器
    
    验证邮箱地址格式是否正确
    """
    
    # 邮箱正则表达式
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    def validate(self, value: Any, field_name: str = "email") -> bool:
        """
        验证邮箱地址格式
        
        Args:
            value: 邮箱地址
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: 邮箱格式无效时抛出
        """
        if value is None:
            return True  # 允许空值，由Required验证器处理
        
        if not isinstance(value, str):
            raise ValidationError(field_name, "邮箱地址必须是字符串类型", value)
        
        if len(value) > 254:  # RFC 5321 限制
            raise ValidationError(field_name, "邮箱地址长度不能超过254个字符", value)
        
        if not self.EMAIL_PATTERN.match(value):
            raise ValidationError(field_name, "邮箱地址格式无效", value)
        
        return True


class UUIDValidator(BaseValidator):
    """
    UUID验证器
    
    验证UUID格式是否正确
    """
    
    def validate(self, value: Any, field_name: str = "uuid") -> bool:
        """
        验证UUID格式
        
        Args:
            value: UUID值
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: UUID格式无效时抛出
        """
        if value is None:
            return True  # 允许空值
        
        if isinstance(value, uuid.UUID):
            return True
        
        if not isinstance(value, str):
            raise ValidationError(field_name, "UUID必须是字符串或UUID对象", value)
        
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            raise ValidationError(field_name, "UUID格式无效", value)


class JSONValidator(BaseValidator):
    """
    JSON数据验证器
    
    验证JSON数据格式是否正确
    """
    
    def validate(self, value: Any, field_name: str = "json_data") -> bool:
        """
        验证JSON数据格式
        
        Args:
            value: JSON数据
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: JSON格式无效时抛出
        """
        if value is None:
            return True  # 允许空值
        
        # 如果已经是Python对象，尝试序列化验证
        if not isinstance(value, str):
            try:
                json.dumps(value)
                return True
            except (TypeError, ValueError) as e:
                raise ValidationError(field_name, f"无法序列化为JSON: {str(e)}", value)
        
        # 如果是字符串，尝试解析
        try:
            json.loads(value)
            return True
        except json.JSONDecodeError as e:
            raise ValidationError(field_name, f"JSON格式无效: {str(e)}", value)


class LengthValidator(BaseValidator):
    """
    长度验证器
    
    验证字符串或列表的长度是否在指定范围内
    """
    
    def __init__(self, min_length: int = 0, max_length: Optional[int] = None):
        """
        初始化长度验证器
        
        Args:
            min_length: 最小长度
            max_length: 最大长度，None表示无限制
        """
        self.min_length = min_length
        self.max_length = max_length
    
    def validate(self, value: Any, field_name: str = "field") -> bool:
        """
        验证长度
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: 长度不符合要求时抛出
        """
        if value is None:
            return True  # 允许空值
        
        try:
            length = len(value)
        except TypeError:
            raise ValidationError(field_name, "值不支持长度检查", value)
        
        if length < self.min_length:
            raise ValidationError(
                field_name, 
                f"长度不能少于{self.min_length}个字符", 
                value
            )
        
        if self.max_length is not None and length > self.max_length:
            raise ValidationError(
                field_name, 
                f"长度不能超过{self.max_length}个字符", 
                value
            )
        
        return True


class RequiredValidator(BaseValidator):
    """
    必填验证器
    
    验证值是否为空
    """
    
    def validate(self, value: Any, field_name: str = "field") -> bool:
        """
        验证是否为必填
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: 值为空时抛出
        """
        if value is None:
            raise ValidationError(field_name, "该字段为必填项", value)
        
        if isinstance(value, str) and not value.strip():
            raise ValidationError(field_name, "该字段不能为空字符串", value)
        
        if isinstance(value, (list, dict, tuple)) and len(value) == 0:
            raise ValidationError(field_name, "该字段不能为空", value)
        
        return True


class ChoiceValidator(BaseValidator):
    """
    选择验证器
    
    验证值是否在指定的选择范围内
    支持字符串值和枚举类型
    """
    
    def __init__(self, choices: list):
        """
        初始化选择验证器
        
        Args:
            choices: 允许的选择列表（可以是字符串或枚举值）
        """
        self.choices = choices
    
    def validate(self, value: Any, field_name: str = "field") -> bool:
        """
        验证是否在选择范围内
        
        Args:
            value: 要验证的值（可以是字符串或枚举对象）
            field_name: 字段名称
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValidationError: 值不在选择范围内时抛出
        """
        if value is None:
            return True  # 允许空值
        
        # 检查直接匹配
        if value in self.choices:
            return True
        
        # 如果value是枚举，检查其值
        if hasattr(value, 'value') and value.value in self.choices:
            return True
        
        # 如果value是字符串，检查是否有匹配的枚举值
        if isinstance(value, str):
            for choice in self.choices:
                if hasattr(choice, 'value') and choice.value == value:
                    return True
        
        # 生成错误消息时，优先显示枚举的值而不是枚举对象
        choice_display = []
        for choice in self.choices:
            if hasattr(choice, 'value'):
                choice_display.append(choice.value)
            else:
                choice_display.append(str(choice))
        
        raise ValidationError(
            field_name, 
            f"值必须是以下选项之一: {', '.join(choice_display)}", 
            value
        )
        
        return True


class CompositeValidator:
    """
    复合验证器
    
    可以组合多个验证器进行验证
    """
    
    def __init__(self, validators: list[BaseValidator]):
        """
        初始化复合验证器
        
        Args:
            validators: 验证器列表
        """
        self.validators = validators
    
    def validate(self, value: Any, field_name: str = "field") -> bool:
        """
        使用所有验证器进行验证
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            bool: 所有验证器都通过返回True
            
        Raises:
            ValidationError: 任一验证器失败时抛出
        """
        for validator in self.validators:
            validator.validate(value, field_name)
        
        return True 
"""
自定义异常类

定义数据库操作中可能遇到的各种异常类型
提供更精确的错误信息和处理方式
"""

class DatabaseError(Exception):
    """
    数据库操作基础异常类
    
    所有数据库相关异常的基类
    """
    
    def __init__(self, message: str, original_error: Exception = None):
        """
        初始化数据库异常
        
        Args:
            message: 错误信息
            original_error: 原始异常对象
        """
        super().__init__(message)
        self.message = message
        self.original_error = original_error
    
    def __str__(self) -> str:
        """
        返回异常的字符串表示
        
        Returns:
            str: 异常信息
        """
        if self.original_error:
            return f"{self.message}. 原始错误: {str(self.original_error)}"
        return self.message


class ConnectionError(DatabaseError):
    """
    数据库连接异常
    
    当数据库连接失败或连接池问题时抛出
    """
    
    def __init__(self, message: str = "数据库连接失败", original_error: Exception = None):
        super().__init__(message, original_error)


class ValidationError(DatabaseError):
    """
    数据验证异常
    
    当输入数据不符合要求时抛出
    """
    
    def __init__(self, field: str, message: str, value=None):
        """
        初始化验证异常
        
        Args:
            field: 验证失败的字段名
            message: 错误信息
            value: 验证失败的值
        """
        self.field = field
        self.value = value
        full_message = f"字段 '{field}' 验证失败: {message}"
        if value is not None:
            full_message += f" (值: {value})"
        super().__init__(full_message)


class TransactionError(DatabaseError):
    """
    事务处理异常
    
    当事务提交或回滚失败时抛出
    """
    
    def __init__(self, message: str = "事务处理失败", original_error: Exception = None):
        super().__init__(message, original_error)


class ModelError(DatabaseError):
    """
    模型操作异常
    
    当模型的CRUD操作失败时抛出
    """
    
    def __init__(self, model_name: str, operation: str, message: str, original_error: Exception = None):
        """
        初始化模型异常
        
        Args:
            model_name: 模型名称
            operation: 操作类型 (create, read, update, delete)
            message: 错误信息
            original_error: 原始异常对象
        """
        self.model_name = model_name
        self.operation = operation
        full_message = f"模型 '{model_name}' 的 '{operation}' 操作失败: {message}"
        super().__init__(full_message, original_error)


class MigrationError(DatabaseError):
    """
    数据库迁移异常
    
    当数据库迁移操作失败时抛出
    """
    
    def __init__(self, migration_name: str, message: str, original_error: Exception = None):
        """
        初始化迁移异常
        
        Args:
            migration_name: 迁移文件名
            message: 错误信息
            original_error: 原始异常对象
        """
        self.migration_name = migration_name
        full_message = f"迁移 '{migration_name}' 执行失败: {message}"
        super().__init__(full_message, original_error)


class DuplicateError(DatabaseError):
    """
    重复数据异常
    
    当插入重复数据时抛出
    """
    
    def __init__(self, field: str, value, message: str = None):
        """
        初始化重复数据异常
        
        Args:
            field: 重复的字段名
            value: 重复的值
            message: 自定义错误信息
        """
        self.field = field
        self.value = value
        if message is None:
            message = f"字段 '{field}' 的值 '{value}' 已存在"
        super().__init__(message)


class NotFoundError(DatabaseError):
    """
    数据未找到异常
    
    当查询的数据不存在时抛出
    """
    
    def __init__(self, model_name: str, identifier=None, message: str = None):
        """
        初始化数据未找到异常
        
        Args:
            model_name: 模型名称
            identifier: 查询标识符
            message: 自定义错误信息
        """
        self.model_name = model_name
        self.identifier = identifier
        if message is None:
            if identifier is not None:
                message = f"未找到 {model_name} 记录 (标识符: {identifier})"
            else:
                message = f"未找到 {model_name} 记录"
        super().__init__(message) 
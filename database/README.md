# 数据库管理模块

这是一个完整的数据库管理模块，专为关系分析系统设计，支持PostgreSQL数据库的连接管理、数据模型定义、迁移管理等功能。

## 功能特性

- **异步数据库操作**: 基于asyncpg的全异步数据库操作
- **连接池管理**: 自动管理数据库连接池，支持连接重用和健康检查
- **ORM模型**: 提供基础模型类，支持CRUD操作和数据验证
- **迁移管理**: 版本化的数据库迁移管理，支持自动发现和执行
- **数据验证**: 完整的数据验证框架，支持自定义验证器
- **事务管理**: 支持自动和手动事务控制
- **异常处理**: 完善的异常处理机制

## 模块结构

```
database/
├── __init__.py                 # 模块入口
├── README.md                   # 模块文档
├── config/                     # 配置管理
│   ├── __init__.py
│   ├── database.py            # 数据库配置
│   └── settings.py            # 环境变量管理
├── connection/                 # 连接管理
│   ├── __init__.py
│   ├── connection_pool.py     # 连接池
│   └── session_manager.py     # 会话管理
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── base.py                # 基础模型
│   ├── user.py                # 用户模型
│   ├── mcp_tool_call.py       # MCP工具调用模型
│   ├── call_log.py            # 调用日志模型
│   ├── emotion_analysis.py    # 情绪分析模型
│   ├── emotion_support.py     # 情绪支持模型
│   └── user_record.py         # 用户记录模型
├── migrations/                 # 数据库迁移
│   ├── __init__.py
│   ├── migration_manager.py   # 迁移管理器
│   ├── 001_create_users_table.py
│   ├── 002_create_mcp_tool_calls_table.py
│   └── 003_create_remaining_tables.py
└── utils/                      # 工具函数
    ├── __init__.py
    ├── exceptions.py          # 自定义异常
    ├── validators.py          # 数据验证器
    └── helpers.py             # 辅助函数
```

## 数据表设计

### 用户表 (users)
- 支持访客和微信授权登录两种模式
- 包含用户基本信息、状态管理
- 设计了约束条件确保数据一致性

### MCP工具调用表 (mcp_tool_calls)
- 记录每次MCP工具调用的详细信息
- 包含输入参数、输出结果、执行状态等
- 支持执行时间统计

### 调用日志表 (call_logs)
- 记录工具调用的日志信息
- 支持不同日志级别和元数据存储

### 情绪阈值分析表 (emotion_analysis)
- 存储用户情绪分析结果
- 支持版本化的分析算法

### 情绪支持服务表 (emotion_support)
- 记录情绪支持服务的交互数据
- 包含满意度评分系统

### 用户记录存储表 (user_records)
- 存储用户的文本内容和相关信息
- 支持软删除和标签系统

## 环境配置

在项目根目录创建 `.env` 文件：

```env
# PostgreSQL 数据库连接配置
PG_HOST=localhost
PG_PORT=5433
PG_DATABASE=mcp_db_local
PG_USERNAME=mcp_db_local
PG_PASSWORD=123456

# 数据库连接池配置
PG_POOL_MIN_SIZE=5
PG_POOL_MAX_SIZE=20

# 应用配置
DEBUG=True
LOG_LEVEL=INFO
```

## 使用示例

### 基本使用

```python
import asyncio
from database import DatabaseManager, User

async def main():
    # 初始化数据库管理器
    db = DatabaseManager()
    
    # 初始化连接池
    await db.initialize()
    
    # 执行数据库迁移
    await db.migrate()
    
    # 使用数据库会话
    async with db.get_session() as session:
        # 创建用户
        user = await User.create_guest_user(session, "测试用户")
        print(f"创建用户: {user.id}")
        
        # 查询用户
        found_user = await User.find_by_id(session, user.id)
        print(f"查询用户: {found_user.nickname}")
        
        # 更新用户
        found_user.nickname = "更新后的用户"
        await found_user.save(session)
        
    # 关闭数据库连接
    await db.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### 事务使用

```python
async def create_user_with_record():
    db = DatabaseManager()
    await db.initialize()
    
    async with db.session_manager.transaction() as session:
        # 在事务中创建用户和记录
        user = await User.create_guest_user(session, "事务用户")
        
        record = UserRecord(
            user_id=user.id,
            content="这是一条测试记录",
            content_type="text"
        )
        await record.save(session)
        
        # 如果出现异常，事务会自动回滚
        
    await db.close()
```

### 自定义查询

```python
async def custom_query_example():
    db = DatabaseManager()
    await db.initialize()
    
    async with db.get_session() as session:
        # 执行自定义查询
        result = await session.fetch("""
            SELECT u.nickname, COUNT(mcp.id) as call_count
            FROM users u
            LEFT JOIN mcp_tool_calls mcp ON u.id = mcp.user_id
            WHERE u.status = $1
            GROUP BY u.id, u.nickname
            ORDER BY call_count DESC
            LIMIT 10
        """, "active")
        
        for row in result:
            print(f"用户: {row['nickname']}, 调用次数: {row['call_count']}")
    
    await db.close()
```

## 依赖要求

需要安装以下Python包：

```bash
pip install asyncpg  # PostgreSQL异步驱动
```

## 注意事项

1. **环境变量**: 确保正确配置数据库连接信息
2. **异步操作**: 所有数据库操作都是异步的，需要使用await
3. **连接池**: 连接池会自动管理，无需手动管理连接
4. **迁移**: 首次使用时会自动执行迁移创建表结构
5. **异常处理**: 注意捕获和处理数据库相关异常

## 扩展开发

### 添加新的数据模型

1. 在 `models/` 目录下创建新的模型文件
2. 继承 `BaseModel` 类
3. 定义表名和字段验证器
4. 创建对应的迁移文件

### 添加新的迁移

1. 在 `migrations/` 目录下创建新的迁移文件
2. 文件名格式: `{version}_{description}.py`
3. 实现 `up()` 和 `down()` 函数
4. 运行迁移: `await db.migrate()`

### 自定义验证器

```python
from database.utils.validators import BaseValidator
from database.utils.exceptions import ValidationError

class CustomValidator(BaseValidator):
    def validate(self, value, field_name="field"):
        if not self.is_valid(value):
            raise ValidationError(field_name, "验证失败", value)
        return True
```

这个数据库模块为关系分析系统提供了完整的数据持久化解决方案，具有良好的扩展性和维护性。 
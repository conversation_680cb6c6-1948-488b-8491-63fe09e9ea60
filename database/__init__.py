"""
数据库管理模块

这个模块提供了完整的数据库管理功能，包括：
- 数据库连接和连接池管理
- 数据模型定义和ORM操作
- 数据库迁移管理
- 数据验证和异常处理

主要组件：
- DatabaseManager: 数据库管理器，负责连接池和会话管理
- BaseModel: 基础模型类，提供通用的CRUD操作
- 各种数据模型: User, McpToolCall, CallLog等
- MigrationManager: 迁移管理器
"""

from .config.database import DatabaseConfig
from .connection.connection_pool import ConnectionPool
from .connection.session_manager import SessionManager
from .models.base import BaseModel
from .models.user import User
from .models.mcp_tool_call import McpToolCall
from .models.call_log import CallLog
from .models.emotion_analysis import EmotionAnalysis
from .models.emotion_support import EmotionSupport
from .models.user_record import UserRecord
from .migrations.migration_manager import MigrationManager
from .utils.exceptions import DatabaseError, ValidationError

__version__ = "1.0.0"

class DatabaseManager:
    """
    数据库管理器
    
    负责管理数据库连接池、会话和迁移等核心功能
    提供统一的数据库操作接口
    """
    
    def __init__(self, config: DatabaseConfig = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置，如果为None则使用默认配置
        """
        self.config = config or DatabaseConfig()
        self.connection_pool = ConnectionPool(self.config)
        self.session_manager = SessionManager(self.connection_pool)
        self.migration_manager = MigrationManager(self.connection_pool)
        
    async def initialize(self):
        """
        初始化数据库管理器
        
        创建连接池并执行必要的初始化操作
        """
        await self.connection_pool.initialize()
        
    async def migrate(self, target_version: str = None):
        """
        执行数据库迁移
        
        Args:
            target_version: 目标版本号，如果为None则迁移到最新版本
        """
        await self.migration_manager.migrate(target_version)
        
    async def close(self):
        """
        关闭数据库管理器
        
        清理连接池和相关资源
        """
        await self.connection_pool.close()
        
    def get_session(self):
        """
        获取数据库会话
        
        Returns:
            DatabaseSession: 数据库会话对象
        """
        return self.session_manager.get_session()
    
    async def reset_database(self):
        """
        重置数据库
        
        删除所有表并重新创建
        """
        await self.migration_manager.reset_database()

# 导出主要类和函数
__all__ = [
    'DatabaseManager',
    'DatabaseConfig',
    'BaseModel',
    'User',
    'McpToolCall', 
    'CallLog',
    'EmotionAnalysis',
    'EmotionSupport',
    'UserRecord',
    'MigrationManager',
    'DatabaseError',
    'ValidationError'
] 
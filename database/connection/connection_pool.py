"""
数据库连接池管理

使用asyncpg实现PostgreSQL连接池管理
提供连接的获取、释放和健康检查功能
"""

import asyncio
import logging
from typing import Optional
import asyncpg
from contextlib import asynccontextmanager

from ..config.database import DatabaseConfig
from ..utils.exceptions import ConnectionError

# 配置日志
logger = logging.getLogger(__name__)

class ConnectionPool:
    """
    数据库连接池管理器
    
    基于asyncpg实现的异步PostgreSQL连接池
    支持连接重用、自动重连和健康检查
    """
    
    def __init__(self, config: DatabaseConfig):
        """
        初始化连接池
        
        Args:
            config: 数据库配置对象
        """
        self.config = config
        self._pool: Optional[asyncpg.Pool] = None
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """
        初始化连接池
        
        创建asyncpg连接池并进行健康检查
        
        Raises:
            ConnectionError: 连接池初始化失败
        """
        if self._initialized:
            return
        
        async with self._lock:
            if self._initialized:
                return
            
            try:
                # 验证配置
                self.config.validate()
                
                # 获取连接参数
                conn_params = self.config.get_asyncpg_params()
                
                logger.info(f"正在初始化数据库连接池: {self.config.host}:{self.config.port}/{self.config.database}")
                
                # 创建连接池
                self._pool = await asyncpg.create_pool(**conn_params)
                
                # 进行健康检查
                await self.health_check()
                
                self._initialized = True
                logger.info(f"数据库连接池初始化成功，连接数: {self.config.pool_min_size}-{self.config.pool_max_size}")
                
            except Exception as e:
                logger.error(f"数据库连接池初始化失败: {str(e)}")
                if self._pool:
                    await self._pool.close()
                    self._pool = None
                raise ConnectionError("数据库连接池初始化失败", e)
    
    async def close(self):
        """
        关闭连接池
        
        释放所有连接并清理资源
        """
        if not self._initialized or not self._pool:
            return
        
        async with self._lock:
            if self._pool:
                logger.info("正在关闭数据库连接池...")
                await self._pool.close()
                self._pool = None
                self._initialized = False
                logger.info("数据库连接池已关闭")
    
    async def health_check(self) -> bool:
        """
        连接池健康检查
        
        Returns:
            bool: 连接池是否健康
            
        Raises:
            ConnectionError: 健康检查失败
        """
        if not self._pool:
            raise ConnectionError("连接池未初始化")
        
        try:
            async with self._pool.acquire() as conn:
                # 执行简单查询测试连接
                result = await conn.fetchval("SELECT 1")
                if result != 1:
                    raise ConnectionError("数据库连接测试失败")
                
                # 检查数据库版本
                version = await conn.fetchval("SELECT version()")
                logger.info(f"数据库版本: {version}")
                
            return True
            
        except Exception as e:
            logger.error(f"数据库连接健康检查失败: {str(e)}")
            raise ConnectionError("数据库连接健康检查失败", e)
    
    @asynccontextmanager
    async def acquire(self):
        """
        获取数据库连接的上下文管理器
        
        使用async with语句自动管理连接的获取和释放
        
        Yields:
            asyncpg.Connection: 数据库连接对象
            
        Raises:
            ConnectionError: 获取连接失败
        """
        if not self._initialized or not self._pool:
            await self.initialize()
        
        try:
            async with self._pool.acquire() as connection:
                yield connection
        except Exception as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            raise ConnectionError("获取数据库连接失败", e)
    
    async def execute(self, query: str, *args) -> str:
        """
        执行SQL命令（INSERT, UPDATE, DELETE等）
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            str: 执行结果状态
            
        Raises:
            ConnectionError: 执行失败
        """
        try:
            async with self.acquire() as conn:
                result = await conn.execute(query, *args)
                return result
        except Exception as e:
            logger.error(f"执行SQL命令失败: {query[:100]}... - {str(e)}")
            raise ConnectionError(f"执行SQL命令失败: {str(e)}", e)
    
    async def fetch(self, query: str, *args) -> list:
        """
        执行查询并返回所有结果
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            list: 查询结果列表
            
        Raises:
            ConnectionError: 查询失败
        """
        try:
            async with self.acquire() as conn:
                result = await conn.fetch(query, *args)
                return result
        except Exception as e:
            logger.error(f"执行SQL查询失败: {query[:100]}... - {str(e)}")
            raise ConnectionError(f"执行SQL查询失败: {str(e)}", e)
    
    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """
        执行查询并返回第一行结果
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            Optional[asyncpg.Record]: 查询结果记录，没有结果时返回None
            
        Raises:
            ConnectionError: 查询失败
        """
        try:
            async with self.acquire() as conn:
                result = await conn.fetchrow(query, *args)
                return result
        except Exception as e:
            logger.error(f"执行SQL查询失败: {query[:100]}... - {str(e)}")
            raise ConnectionError(f"执行SQL查询失败: {str(e)}", e)
    
    async def fetchval(self, query: str, *args, column: int = 0):
        """
        执行查询并返回第一行第一列的值
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            column: 列索引，默认为0（第一列）
            
        Returns:
            Any: 查询结果值
            
        Raises:
            ConnectionError: 查询失败
        """
        try:
            async with self.acquire() as conn:
                result = await conn.fetchval(query, *args, column=column)
                return result
        except Exception as e:
            logger.error(f"执行SQL查询失败: {query[:100]}... - {str(e)}")
            raise ConnectionError(f"执行SQL查询失败: {str(e)}", e)
    
    def get_pool_status(self) -> dict:
        """
        获取连接池状态信息
        
        Returns:
            dict: 连接池状态信息
        """
        if not self._pool:
            return {
                'initialized': False,
                'size': 0,
                'free_size': 0,
                'min_size': self.config.pool_min_size,
                'max_size': self.config.pool_max_size
            }
        
        return {
            'initialized': self._initialized,
            'size': self._pool.get_size(),
            'free_size': self._pool.get_idle_size(),
            'min_size': self.config.pool_min_size,
            'max_size': self.config.pool_max_size
        }
    
    def __str__(self) -> str:
        """
        返回连接池的字符串表示
        
        Returns:
            str: 连接池信息
        """
        status = self.get_pool_status()
        return (
            f"ConnectionPool(host={self.config.host}, "
            f"database={self.config.database}, "
            f"initialized={status['initialized']}, "
            f"size={status['size']}/{status['max_size']})"
        ) 
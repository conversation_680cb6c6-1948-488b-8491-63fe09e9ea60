"""
数据库会话管理

提供数据库会话和事务管理功能
支持自动提交、手动事务控制和异常回滚
"""

import asyncio
import logging
from typing import Optional, Any
from contextlib import asynccontextmanager
import asyncpg

from .connection_pool import ConnectionPool
from ..utils.exceptions import TransactionError, ConnectionError

# 配置日志
logger = logging.getLogger(__name__)

class DatabaseSession:
    """
    数据库会话类
    
    封装数据库连接和事务管理
    提供统一的数据库操作接口
    """
    
    def __init__(self, connection: asyncpg.Connection, auto_commit: bool = True):
        """
        初始化数据库会话
        
        Args:
            connection: 数据库连接对象
            auto_commit: 是否自动提交，默认为True
        """
        self.connection = connection
        self.auto_commit = auto_commit
        self._transaction: Optional[asyncpg.Transaction] = None
        self._closed = False
    
    async def execute(self, query: str, *args) -> str:
        """
        执行SQL命令
        
        Args:
            query: SQL语句
            *args: 查询参数
            
        Returns:
            str: 执行结果状态
            
        Raises:
            TransactionError: 执行失败
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        try:
            result = await self.connection.execute(query, *args)
            logger.debug(f"执行SQL: {query[:100]}... - 结果: {result}")
            return result
        except Exception as e:
            logger.error(f"SQL执行失败: {query[:100]}... - 错误: {str(e)}")
            raise TransactionError(f"SQL执行失败: {str(e)}", e)
    
    async def fetch(self, query: str, *args) -> list:
        """
        执行查询并返回所有结果
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            list: 查询结果列表
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        try:
            result = await self.connection.fetch(query, *args)
            logger.debug(f"查询SQL: {query[:100]}... - 返回 {len(result)} 条记录")
            return result
        except Exception as e:
            logger.error(f"SQL查询失败: {query[:100]}... - 错误: {str(e)}")
            raise TransactionError(f"SQL查询失败: {str(e)}", e)
    
    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """
        执行查询并返回第一行结果
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            Optional[asyncpg.Record]: 查询结果记录
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        try:
            result = await self.connection.fetchrow(query, *args)
            logger.debug(f"查询SQL: {query[:100]}... - 返回记录: {'有' if result else '无'}")
            return result
        except Exception as e:
            logger.error(f"SQL查询失败: {query[:100]}... - 错误: {str(e)}")
            raise TransactionError(f"SQL查询失败: {str(e)}", e)
    
    async def fetchval(self, query: str, *args, column: int = 0) -> Any:
        """
        执行查询并返回第一行第一列的值
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            column: 列索引
            
        Returns:
            Any: 查询结果值
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        try:
            result = await self.connection.fetchval(query, *args, column=column)
            logger.debug(f"查询SQL: {query[:100]}... - 返回值: {result}")
            return result
        except Exception as e:
            logger.error(f"SQL查询失败: {query[:100]}... - 错误: {str(e)}")
            raise TransactionError(f"SQL查询失败: {str(e)}", e)
    
    async def begin_transaction(self):
        """
        开始事务
        
        Raises:
            TransactionError: 事务已存在或开始失败
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        if self._transaction:
            raise TransactionError("事务已存在")
        
        try:
            self._transaction = self.connection.transaction()
            await self._transaction.start()
            logger.debug("事务已开始")
        except Exception as e:
            logger.error(f"开始事务失败: {str(e)}")
            self._transaction = None
            raise TransactionError("开始事务失败", e)
    
    async def commit(self):
        """
        提交事务
        
        Raises:
            TransactionError: 无事务或提交失败
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        if not self._transaction:
            raise TransactionError("没有活跃的事务")
        
        try:
            await self._transaction.commit()
            logger.debug("事务已提交")
        except Exception as e:
            logger.error(f"提交事务失败: {str(e)}")
            raise TransactionError("提交事务失败", e)
        finally:
            self._transaction = None
    
    async def rollback(self):
        """
        回滚事务
        
        Raises:
            TransactionError: 无事务或回滚失败
        """
        if self._closed:
            raise TransactionError("会话已关闭")
        
        if not self._transaction:
            raise TransactionError("没有活跃的事务")
        
        try:
            await self._transaction.rollback()
            logger.debug("事务已回滚")
        except Exception as e:
            logger.error(f"回滚事务失败: {str(e)}")
            raise TransactionError("回滚事务失败", e)
        finally:
            self._transaction = None
    
    def is_in_transaction(self) -> bool:
        """
        检查是否在事务中
        
        Returns:
            bool: 是否在事务中
        """
        return self._transaction is not None
    
    def close(self):
        """
        关闭会话
        
        注意：这不会关闭底层连接，只是标记会话为关闭状态
        """
        self._closed = True
        if self._transaction:
            # 异步回滚在这里不太合适，应该在上下文管理器中处理
            logger.warning("会话关闭时仍有未完成的事务")
    
    def is_closed(self) -> bool:
        """
        检查会话是否已关闭
        
        Returns:
            bool: 会话是否已关闭
        """
        return self._closed


class SessionManager:
    """
    会话管理器
    
    负责创建和管理数据库会话
    提供事务上下文管理器
    """
    
    def __init__(self, connection_pool: ConnectionPool):
        """
        初始化会话管理器
        
        Args:
            connection_pool: 连接池对象
        """
        self.connection_pool = connection_pool
    
    @asynccontextmanager
    async def get_session(self, auto_commit: bool = True):
        """
        获取数据库会话的上下文管理器
        
        Args:
            auto_commit: 是否自动提交
            
        Yields:
            DatabaseSession: 数据库会话对象
        """
        async with self.connection_pool.acquire() as connection:
            session = DatabaseSession(connection, auto_commit)
            try:
                yield session
            except Exception as e:
                # 如果有事务且发生异常，自动回滚
                if session.is_in_transaction():
                    try:
                        await session.rollback()
                        logger.info("异常发生，事务已自动回滚")
                    except Exception as rollback_error:
                        logger.error(f"自动回滚失败: {str(rollback_error)}")
                raise e
            finally:
                session.close()
    
    @asynccontextmanager
    async def transaction(self):
        """
        事务上下文管理器
        
        自动开始事务，成功时提交，异常时回滚
        
        Yields:
            DatabaseSession: 数据库会话对象
        """
        async with self.get_session(auto_commit=False) as session:
            await session.begin_transaction()
            try:
                yield session
                await session.commit()
                logger.debug("事务成功提交")
            except Exception as e:
                await session.rollback()
                logger.info(f"事务因异常回滚: {str(e)}")
                raise e
    
    async def execute_in_transaction(self, operations):
        """
        在事务中执行多个操作
        
        Args:
            operations: 异步函数，接受session参数
            
        Returns:
            Any: operations函数的返回值
        """
        async with self.transaction() as session:
            return await operations(session) 
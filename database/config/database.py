"""
数据库配置管理

负责管理PostgreSQL数据库连接配置
包括连接参数、连接池设置等
"""

from dataclasses import dataclass
from typing import Optional
from .settings import settings

@dataclass
class DatabaseConfig:
    """
    数据库配置类
    
    包含PostgreSQL数据库连接所需的所有参数
    支持从环境变量自动加载配置
    """
    
    # 数据库连接参数
    host: str = "localhost"
    port: int = 5432
    database: str = "postgres"
    username: str = "postgres"
    password: str = ""
    
    # 连接池配置
    pool_min_size: int = 5
    pool_max_size: int = 20
    pool_max_queries: int = 50000
    pool_max_inactive_connection_lifetime: float = 300.0
    
    # 连接超时配置
    connect_timeout: float = 60.0
    command_timeout: float = 60.0
    
    # SSL配置
    ssl_mode: str = "prefer"  # disable, allow, prefer, require, verify-ca, verify-full
    
    def __post_init__(self):
        """
        初始化后处理，从环境变量加载配置
        """
        self._load_from_env()
    
    def _load_from_env(self):
        """
        从环境变量加载数据库配置
        
        读取以PG_开头的环境变量来覆盖默认配置
        """
        # 数据库连接参数
        self.host = settings.get('PG_HOST', self.host)
        self.port = settings.get('PG_PORT', self.port, int)
        self.database = settings.get('PG_DATABASE', self.database)
        self.username = settings.get('PG_USERNAME', self.username)
        self.password = settings.get('PG_PASSWORD', self.password)
        
        # 连接池配置
        self.pool_min_size = settings.get('PG_POOL_MIN_SIZE', self.pool_min_size, int)
        self.pool_max_size = settings.get('PG_POOL_MAX_SIZE', self.pool_max_size, int)
        self.pool_max_queries = settings.get('PG_POOL_MAX_QUERIES', self.pool_max_queries, int)
        self.pool_max_inactive_connection_lifetime = settings.get(
            'PG_POOL_MAX_INACTIVE_CONNECTION_LIFETIME', 
            self.pool_max_inactive_connection_lifetime, 
            float
        )
        
        # 连接超时配置
        self.connect_timeout = settings.get('PG_CONNECT_TIMEOUT', self.connect_timeout, float)
        self.command_timeout = settings.get('PG_COMMAND_TIMEOUT', self.command_timeout, float)
        
        # SSL配置
        self.ssl_mode = settings.get('PG_SSL_MODE', self.ssl_mode)
    
    def get_dsn(self) -> str:
        """
        获取数据库连接字符串 (DSN)
        
        Returns:
            str: PostgreSQL连接字符串
        """
        return (
            f"postgresql://{self.username}:{self.password}@"
            f"{self.host}:{self.port}/{self.database}"
            f"?sslmode={self.ssl_mode}"
        )
    
    def get_asyncpg_params(self) -> dict:
        """
        获取asyncpg连接参数
        
        Returns:
            dict: asyncpg.create_pool所需的参数字典
        """
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.username,
            'password': self.password,
            'min_size': self.pool_min_size,
            'max_size': self.pool_max_size,
            'max_queries': self.pool_max_queries,
            'max_inactive_connection_lifetime': self.pool_max_inactive_connection_lifetime,
            'timeout': self.connect_timeout,
            'command_timeout': self.command_timeout,
        }
    
    def validate(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
            
        Raises:
            ValueError: 配置无效时抛出异常
        """
        if not self.host:
            raise ValueError("数据库主机地址不能为空")
        
        if not self.database:
            raise ValueError("数据库名称不能为空")
        
        if not self.username:
            raise ValueError("数据库用户名不能为空")
        
        if self.port <= 0 or self.port > 65535:
            raise ValueError("数据库端口号必须在1-65535之间")
        
        if self.pool_min_size <= 0:
            raise ValueError("连接池最小连接数必须大于0")
        
        if self.pool_max_size <= self.pool_min_size:
            raise ValueError("连接池最大连接数必须大于最小连接数")
        
        if self.ssl_mode not in ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full']:
            raise ValueError("SSL模式无效")
        
        return True
    
    def __str__(self) -> str:
        """
        返回配置的字符串表示（隐藏密码）
        
        Returns:
            str: 配置信息字符串
        """
        return (
            f"DatabaseConfig(host='{self.host}', port={self.port}, "
            f"database='{self.database}', username='{self.username}', "
            f"password='***', pool_size={self.pool_min_size}-{self.pool_max_size})"
        ) 
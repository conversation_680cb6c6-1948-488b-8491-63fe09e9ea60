"""
环境变量设置管理

负责从环境变量和.env文件中加载配置信息
提供默认值和类型转换功能
"""

import os
from typing import Optional, Any
from pathlib import Path

class Settings:
    """
    环境变量设置管理器
    
    从环境变量和.env文件中加载配置，支持默认值和类型转换
    """
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化设置管理器
        
        Args:
            env_file: .env文件路径，默认为项目根目录下的.env文件
        """
        self.env_file = env_file or self._find_env_file()
        self._load_env_file()
    
    def _find_env_file(self) -> Optional[str]:
        """
        查找.env文件
        
        从当前目录向上查找.env文件
        
        Returns:
            str: .env文件路径，如果未找到返回None
        """
        current_dir = Path.cwd()
        for path in [current_dir] + list(current_dir.parents):
            env_path = path / '.env'
            if env_path.exists():
                return str(env_path)
        return None
    
    def _load_env_file(self):
        """
        加载.env文件
        
        将.env文件中的环境变量加载到os.environ中
        """
        if not self.env_file or not os.path.exists(self.env_file):
            return
            
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        # 移除引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        os.environ[key] = value
        except Exception as e:
            print(f"警告: 无法加载.env文件 {self.env_file}: {e}")
    
    def get(self, key: str, default: Any = None, cast_type: type = str) -> Any:
        """
        获取环境变量值
        
        Args:
            key: 环境变量名
            default: 默认值
            cast_type: 类型转换，支持str, int, float, bool
            
        Returns:
            Any: 转换后的值
        """
        value = os.getenv(key, default)
        
        if value is None:
            return default
            
        if cast_type == str:
            return str(value)
        elif cast_type == int:
            try:
                return int(value)
            except (ValueError, TypeError):
                return default
        elif cast_type == float:
            try:
                return float(value)
            except (ValueError, TypeError):
                return default
        elif cast_type == bool:
            if isinstance(value, bool):
                return value
            return str(value).lower() in ('true', '1', 'yes', 'on')
        else:
            return value
    
    def get_required(self, key: str, cast_type: type = str) -> Any:
        """
        获取必需的环境变量值
        
        Args:
            key: 环境变量名
            cast_type: 类型转换
            
        Returns:
            Any: 转换后的值
            
        Raises:
            ValueError: 如果环境变量不存在
        """
        value = self.get(key, None, cast_type)
        if value is None:
            raise ValueError(f"必需的环境变量 {key} 未设置")
        return value

# 全局设置实例
settings = Settings() 
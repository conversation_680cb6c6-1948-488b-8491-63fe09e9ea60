server {
    listen 80;
    server_name common.feeling.ltd;

    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name common.feeling.ltd;

    ssl_certificate     /etc/nginx/ssl/feeling.ltd.pem;
    ssl_certificate_key /etc/nginx/ssl/feeling.ltd.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://127.0.0.1:8010;  
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    access_log /var/log/nginx/common.feeling.ltd-access.log;
    error_log /var/log/nginx/common.feeling.ltd-error.log;
}
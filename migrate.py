#!/usr/bin/env python3
"""
数据库迁移执行脚本

提供命令行接口来执行数据库迁移、查看状态等操作
支持多种命令和参数，方便开发和部署使用

使用方法:
    python migrate.py migrate              # 执行所有待处理的迁移
    python migrate.py migrate --target=003 # 迁移到指定版本
    python migrate.py status               # 查看迁移状态
    python migrate.py --help               # 显示帮助信息
"""

import asyncio
import argparse
import logging
import sys
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from database import DatabaseManager
from database.utils.exceptions import DatabaseError, MigrationError

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class MigrationScript:
    """
    数据库迁移脚本类
    
    封装迁移相关的操作，提供命令行接口
    """
    
    def __init__(self):
        """初始化迁移脚本"""
        self.db_manager: Optional[DatabaseManager] = None
    
    async def initialize(self):
        """
        初始化数据库管理器
        
        创建数据库连接并进行健康检查
        
        Raises:
            DatabaseError: 数据库连接失败
        """
        try:
            logger.info("正在初始化数据库连接...")
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            logger.info("数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {str(e)}")
            raise DatabaseError("数据库连接初始化失败", e)
    
    async def cleanup(self):
        """
        清理资源
        
        关闭数据库连接池
        """
        if self.db_manager:
            await self.db_manager.close()
            logger.info("数据库连接已关闭")
    
    async def execute_migrate(self, target_version: Optional[str] = None):
        """
        执行数据库迁移
        
        Args:
            target_version: 目标版本号，None表示迁移到最新版本
        """
        try:
            logger.info("=" * 50)
            logger.info("开始执行数据库迁移")
            logger.info("=" * 50)
            
            start_time = time.time()
            
            # 获取迁移状态
            migration_status = await self.db_manager.migration_manager.get_migration_status()
            
            if 'error' in migration_status:
                logger.error(f"获取迁移状态失败: {migration_status['error']}")
                return False
            
            logger.info(f"当前状态:")
            logger.info(f"  已执行迁移: {migration_status['executed_count']}")
            logger.info(f"  可用迁移: {migration_status['available_count']}")
            logger.info(f"  待处理迁移: {migration_status['pending_count']}")
            
            if migration_status['pending_count'] == 0:
                logger.info("没有需要执行的迁移")
                return True
            
            logger.info(f"待执行的迁移文件:")
            for migration in migration_status['pending_migrations']:
                logger.info(f"  - {migration}")
            
            # 执行迁移
            if target_version:
                logger.info(f"迁移到目标版本: {target_version}")
            else:
                logger.info("迁移到最新版本")
            
            executed_count = await self.db_manager.migrate(target_version)
            
            execution_time = time.time() - start_time
            logger.info("=" * 50)
            logger.info("迁移执行完成")
            logger.info(f"执行了 {executed_count} 个迁移")
            logger.info(f"总耗时: {execution_time:.2f} 秒")
            logger.info("=" * 50)
            
            return True
            
        except MigrationError as e:
            logger.error(f"迁移执行失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return False
    
    async def show_status(self):
        """
        显示迁移状态信息
        
        查询并显示当前的迁移状态
        """
        try:
            logger.info("=" * 50)
            logger.info("数据库迁移状态")
            logger.info("=" * 50)
            
            # 获取迁移状态
            migration_status = await self.db_manager.migration_manager.get_migration_status()
            
            if 'error' in migration_status:
                logger.error(f"获取迁移状态失败: {migration_status['error']}")
                return False
            
            # 显示总体状态
            logger.info(f"迁移统计:")
            logger.info(f"  已执行迁移数量: {migration_status['executed_count']}")
            logger.info(f"  可用迁移数量: {migration_status['available_count']}")
            logger.info(f"  待处理迁移数量: {migration_status['pending_count']}")
            
            # 显示已执行的迁移
            if migration_status['executed_migrations']:
                logger.info(f"\n已执行的迁移版本:")
                for version in migration_status['executed_migrations']:
                    logger.info(f"  ✓ {version}")
            
            # 显示待处理的迁移
            if migration_status['pending_migrations']:
                logger.info(f"\n待处理的迁移:")
                for migration in migration_status['pending_migrations']:
                    logger.info(f"  ○ {migration}")
            else:
                logger.info(f"\n✓ 所有迁移都已执行完成")
            
            # 获取连接池状态
            pool_status = self.db_manager.connection_pool.get_pool_status()
            logger.info(f"\n数据库连接状态:")
            logger.info(f"  连接池初始化: {'是' if pool_status['initialized'] else '否'}")
            logger.info(f"  当前连接数: {pool_status['size']}")
            logger.info(f"  空闲连接数: {pool_status['free_size']}")
            logger.info(f"  连接池范围: {pool_status['min_size']}-{pool_status['max_size']}")
            
            logger.info("=" * 50)
            return True
            
        except Exception as e:
            logger.error(f"获取状态信息失败: {str(e)}")
            return False
        
    async def reset_database(self):
        """
        重置数据库
        
        删除所有表并重新创建
        """
        try:
            logger.info("正在重置数据库...")
            
            # 获取迁移状态
            migration_status = await self.db_manager.migration_manager.get_migration_status()
            
            if 'error' in migration_status:
                logger.error(f"获取迁移状态失败: {migration_status['error']}")
                return False
            
            # 删除所有表
            await self.db_manager.reset_database()
            
            logger.info("数据库重置完成")

            return True
            
        except Exception as e:
            logger.error(f"数据库重置失败: {str(e)}")
            return False
    
    async def test_connection(self):
        """
        测试数据库连接
        
        验证数据库连接是否正常
        """
        try:
            logger.info("正在测试数据库连接...")
            
            # 执行健康检查
            await self.db_manager.connection_pool.health_check()
            
            # 执行简单查询
            result = await self.db_manager.connection_pool.fetchval("SELECT CURRENT_TIMESTAMP")
            
            logger.info(f"数据库连接正常")
            logger.info(f"当前数据库时间: {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return False

def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description="数据库迁移管理脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python migrate.py migrate              # 执行所有待处理的迁移
  python migrate.py migrate --target=003 # 迁移到指定版本
  python migrate.py status               # 查看迁移状态
  python migrate.py test                 # 测试数据库连接
  python migrate.py reset                # 重置数据库
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # migrate 命令
    migrate_parser = subparsers.add_parser('migrate', help='执行数据库迁移')
    migrate_parser.add_argument(
        '--target',
        type=str,
        help='目标迁移版本号 (例: 003)'
    )
    
    # status 命令
    subparsers.add_parser('status', help='显示迁移状态')
    
    # test 命令
    subparsers.add_parser('test', help='测试数据库连接')
    
    # reset 命令
    subparsers.add_parser('reset', help='重置数据库')
    
    # 全局参数
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，只显示错误'
    )
    
    return parser

async def main():
    """
    主函数
    
    解析命令行参数并执行相应操作
    """
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 配置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    
    # 检查命令
    if not args.command:
        parser.print_help()
        return 1
    
    script = MigrationScript()
    success = False
    
    try:
        # 初始化数据库连接
        await script.initialize()
        
        # 执行相应命令
        if args.command == 'migrate':
            success = await script.execute_migrate(args.target)
        elif args.command == 'status':
            success = await script.show_status()
        elif args.command == 'test':
            success = await script.test_connection()
        elif args.command == 'reset':
            success = await script.reset_database()
        else:
            logger.error(f"未知命令: {args.command}")
            success = False
        
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
        success = False
    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        success = False
    finally:
        # 清理资源
        await script.cleanup()
    
    return 0 if success else 1

if __name__ == "__main__":
    """脚本入口点"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("程序被中断")
        sys.exit(130)
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        sys.exit(1) 